{"name": "@holistics/app", "version": "1.0.0", "private": true, "description": "Holistics main app", "scripts": {"otel-upgrade": "yarn add @opentelemetry/api @opentelemetry/instrumentation @opentelemetry/exporter-trace-otlp-http @opentelemetry/resources @opentelemetry/sdk-trace-web"}, "dependencies": {"@activeadmin/activeadmin": "^2.13.1", "@algolia/autocomplete-js": "^1.7.1", "@algolia/autocomplete-theme-classic": "^1.7.1", "@babel/polyfill": "^7.0.0", "@ckpack/vue-color": "0.0.3", "@google/markerclustererplus": "^5.0.4", "@holistics/aml": "4.6.0-alpha.5", "@holistics/aml-editor": "2.16.0-alpha.5", "@holistics/aml-std": "2.62.0", "@holistics/aml-studio": "1.0.0", "@holistics/amql": "2.5.8", "@holistics/command-palette": "1.0.0", "@holistics/custom-chart": "^0.6.0", "@holistics/custom-chart-template-parser": "^0.2.0", "@holistics/date-parser": "^3.4.0", "@holistics/dbrender": "^1.0.1", "@holistics/dbrenderer": "2.14.3-vue-3-patch-2", "@holistics/design-system": "2.41.2", "@holistics/ds": "1.0.0", "@holistics/feature-toggle": "^0.1.3", "@holistics/floating-vue": "^5.2.2-holistics.3", "@holistics/geojson": "^0.1.2", "@holistics/handsontable": "^6.2.2", "@holistics/icon": "0.13.5", "@holistics/node-tree": "1.0.0", "@holistics/tree-select": "1.0.0", "@holistics/utils": "1.0.0", "@holistics/vue-draggable": "1.0.0", "@holistics/web-storage": "1.0.0", "@opentelemetry/api": "~1.8.0", "@opentelemetry/exporter-trace-otlp-http": "^0.49.1", "@opentelemetry/instrumentation": "^0.49.1", "@opentelemetry/resources": "^1.22.0", "@opentelemetry/sdk-trace-web": "^1.22.0", "@rails/actioncable": "^8.0.0", "@sentry/vue": "^9.22.0", "@vee-validate/i18n": "^4.11.8", "@vee-validate/rules": "^4.5.11", "@vue-leaflet/vue-leaflet": "^0.6.1", "@vueuse/core": "^10.9.0", "@vueuse/integrations": "^11.1.0", "@vueuse/router": "^10.9.0", "ag-grid-community": "33.2.0", "ag-grid-vue3": "33.2.0", "algoliasearch": "^4.14.2", "axios": "^1.7.7", "bignumber.js": "^9.1.2", "canvas-confetti": "^1.5.1", "chroma-js": "^2.4.2", "click-outside-vue3": "^4.0.1", "core-js": "3", "currencyformatter.js": "^2.2.0", "d3-sankey": "^0.12.3", "d3-scale": "^4.0.2", "dexie": "^3.2.4", "fast-json-patch": "^3.1.1", "fast-xml-parser": "^4.5.2", "filesize": "^8.0.7", "fuse.js": "^7.1.0", "geojson-validation": "^1.0.2", "gleap": "^13.5.16", "highcharts": "^11.4.6", "highlight.js": "^11.5.1", "intersection-observer": "^0.12.2", "jest-date-mock": "^1.0.8", "jose": "^4.15.5", "jquery": "^3.6.4", "js-yaml": "^3.14.0", "leaflet": "^1.8.0", "leaflet-heatmap": "^1.0.0", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "lru-cache": "^7.14.0", "luxon": "^3.3.0", "marked": "^4.1.0", "md5": "^2.3.0", "moment": "^2.29.4", "moment-timezone": "^0.5.40", "p-debounce": "^4.0.0", "p-queue": "^6.6.2", "p-throttle": "^7.0.0", "pako": "^2.1.0", "papaparse": "^5.3.2", "portal-vue": "^3.0.0", "qrcode": "^1", "query-string": "^7.1.1", "select2": "4.0.3", "sql-formatter": "https://github.com/holistics/sql-formatter", "string-similarity-js": "^2.1.4", "tiny-emitter": "^2.1.0", "tributejs": "^5.1.3", "typescript": "^5.6.3", "url-search-params-polyfill": "^8.1.1", "uuid": "^8.3.2", "v3-tour": "^3.1.2", "vee-validate": "^4.13.2", "vega": "^5.32.0", "vega-dataflow": "^5.7.6", "vega-embed": "^6.26.0", "vega-lite": "^5.21.0", "vega-util": "^1.17.2", "vue-datepicker-next": "^1.0.2", "vue-observe-visibility": "^2.0.0-alpha.1", "vue-router": "4", "vue-scrollto": "^2.18.2", "vue-slider-component": "^4.0.0-beta.9", "vue-virtual-scroller": "^2.0.0-beta.8", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0", "zxcvbn": "^4.4.2"}, "devDependencies": {"@holistics/types": "0.5.11", "@types/canvas-confetti": "^1.4.3", "@types/d3-sankey": "^0.12.1", "@types/luxon": "^3.3.0", "@types/md5": "^2.3.2", "@types/pako": "^2.0.3", "@types/qs": "^6.9.7", "@types/string-similarity": "^4.0.2", "@vue/runtime-dom": "^3.5.13", "@vue/test-utils": "^2.4.1", "axios-mock-adapter": "^2.1.0", "fake-indexeddb": "^3.1.7", "flush-promises": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "vue-component-type-helpers": "^2.2.8"}, "engines": {"node": "^18.0.0", "yarn": "^1.x.x"}}