# typed: false
# frozen_string_literal: true

shared_context 'dynamic_data_source' do
  before do
    FeatureToggle.toggle_global(AmlStudio::Project::FT_DASHBOARD_V4, true)
    FeatureToggle.toggle_global(AmlCompiledCache::FT_USE_AML_COMPILED_CACHE, true)

    create(:user_attribute, name: 'data_source', attribute_type: 'text')
  end

  include_context 'aml_studio_deployed' do
    let(:isolated_repo) { true }
    let(:deploy_dashboard_v4) { true }
    let(:project_fixture_folder_path) { 'spec/fixtures/aml_repos/dynamic_data_source' }
  end

  let(:user) { get_test_admin }
  let(:work_flow) do
    project.working_repo!(user).work_flow(user)
  end
end
