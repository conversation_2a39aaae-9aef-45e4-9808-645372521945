<template>
  <div
    class="embed-preview-tabs ml-3 flex flex-col"
  >
    <div class="mb-2 text-xs font-medium">
      Embed code
      <HTooltip
        :content="'This code is generated from Embed Settings and will be then used to integrate into your application.'"
        placement="right"
      >
        <h-icon
          name="info"
        />
      </HTooltip>
    </div>
    <h-tabs
      v-model="currentTab"
      class="tabs"
    >
      <h-tab
        v-for="(value, key) in samples"
        :key="key"
        :title="value.name"
        class="tab-content"
      >
        <div class="generating-code">
          <code-highlight
            :code="value.backendCode"
            :type="key"
          />
        </div>
        <div class="btn-copy flex justify-end">
          <HButton
            size="sm"
            icon="type/json"
            icon-size="sm"
            type="secondary-default"
            class="flex items-center"
            @click="copyToClipboard(value.backendCode)"
          >
            {{ copied ? 'Copied' : 'Copy' }}
          </HButton>
          <HButton
            size="sm"
            icon="play"
            icon-size="sm"
            type="primary-highlight"
            class="ci-run-embed-preview ml-2 flex items-center"
            :disabled="disableRun"
            @click="run"
          >
            <h-icon
              v-show="disableRun"
              name="circle-notch"
              class="mr-1"
              size="sm"
              spin
            />
            Run
          </HButton>
        </div>
      </h-tab>
    </h-tabs>
    <div class="iframe-example">
      <div class="py-1 pl-4 font-medium ">
        HTML Iframe
      </div>
      <code-highlight
        :code="currentHTML"
        type="html"
      />
    </div>
    <div class="browser-script-example">
      <div class="py-1 pl-4 font-medium ">
        Browser script (Optional)
      </div>
      <code-highlight
        :code="sampleBrowserScript"
        type="javascript"
      />
    </div>
  </div>
</template>

<script>
import { HTooltip, HButton } from '@holistics/design-system';
import CodeHighlight from '@/vue_components/code_highlight.vue';
import defaultSampleCodes from '@/modules/EmbedLinks/constants/defaultSampleCodes';
import sampleBrowserScript from '@/modules/EmbedLinks/constants/sampleBrowserScript';
import getPattern from '@/modules/EmbedLinks/utils/getPattern';
import { REFRESH_TOKEN_POST_MESSAGE_TYPE } from '@/modules/EmbedLinks/constants/embedToken';
import Utils from '@/es6/utils';
import { cloneDeep, get, keys } from 'lodash';

export default {
  name: 'EmbedPreviewTabs',
  components: {
    HTooltip,
    HButton,
    CodeHighlight,
  },
  props: {
    embedLink: {
      type: Object,
      required: true,
    },
    disableRun: {
      type: Boolean,
      required: true,
    },
    targetUrl: {
      type: String,
      required: true,
    },
    embedBuilder: {
      type: Object,
      required: true,
    },
  },
  emits: ['run'],
  data () {
    return {
      currentTab: 0,
      languages: keys(defaultSampleCodes),
      samples: cloneDeep(defaultSampleCodes),
      copied: false,
      sampleBrowserScript: undefined,
    };
  },
  computed: {
    currentLanguage () {
      return this.languages[this.currentTab];
    },
    currentHTML () {
      return get(this.samples, `${this.currentLanguage}.frontendCode`);
    },
  },
  watch: {
    embedBuilder: {
      handler () {
        this.copied = false;
        this.generate();
      },
    },
    currentTab: {
      handler () {
        this.copied = false;
        this.generate();
      },
    },
  },
  mounted () {
    this.samples = this.languages.reduce((result, key) => {
      result[key] = {
        ...defaultSampleCodes[key],
        backendCode: this.generateBackendCode(defaultSampleCodes[key].backendCode),
        frontendCode: this.generateFrontendCode(defaultSampleCodes[key].frontendCode),
      };
      return result;
    }, {});
    this.sampleBrowserScript = this.generateBrowserScript(sampleBrowserScript);
  },
  methods: {
    generate () {
      const defaultSample = defaultSampleCodes[this.currentLanguage];

      this.samples[this.currentLanguage] = {
        ...this.samples[this.currentLanguage],
        backendCode: this.generateBackendCode(defaultSample.backendCode),
      };
    },
    generateFrontendCode (code) {
      if (!code) {
        return null;
      }
      return code.replace('{{ url }}', this.targetUrl);
    },
    generateBackendCode (code) {
      if (!code) {
        return null;
      }
      const sample = code
        .replace('{{ settings }}', getPattern({ language: this.currentLanguage, value: this.embedBuilder.settings }))
        .replace('{{ filters }}', getPattern({ language: this.currentLanguage, value: this.embedBuilder.filters }))
        .replace('{{ permissions }}', getPattern({ language: this.currentLanguage, value: this.embedBuilder.permissions }))
        .replace('{{ user_attributes }}', getPattern({ language: this.currentLanguage, value: this.embedBuilder.user_attributes }))
        .replace('{{ embed_code }}', this.embedLink.hash_code)
        .replace('{{ secret_key }}', this.embedLink.secret_key);

      return sample;
    },
    generateBrowserScript (code) {
      if (!code) {
        return null;
      }
      return code
        .replace('{{ url }}', this.targetUrl)
        .replace('{{ refresh_token_type }}', REFRESH_TOKEN_POST_MESSAGE_TYPE);
    },
    run () {
      this.$emit('run');
    },
    copyToClipboard (code) {
      Utils.copyToClipboard(code);
      this.copied = true;
    },
  },
};
</script>
<style lang="postcss" scoped>
.embed-preview-tabs {
  overflow: auto;
  .tabs {
    /* Use border/default token */
    @apply border border-gray-300;

    .tab-content {
      position: relative;
      /* Use background/secondary/default token */
      @apply bg-gray-50;

      .btn-copy {
        position: absolute;
        z-index: 10;
        bottom: 12px;
        right: 16px;
      }
    }

    .generating-code {
      height: 300px;
      overflow: auto;
      position: relative;
    }
  }

  .iframe-example, .browser-script-example {
    margin-top: 24px;
    /* Use border/default token */
    @apply border border-gray-300;
    pre {
      height: 135px;
      overflow: auto;
    }
  }
}

pre {
  border: none;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  /* Use border/default token */
  @apply border-gray-300;
  height: 100%;
  word-wrap: break-word;
  overflow-x: auto;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  margin-bottom: 0;
}
</style>
