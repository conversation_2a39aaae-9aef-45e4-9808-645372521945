<template>
  <HTooltip
    :disabled="!disabledReason"
    placement="right"
    @click.prevent.stop="onClick"
  >
    <template #content>
      <div class="text-gray-200">
        <span>{{ disabledReason }}</span>
        <HButton
          v-if="disabledDocUrl"
          type="clear-highlight !text-blue-400"
          :href="disabledDocUrl"
        >
          Learn more.
        </HButton>
      </div>
    </template>
    <div
      class="flex cursor-pointer flex-row items-start justify-between space-x-1 rounded-md p-1 hover:bg-gray-100"
      :class="disabledReason ? '!cursor-not-allowed text-gray-500' : 'active:bg-gray-200'"
    >
      <div class="flex flex-row space-x-1 p-1">
        <HIcon
          v-if="icons"
          :name="icons[0]"
        />
        <span>{{ label }}</span>
      </div>
      <HTooltip
        placement="right"
        :disabled="disabledReason"
      >
        <template #content>
          <div class="text-gray-200">
            <span>This feature is under active development. Have any ideas on how we can improve this?</span>
            <HButton
              type="clear-highlight !text-blue-400"
              @click="jotform('203308784415456', { feature: label }, { height: 850 })"
            >
              Send feedback
            </HButton>
          </div>
        </template>
        <HBadge
          :icon="''"
          size="sm"
          type="info-primary"
          label="Beta"
          as-div
        />
      </HTooltip>
    </div>
  </HTooltip>
</template>
<script setup lang="ts">
import jotform from '@/core/services/jotform';
import {
  HBadge, HIcon, HTooltip, HButton,
} from '@holistics/design-system';

const props = defineProps<{
  disabledReason?: string
  disabledDocUrl?: string
  icons?: any[]
  label?: string
}>();

const emit = defineEmits<{(e: 'click'):void,
}>();

function onClick () {
  if (props.disabledReason) {
    return;
  }

  emit('click');
}
</script>
