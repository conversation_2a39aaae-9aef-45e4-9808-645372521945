Dashboard canvas_dashboard {
  title: 'Canvas dashboard 4.0'
  view: LinearLayout {
  }
  block combination_chart: VizBlock {
    label: 'Sum of Price by Month Created at and Name'
    viz: CombinationChart {
      dataset: ecommerce
      x_axis: VizFieldFull {
        ref: ref('data_modeling_products', 'created_at')
        transformation: 'datetrunc month'
        format {
          type: 'date'
          pattern: 'LLL yyyy'
        }
      }
      legend: VizFieldFull {
        ref: ref('data_modeling_products', 'status')
        format {
          type: 'text'
        }
      }
      y_axis {
        series {
          field: VizFieldFull {
            ref: ref('data_modeling_products', 'price')
            aggregation: 'sum'
            format {
              type: 'number'
              pattern: 'inherited'
            }
          }
          settings {
            color_palette: -2
          }
        }
      }
      settings {
        x_axis_show_null_datetime: false
      }
    }
  }
  block table: VizBlock {
    label: 'Table'
    viz: DataTable {
      dataset: ecommerce
      fields: [
        VizFieldFull {
          ref: ref('data_modeling_products', 'name')
          format {
            type: 'text'
          }
        },
        VizFieldFull {
          ref: ref('data_modeling_products', 'status')
          format {
            type: 'text'
          }
        },
        VizFieldFull {
          ref: ref('data_modeling_products', 'price')
          aggregation: 'sum'
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
      ]
      settings {
        show_row_number: true
      }
    }
  }
  block pivot_table: VizBlock {
    label: 'pivot'
    viz: PivotTable {
      dataset: ecommerce
      rows: [
        VizFieldFull {
          ref: ref('data_modeling_products', 'name')
          format {
            type: 'text'
          }
        }
      ]
      columns: [
        VizFieldFull {
          ref: ref('data_modeling_products', 'status')
          format {
            type: 'text'
          }
        }
      ]
      values: [
        VizFieldFull {
          ref: ref('data_modeling_products', 'price')
          aggregation: 'sum'
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
      ]
      settings {
        show_row_total: true
        show_column_total: true
      }
    }
  }
  block pie_chart: VizBlock {
    label: 'Count of Id by Name'
    viz: PieChart {
      dataset: ecommerce
      legend: VizFieldFull {
        ref: ref('data_modeling_products', 'name')
        format {
          type: 'text'
        }
      }
      series {
        field: VizFieldFull {
          ref: ref('data_modeling_products', 'id')
          aggregation: 'count'
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
        settings {
          color_palette: -2
        }
      }
      series {
        field: VizFieldFull {
          ref: ref('data_modeling_products', 'price')
          aggregation: 'sum'
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
        settings {
          color_palette: -2
        }
      }
    }
  }
  block metric_kpi: VizBlock {
    label: 'metric kpi'
    viz: MetricKpi {
      dataset: ecommerce
      value: VizFieldFull {
        ref: ref('data_modeling_products', 'price')
        aggregation: 'sum'
        format {
          type: 'number'
          pattern: 'inherited'
        }
      }
      settings {
        alignment: 'left'
      }
    }
    settings {
      hide_label: true
    }
  }
  block conversion_funnel: VizBlock {
    label: 'Conversion Funnel'
    viz: ConversionFunnel {
      dataset: ecommerce
      breakdown_by: VizFieldFull {
        ref: ref('data_modeling_products', 'status')
        format {
          type: 'text'
        }
      }
      values: [
        VizFieldFull {
          ref: ref('data_modeling_products', 'price')
          aggregation: 'sum'
          format {
            type: 'number'
            pattern: 'inherited'
          }
        }
      ]
    }
  }
  block p1: PopBlock {
    label: 'pop'
    default {
      type: 'relative'
      duration: 1
      granularity: 'month'
    }
  }
  block bar_chart: VizBlock {
    label: 'Count of Status by Month Created at'
    viz: ColumnChart {
      dataset: ecommerce
      theme {

      }
      x_axis: VizFieldFull {
        ref: ref('data_modeling_products', 'created_at')
        transformation: 'datetrunc month'
        format {
          type: 'date'
          pattern: 'LLL yyyy'
        }
      }
      y_axis {
        series {
          field: VizFieldFull {
            ref: ref('data_modeling_products', 'status')
            aggregation: 'count'
            format {
              type: 'number'
              pattern: 'inherited'
            }
          }
          settings {
            color_palette: -2
          }
        }
      }
      settings {
        x_axis_show_null_datetime: false
      }
    }
  }
  block gauge_chart: VizBlock {
    label: 'Gauge Chart'
    viz: GaugeChart {
      dataset: ecommerce
      theme {

      }
      value: VizFieldFull {
        ref: ref('data_modeling_products', 'id')
        aggregation: 'count'
        format {
          type: 'number'
          pattern: 'inherited'
        }
      }
      max_value: VizFieldFull {
        ref: ref('data_modeling_products', 'price')
        aggregation: 'sum'
        format {
          type: 'number'
          pattern: 'inherited'
        }
      }
    }
  }
  interactions: [
    PopInteraction {
      from: 'p1'
      to: [
        CustomMapping {
          block: 'bar_chart'
          field: ref('data_modeling_products', 'created_at')
        }
      ]
    }
  ]
}