# typed: false
# frozen_string_literal: true

# @file-tag # visualization/execution

require 'rails_helper'

describe VizDataController do
  include_context 'simple_query_model'
  let(:ds) { get_test_ds } # need to touch this in order for model initialization
  let(:rm) { query_data_model }
  let(:tenant) { get_test_tenant }
  let(:explorer) { create :explorer, tenant: tenant }
  let(:admin) { get_test_admin }
  let(:analyst) { get_test_analyst }
  let(:data_set) do
    _data_set = create(:data_set, root_model_id: nil)
    _data_set.data_models << rm
    _data_set.save!
    _data_set
  end

  before do
    FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
    FeatureToggle.toggle_global('data_sets:enabled', true)
    FeatureToggle.toggle_global(DataSet::FT_CREATION_IN_REPORTING, true)
    FeatureToggle.toggle_global(QueryReport::FT_ALLOW_STANDALONE_DATASET, true)
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)
    FeatureToggle.toggle_global(Viz::Data::V3::VizModelTransformers::Pop::FT_POP_ENABLED, true)
    FeatureToggle.toggle_global(::Viz::Constants::FT_TREAT_PLACEHOLDERS_AS_LITERALS, true)
    FeatureToggle.toggle_global(::Viz::Constants::FT_IMPLICIT_PLACEHOLDER_DATA_TYPE, true)
    sign_in admin
  end

  shared_context 'timezone_setup' do
    before do
      FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
      FeatureToggle.toggle_global(::Tenant::FT_NEW_TIMEZONE_CONFIG, true)
      FeatureToggle.toggle_global(::Timezone::Helper::FT_DASHBOARD_TIMEZONE, true)
      FeatureToggle.toggle_global(::Viz::Constants::FT_TABLE_V2, true)
      FeatureToggle.toggle_global(::Viz::Constants::FT_PIVOT_V2, true)
      ds.tenant.settings[:time_zone] = tz
      ds.tenant.save
    end
  end

  shared_context 'simple_query_model_for_date_drill' do
    include_context 'timezone_setup'

    let(:tz) { 'Europe/Copenhagen' }
    let(:query_model_sql) do
      <<~SQL
        SELECT *
          FROM (VALUES
            ('2019-11-01 22:00:00'::timestamp, 10),
            ('2019-11-30 23:00:00'::timestamp, 20),
            ('2019-12-01 22:00:00'::timestamp, 30),
            ('2020-01-01 23:00:00'::timestamp, 40),
            ('2020-02-01 23:00:00'::timestamp, 50),
            ('2020-02-29 22:30:00'::timestamp, 60),
            ('2020-03-01 23:00:00'::timestamp, 70),
            ('2020-03-31 22:00:00'::timestamp, 80)
          )
        AS t (date_and_time, value)
      SQL
    end
  end

  shared_context 'setup_login_embed_user' do
    let(:admin) { get_test_admin }
    let(:source) { nil }
    let(:payload) { {} }

    let(:embed_link) do
      el = FactoryBot.create :embed_link, source: source, filter_ownerships: [], tenant: get_test_tenant
      el.set_public_user
      el.share_source
      el
    end

    let(:embed_token) do
      sk = embed_link.secret_key
      jwt_encode(sk, payload, Time.now.to_i + 1000)
    end

    let(:headers) do
      {
        PublicLinks::AuthenticationHelper::EMBED_ID_HEADER => embed_link.hash_code,
        PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER => embed_token,
      }
    end

    let(:embed_token) do
      sk = embed_link.secret_key
      jwt_encode(sk, payload, Time.now.to_i + 1000)
    end

    let(:headers) do
      {
        PublicLinks::AuthenticationHelper::EMBED_ID_HEADER => embed_link.hash_code,
        PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER => embed_token,
      }
    end

    before do
      ts = FactoryBot.create :tenant_subscription, status: 'active'
      ts.update_embed_workers! 5
      sign_out admin
      request.headers.merge!(headers)
    end
  end

  describe '#submit_generate' do
    let(:root_model_id) { rm.id }
    let(:data_set_id) { nil }
    let(:vs_params) do
      {
        viz_type: 'data_table',
        fields: {},
        format: {},
        filters: [],
      }
    end
    let(:options_params) do
      Viz::Data::Vl::PivotRunnerOption.new.to_h.merge(Viz::Data::Vl::PostExploreOption.new.to_h)
    end
    let(:source_params) { nil }
    let(:params) do
      {
        viz_setting: vs_params,
        root_model_id: root_model_id,
        data_set_id: data_set_id,
        options: options_params,
        source: source_params,
      }
    end

    before do
      request.content_type = 'application/json'
    end

    it 'authenticates root model' do
      sign_in get_test_user

      get :submit_generate, format: :json, params: params

      expect(response.status).to eq 403
    end

    def test_cache_duration!(metadata, cache_duration)
      # allow error to 1 seconds
      expect((metadata[:expired_at].to_i - metadata[:saved_at].to_i) - (cache_duration * 60)).to be < 2
    end

    context 'parsed_query' do
      let(:expected_sql) do
        <<~SQL
          WITH new_sql_model AS (
            SELECT
              T0."name" AS "name",
              T0."age" AS "age"
            FROM
              (
                with t(name,age) as (
                  values('alice', 1),
                    ('bob', 2)
                )
                select * from t
              ) T0
          )
          SELECT
            T0."name" AS "name",
            T0."age" AS "age"
          FROM
            new_sql_model T0
        SQL
      end

      it 'saves parsed query to job.running_data' do
        get :submit_generate, format: :json, params: params

        res = JSON.parse(response.body)
        job = Job.find res['job_id']
        assert_success_job!(job)

        expect(job.running_info[:has_generated_sql]).to be true
        expect(job.running_data[:parsed_query]).to eq(expected_sql)

        cache_metadata = PostgresCache.fetch_metadata(job.data[:cache_key])

        expect(cache_metadata['parsed_query']).to eq expected_sql
      end

      it 'returns parsed query if result is cached' do
        get :submit_generate, format: :json, params: params

        res = JSON.parse(response.body)
        job = Job.find res['job_id']
        assert_success_job!(job)

        @controller = described_class.new

        get :submit_generate, format: :json, params: params
        res = JSON.parse(response.body)
        expect(res['data']['generated']['extra_details']['sql']).to eq(expected_sql)
      end
    end

    context 'job running step' do
      it 'only goes through job running steps when not cached' do
        # expect(Job).to receive(:update_running_step).exactly(3).times.and_call_original
        get :submit_generate, format: :json, params: params

        res = JSON.parse(response.body)
        job = Job.find res['job_id']
        assert_success_job!(job)

        expect(job.running_info[:step]).to eq 'processing_result'
        expect(job.running_info[:dbtype]).to eq 'postgresql'

        @controller = described_class.new

        expect(Job).not_to receive(:update_running_step)
        get :submit_generate, format: :json, params: params
        assert_success_response!
      end
    end

    context 'with timestamp column' do
      include_context 'simple_query_model'
      include_context 'timezone_setup'
      let(:tz) { 'Etc/UTC' }
      let(:query_model_sql) do
        <<~SQL
          select '2020-01-03T01:02:04.696969'::timestamp as ts
        SQL
      end
      let(:data_set) do
        ds = FactoryBot.create :data_set, root_model_id: nil
        ds.data_models << query_data_model
        ds.save!
        ds.reload
        ds
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:vs_params) do
        {
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                path_hash: { model_id: query_data_model.id, field_name: 'ts' },
                format: { type: 'auto' },
                type: 'datetime',
              },
            ],
          },
          format: {},
          filters: [],
          settings: {},
        }
      end

      it 'keeps the milliseconds' do
        post :submit_generate, format: :json, params: params
        job = assert_success_async_response!

        data = Viz::Data::GeneratorService.async_result(job)
        expect(data.values).to eq([['2020-01-03 01:02:04.696969']])
      end
    end

    context 'with action', aml_env: AmlStudio::WorkingEnvironment::Env::AmlStudio do
      # include_context 'simple_query_model'
      include_context 'aml_studio_basic'

      let(:query_model_sql) do
        <<~SQL
          with t(city_name, search_city_url, country_name, search_country_url, date) as (
            values
                ('ho chi minh', 'https://google.com/search?q=ho chi minh', 'vietnam', 'https://google.com/search?q=vietnam', '2024-01-01'),
                ('hanoi', 'https://google.com/search?q=hanoi', 'vietnam', 'https://google.com/search?q=vietnam', '2024-01-01'),
                ('new york', 'https://google.com/search?q=new york', 'usa', 'https://google.com/search?q=usa', '2024-01-01')
          )
          select city_name, search_city_url, country_name, search_country_url, date from t
        SQL
      end

      let(:viz_setting_param) do
        {
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                path_hash: {
                  field_name: 'city_name',
                  model_id: 'city_model',
                },
                type: 'text',
                format: {
                  type: 'string',
                  sub_type: 'string',
                },
                actions: [
                  {
                    id: '31afc3f7-ea20-4049-ae24-5d54cc2f9cad',
                    label: 'Search City',
                    type: 'GoToUrlAction',
                    url: {
                      path_hash: {
                        field_name: 'search_city_url',
                        model_id: 'city_model',
                      },
                      type: 'text',
                      uuid: '5dda1238-89db-44ad-955d-63ea58b44737',
                      aggregation: 'min',
                      format: {
                        type: 'string',
                        sub_type: 'string',
                      },
                      is_action_input: true,
                    },
                    description: '',
                  },
                  {
                    id: 'random-id',
                    label: 'Search City',
                    type: 'GoToUrlAction',
                    url: {
                      path_hash: {
                        field_name: 'search_country_url',
                        model_id: 'city_model',
                      },
                      type: 'text',
                      uuid: '5dda1238-89db-44ad-955d-63ea58b44123',
                      aggregation: 'min',
                      format: {
                        type: 'string',
                        sub_type: 'string',
                      },
                      is_action_input: true,
                    },
                    description: '',
                  },
                ],
              },
            ],
          },
          settings: {
            misc: {
              custom_color_list: [
                {},
              ],
              pagination_size: 25,
              show_row_number: true,
              row_limit: -1,
              row_height: 'Single line',
            },
            conditional_formatting: [],
            aggregation: {
              show_total: false,
              show_average: false,
            },
            others: {
              include_empty_children_rows: false,
            },
            quick_pivot: false,
            sort: {},
            pop_settings: nil,
          },
          format: {},
          filters: [],
          amql: {
            filters: [],
            adhoc_fields: [],
          },
          adhoc_fields: [],
        }
      end

      let(:binding_json) do
        {
          name: 'city_ds',
          _type: 'Dataset',
          value: {
            __engine__: 'aql',
            label: 'Cities',
            description: '',
            owner: '<EMAIL>',
            distinct_rows: true,
            __fqn__: 'city_ds',
            data_source_name: 'pg',
            models: [
              {
                label: 'Cities',
                description: '',
                owner: '<EMAIL>',
                data_source_name: 'pg',
                models: [],
                dimension: {
                  city_name: {
                    name: 'city_name',
                    hidden: false,
                    definition: {
                      __type__: 'Heredoc',
                      name: 'sql',
                      content: '{{ #SOURCE.city_name }}',
                    },
                    label: 'City Name',
                    type: 'text',
                    __childIdx__: 4,
                    __doc__: '',
                  },
                  search_city_url: {
                    name: 'search_city_url',
                    hidden: false,
                    definition: {
                      __type__: 'Heredoc',
                      name: 'sql',
                      content: '{{ #SOURCE.search_city_url }}',
                    },
                    label: 'Search City Url',
                    type: 'text',
                    __childIdx__: 5,
                    __doc__: '',
                  },
                  country_name: {
                    name: 'country_name',
                    hidden: false,
                    definition: {
                      __type__: 'Heredoc',
                      name: 'sql',
                      content: '{{ #SOURCE.country_name }}',
                    },
                    label: 'Country Name',
                    type: 'text',
                    __childIdx__: 6,
                    __doc__: '',
                  },
                  search_country_url: {
                    name: 'search_country_url',
                    hidden: false,
                    definition: {
                      __type__: 'Heredoc',
                      name: 'sql',
                      content: '{{ #SOURCE.search_country_url }}',
                    },
                    label: 'Search Country Url',
                    type: 'text',
                    __childIdx__: 7,
                    __doc__: '',
                  },
                  date: {
                    name: 'date',
                    hidden: false,
                    definition: {
                      __type__: 'Heredoc',
                      name: 'sql',
                      content: '{{ #SOURCE.date }}',
                    },
                    label: 'Date',
                    type: 'date',
                    __childIdx__: 8,
                    __doc__: '',
                  },
                },
                __fqn__: 'city_model',
                type: 'query',
                query: {
                  __type__: 'Heredoc',
                  name: 'sql',
                  content: query_model_sql,
                },
                __doc__: '',
                name: 'city_model',
                __type__: 'QueryModel',
              },
            ],
            relationships: [],
            __doc__: '',
            name: 'city_ds',
            __type__: 'Dataset',
          },
        }
      end

      before do
        FeatureToggle.toggle_global(DataModel::FT_AQL, true)
        FeatureToggle.toggle_global(AmlStudio::Project::FT_UTILIZE_FE_PROCESSING, true)
        request.headers[HolisticsSetting::HEADER_AML_ENV] = AmlStudio::WorkingEnvironment::Env::AmlStudio.serialize

        params[:viz_setting] = viz_setting_param
        params[:binding_json] = binding_json
        params[:source] = {
          type: 'DataSet',
          id: 'city_ds',
          action: 'preview',
        }
        params[:project_id] = project.id
      end

      it 'FT_ACTION off - viz data without action data' do |ex|
        post :submit_generate, format: :json, params: params
        job = assert_success_async_response!

        data = Viz::Data::GeneratorService.async_result(job)
        SnapshotTest.test!(
          data.values,
          rspec_example: ex,
          snapshot_name: 'viz_without_action.json',
        )
      end

      it 'FT_ACTION on - viz data with action data' do |ex|
        FeatureToggle.toggle_global(Viz::Action::FT_ACTION, true)

        post :submit_generate, format: :json, params: params
        job = assert_success_async_response!

        data = Viz::Data::GeneratorService.async_result(job)
        SnapshotTest.test!(
          data.values,
          rspec_example: ex,
          snapshot_name: 'viz_with_action.json',
        )
        SnapshotTest.test!(
          JSON.parse(response.body)['extra_details']['actions'],
          rspec_example: ex,
          snapshot_name: 'viz_with_action_extra_details.json',
        )
      end

      it 'action works with PoP' do |ex|
        FeatureToggle.toggle_global(Viz::Action::FT_ACTION, true)

        viz_setting_param[:fields][:table_fields].push({
          path_hash: {
            field_name: 'city_name',
            model_id: 'city_model',
          },
          type: 'text',
          format: {
            type: 'number',
            format: {
              pattern: 'inherited',
            },
          },
          aggregation: 'count',
          transformation: nil,
        })

        viz_setting_param[:settings][:pop_settings] = {
          field: {
            path_hash: {
              field_name: 'date',
              model_id: 'city_model',
            },
            type: 'date',
            uuid: 'e2baa1b5-ea02-4565-83cc-b095d8a86c8c',
            format: {
              type: 'date',
              sub_type: 'mmm dd yyyy',
            },
          },
          type: 'relative',
          offset: 1,
          period: 'year',
          condition: {
            operator: 'between',
            values: [],
          },
          show_growth_rate: true,
        }

        post :submit_generate, format: :json, params: params
        job = assert_success_async_response!

        data = Viz::Data::GeneratorService.async_result(job)

        SnapshotTest.test!(
          data.values,
          rspec_example: ex,
          snapshot_name: 'action_works_with_pop.json',
        )
        SnapshotTest.test!(
          JSON.parse(response.body)['extra_details']['actions'],
          rspec_example: ex,
          snapshot_name: 'action_works_with_pop_extra_details.json',
        )
      end
    end

    context 'embed with user attribute' do
      include_context 'dynamic_data_source'

      before do
        FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, true)
        FeatureToggle.toggle_global(DataModel::FT_AQL, true)

        params[:viz_setting] = viz_setting_param
        params[:source] = source_param
        params[:root_model_id] = nil
        params[:data_set_id] = dataset.id

        request.headers[PublicLinks::AuthenticationHelper::EMBED_ID_HEADER] = embed_code
      end

      let(:dashboard) { Dashboard.find_by!(uname: 'ecommerce_dashboard') }
      let(:embed_link) do
        link = EmbedLink.new(
          source: dashboard,
          source_type: 'Dashboard',
          owner_id: user.id,
          tenant_id: dashboard.tenant_id,
          filter_ownerships: [],
          version: 4,
        )
        link.set_public_user
        link.share_source
        link.save
        link
      end
      let(:embed_code) { embed_link.hash_code }
      let(:embed_secret) { embed_link.secret_key }

      def embed_token(embed_payload)
        jwt_encode(embed_secret, embed_payload, Time.now.to_i + (24 * 60 * 60))
      end

      let(:dataset) { DataSet.find_by!(uname: 'ecommerce') }
      let(:viz_setting_param) do
        {
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                path_hash: {
                  field_name: 'name',
                  model_id: 'countries',
                },
                type: 'text',
                format: {
                  type: 'string',
                },
              },
            ],
          },
          settings: {},
          format: {},
          filters: [],
          amql: {
            filters: [],
            adhoc_fields: [],
          },
          adhoc_fields: [],
        }
      end
      let(:source_param) do
        {
          type: 'VizBlock',
          id: "#{dashboard.id}:v1",
          action: 'view',
          uname: 'v1',
        }
      end

      it 'dynamic data source not working without user attribute' do
        request.headers[PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER] = embed_token({})

        post :submit_generate, format: :json, params: params
        assert_response_status!(422)
        res = JSON.parse(response.body)
        expect(res['errors'][0]['message']).to match(/Unexpected nil value when resolving dynamic data source/i)
      end

      it 'dynamic data work with user attributes' do
        payload = {
          user_attributes: {
            data_source: 'pg',
          },
        }
        request.headers[PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER] = embed_token(payload)

        post :submit_generate, format: :json, params: params
        job = assert_success_async_response!

        data = Viz::Data::GeneratorService.async_result(job)
        expect(data.values).to eq([['Vietnam'], ['Singapore'], ['Thailand'], ['Malaysia']])
      end

      it 'cannot find data source' do
        payload = {
          user_attributes: {
            data_source: 'abcd',
          },
        }
        request.headers[PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER] = embed_token(payload)

        post :submit_generate, format: :json, params: params
        assert_response_status!(422)
        res = JSON.parse(response.body)
        expect(res['errors'][0]['message']).to match(/Cannot find datasource: `abcd`/i)
      end
    end

    context 'embed portal' do
      include_context 'aml_studio_embed_row_level_permissions'

      let!(:embed_portal) { EmbedPortal.find_by(uname: 'app') }
      let!(:embed_credentials) do
        embed_link = create(:embed_link, source: project, tenant: tenant, version: 4)
        embed_link.set_public_user
        embed_link
      end
      let(:embed_payload) do
        {
          'object_name' => embed_portal.uname,
          'object_type' => 'EmbedPortal',
          'user_attributes' => {
            'country_name' => 'Vietnam',
          },
        }
      end
      let(:embed_token) do
        jwt_encode(embed_credentials.secret_key, embed_payload, Time.now.to_i + (24 * 60 * 60))
      end
      let(:dataset) { DataSet.find_by!(uname: 'ecommerce_embed') }
      let(:viz_setting_param) do
        {
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                path_hash: {
                  field_name: 'name',
                  model_id: 'countries',
                },
                type: 'text',
                format: {
                  type: 'string',
                  sub_type: 'string',
                },
                actions: [],
              },
            ],
          },
          settings: {},
          format: {},
          filters: [],
          amql: {
            filters: [],
            adhoc_fields: [],
          },
          adhoc_fields: [],
        }
      end

      before do
        FeatureToggle.toggle_global(EmbedLink::FT_USE_SINGLE_EMBED_KEY, true)
        FeatureToggle.toggle_global(EmbedPortal::FT_EMBED_PORTAL_ENABLED, true)
        FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, true)

        FeatureToggle.toggle_global(DataModel::FT_AQL, true)
      end

      it 'work' do
        params[:viz_setting] = viz_setting_param
        params[:source] = {
          type: 'DataSet',
          id: dataset.id,
          action: 'preview',
        }
        params[:root_model_id] = nil
        params[:data_set_id] = dataset.id

        request.headers[PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER] = embed_token
        request.headers[PublicLinks::AuthenticationHelper::EMBED_ID_HEADER] = embed_credentials.hash_code

        post :submit_generate, format: :json, params: params
        job = assert_success_async_response!

        data = Viz::Data::GeneratorService.async_result(job)
        expect(data.values).to eq([['Vietnam']])
      end

      describe 'work with user attribute' do
        let(:embed_payload) do
          {
            'object_name' => embed_portal.uname,
            'object_type' => 'EmbedPortal',
            'user_attributes' => {
              'country_name' => ['Vietnam'],
            },
          }
        end
        let(:embed_token) do
          jwt_encode(embed_credentials.secret_key, embed_payload, Time.now.to_i + (24 * 60 * 60))
        end
        let(:dataset) { DataSet.find_by!(uname: 'ecommerce_embed_2') }
        let(:viz_setting_param) do
          {
            viz_type: 'data_table',
            fields: {
              table_fields: [
                {
                  path_hash: {
                    field_name: 'name',
                    model_id: 'countries',
                  },
                  type: 'text',
                  format: {
                    type: 'string',
                    sub_type: 'string',
                  },
                  actions: [],
                },
              ],
            },
            settings: {},
            format: {},
            filters: [],
            amql: {
              filters: [],
              adhoc_fields: [],
            },
            adhoc_fields: [],
          }
        end

        it 'work' do
          params[:viz_setting] = viz_setting_param
          params[:source] = {
            type: 'DataSet',
            id: dataset.id,
            action: 'preview',
          }
          params[:root_model_id] = nil
          params[:data_set_id] = dataset.id

          request.headers[PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER] = embed_token
          request.headers[PublicLinks::AuthenticationHelper::EMBED_ID_HEADER] = embed_credentials.hash_code

          post :submit_generate, format: :json, params: params
          job = assert_success_async_response!

          data = Viz::Data::GeneratorService.async_result(job)
          expect(data.values).to eq([['Vietnam']])
        end

        it 'show no data if user attribute is not set' do
          embed_payload = {
            'object_name' => embed_portal.uname,
            'object_type' => 'EmbedPortal',
            'user_attributes' => {},
          }
          embed_token = jwt_encode(embed_credentials.secret_key, embed_payload, Time.now.to_i + (24 * 60 * 60))
          params[:viz_setting] = viz_setting_param
          params[:source] = {
            type: 'DataSet',
            id: dataset.id,
            action: 'preview',
          }
          params[:root_model_id] = nil
          params[:data_set_id] = dataset.id

          request.headers[PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER] = embed_token
          request.headers[PublicLinks::AuthenticationHelper::EMBED_ID_HEADER] = embed_credentials.hash_code

          post :submit_generate, format: :json, params: params
          job = assert_success_async_response!

          data = Viz::Data::GeneratorService.async_result(job)
          expect(data.values).to eq([])
        end
      end
    end

    context 'exploration source is widget' do
      let!(:viz_setting) do
        create(
          :viz_setting,
          fields: {
            table_fields: [
              {
                path_hash: { field_name: rm.fields.first.name, model_id: rm.id },
                custom_label: '',
                type: rm.fields.first.type,
                format: { type: 'auto' },
              },
            ],
          },
        )
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:qr) { FactoryBot.create :query_report, viz_setting: viz_setting, data_set_id: data_set_id }
      let(:dashboard) { FactoryBot.create :dashboard, version: 3 }
      let(:dw) { FactoryBot.create :dashboard_widget, dashboard: dashboard, source: qr }
      let(:source) { dw }
      let(:source_params) { { type: source.class.name, id: source.id, action: 'explore' } }
      let(:vs_params) do
        JSON.parse(VizSettingSerializer.new(viz_setting, root: false).to_json)
      end
      let(:new_source) { data_set }
      let(:new_source_params) { { type: new_source.class.name, id: new_source.id, action: 'view' } }
      let(:new_params) do
        {
          viz_setting: vs_params,
          root_model_id: root_model_id,
          data_set_id: data_set_id,
          options: options_params,
          source: new_source_params,
        }
      end

      context 'tenant don\'t have cache settings' do
        before do
          tenant.settings[:report_cache_duration] = nil
          tenant.save!
        end
        it 'uses default cache duration' do
          post :submit_generate, format: :json, params: params
          assert_success_response!
          job = Job.find(JSON.parse(response.body)['job_id'])
          assert_success_job!(job)
          cache_key = job.data[:cache_key]
          metadata = PostgresCache.fetch_metadata(cache_key)
          test_cache_duration!(metadata, DataModels::Explorable::QUERY_EXPIRE_TIME / 60)
        end
      end

      it 'stores the source as the origin of the job' do
        post :submit_generate, format: :json, params: params
        assert_success_response!
        job = Job.find(JSON.parse(response.body)['job_id'])
        assert_success_job!(job)
        expect(job.origin.entity_type).to eql('DashboardWidget')
        expect(job.origin.entity_id).to eql(data_set_id)
        expect(job.origin.entity_action).to eql('explore')
      end

      context 'otel tracing', otel: true, type: :request do
        it 'Job.new_job span has origin_type and origin_id attributes' do |_ex|
          # reset OTel after triggering the lazy processings of `params` to reduce noise
          params
          otel_reset

          post '/viz_data/submit_generate', params: params, as: :json
          job = assert_success_async_response!
          assert_success_job!(job)

          new_job_span = otel_finished_spans.find { |s| s.name == 'Job.new_job' }
          expect(new_job_span).to be_present
          expect(new_job_span.attributes['h.origin_type']).to eq(job.origin.entity_type)
          expect(new_job_span.attributes['h.origin_id']).to eq(job.origin.entity_id.to_s)
        end
      end

      context 'set job queue tag based on origin of the job' do
        context 'set tag to report when job is from' do
          it 'dashboard widget' do
            post :submit_generate, format: :json, params: params
            assert_success_response!
            job = Job.find(JSON.parse(response.body)['job_id'])
            expect(job.tag).to eq 'report'
          end

          context 'dashboard as code' do
            include_context 'dashboards_v4'
            it 'with source is explorable viz block' do
              params = get_params_submit_gererate_dashboard_4(dashboard_table_timezone)
              params[:source] = { ** params[:source], action: 'view' }

              post :submit_generate, format: :json, params: params
              assert_success_response!
              job = Job.find(JSON.parse(response.body)['job_id'])
              expect(job.tag).to eq 'report'
            end
          end
        end

        context 'sets the tag to non-report' do
          it ' if the job is not from dashboard widget, explorable viz block of dashboard as code' do
            post :submit_generate, format: :json, params: new_params
            assert_success_response!
            job = Job.find(JSON.parse(response.body)['job_id'])
            expect(job.tag).to eq 'adhoc_query'
          end
        end
      end

      context 'tenant has default cache duration' do
        before do
          tenant.settings[:report_cache_duration] = 300
          tenant.save!
        end

        it 'uses tenant default cache duration' do
          post :submit_generate, format: :json, params: params
          assert_success_response!
          job = Job.find(JSON.parse(response.body)['job_id'])
          assert_success_job!(job)
          cache_key = job.data[:cache_key]
          metadata = PostgresCache.fetch_metadata(cache_key)
          test_cache_duration!(metadata, tenant.settings[:report_cache_duration])
        end

        context 'dashboard has cache setting' do
          let!(:cache_setting) { FactoryBot.create :cache_setting, source: dashboard, duration: 1440 }

          it 'uses cache setting of dashboard' do
            post :submit_generate, format: :json, params: params
            assert_success_response!
            job = Job.find(JSON.parse(response.body)['job_id'])
            assert_success_job!(job)
            cache_key = job.data[:cache_key]
            metadata = PostgresCache.fetch_metadata(cache_key)
            test_cache_duration!(metadata, dashboard.cache_duration)
          end
        end
      end

      it 'job metadata contains viz setting hashid' do
        post :submit_generate, format: :json, params: new_params
        assert_success_response!
        job = Job.find(JSON.parse(response.body)['job_id'])
        assert_success_job!(job)
        expect(job.data[:metadata][:original_viz_hashid]).to eq viz_setting.hashid
      end
    end

    describe 'authorize via modeling source' do
      before do
        FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
        sign_in explorer
      end

      let(:viz_setting) do
        FactoryBot.create(
          :viz_setting,
          fields: {
            table_fields: [
              {
                path_hash: { field_name: rm.fields.first.name, model_id: rm.id },
                custom_label: '',
                type: rm.fields.first.type,
                format: { type: 'auto' },
              },
            ],
          },
        )
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:qr) { FactoryBot.create :query_report, viz_setting: viz_setting, data_set_id: data_set_id }
      let(:dashboard) { FactoryBot.create :dashboard, version: 3 }
      let(:dw) { FactoryBot.create :dashboard_widget, dashboard: dashboard, source: qr }
      let(:source) { qr }
      let(:source_params) { { type: source.class.name, id: source.id } }
      let(:vs_params) do
        JSON.parse(VizSettingSerializer.new(viz_setting, root: false).to_json)
      end

      shared_context 'modified viz fields' do
        before do
          vs_params['fields']['table_fields'] << {
            path_hash: { field_name: rm.fields.last.name, model_id: rm.id },
            custom_label: '',
            type: rm.fields.last.type,
            format: { type: 'auto' },
          }
        end
      end

      shared_context 'modified adhoc fields' do
        before do
          vs_params['adhoc_fields'] = [
            {
              is_custom: true,
              is_external: true,
              label: 'Test',
              name: 'test',
              sql: "#{rm.name}.#{rm.fields.last.name} + 1",
              syntax: 'aml',
              type: 'number',
            },
          ]
        end
      end

      shared_examples 'can modify y axis series_settings' do
        before do
          viz_setting.viz_type = 'line_chart'
          viz_setting.fields = {
            x_axis: {},
            series: {},
            y_axes: [
              {
                columns: [
                  {
                    path_hash: { field_name: rm.fields.last.name, model_id: rm.id },
                    custom_label: '',
                    type: rm.fields.last.type,
                    format: { type: 'auto' },
                    series_settings: {
                      palette_id: 1,
                      series_hash: {
                        a: {
                          color: 'red',
                          hidden: false,
                        },
                      },
                    },
                  },
                ],
              },
            ],
          }
          viz_setting.save!
          vs_params['fields'] = {
            x_axis: {},
            series: {},
            y_axes: [
              {
                columns: [
                  {
                    path_hash: { field_name: rm.fields.last.name, model_id: rm.id },
                    custom_label: '',
                    type: rm.fields.last.type,
                    format: { type: 'auto' },
                    series_settings: {
                      palette_id: 1,
                      series_hash: {
                        b: {
                          color: 'blue',
                          hidden: true,
                        },
                        c: {
                          color: 'red',
                          hidden: false,
                        },
                      },
                    },
                  },
                ],
              },
            ],
          }
        end

        it 'allows modification' do
          post :submit_generate, format: :json, params: params
          assert_success_response!
        end
      end
      shared_examples 'cannot reduce viz filters' do
        let(:input_vs_filters) { [] }
        before do
          viz_setting.filters = [
            { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['a'], modifier: nil },
            { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['b'], modifier: nil },
          ]
          viz_setting.save!
          vs_params['filters'] = input_vs_filters
        end

        context 'adding filter' do
          let(:input_vs_filters) do
            [
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['a'], modifier: nil,
                aggregation: nil, },
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['b'], modifier: nil },
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['c'], modifier: nil },
            ]
          end

          it 'runs successfully' do
            post :submit_generate, format: :json, params: params
            assert_success_response!
          end
        end

        context 'reordering filters' do
          let(:input_vs_filters) do
            [
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['b'], modifier: nil },
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['a'], modifier: nil },
            ]
          end

          it 'runs successfully' do
            post :submit_generate, format: :json, params: params
            assert_success_response!
          end
        end

        context 'removing filter' do
          let(:input_vs_filters) do
            [
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['a'], modifier: nil },
            ]
          end

          it 'raises permission denied' do
            post :submit_generate, format: :json, params: params
            assert_response_status!(403)
            expect(response.body).to match(/no permission to modify visualization scope \(filters\)/i)
          end
        end

        context 'modifying filter' do
          let(:input_vs_filters) do
            [
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: %w[a c], modifier: nil },
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['b'], modifier: nil },
            ]
          end

          it 'raises permission denied' do
            post :submit_generate, format: :json, params: params
            assert_response_status!(403)
            expect(response.body).to match(/no permission to modify visualization scope \(filters\)/i)
          end
        end

        context 'filters have frontend keys' do
          let(:input_vs_filters) do
            [
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['a'], modifier: nil,
                uuid: 'ahihi', },
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['b'], modifier: nil },
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: ['a'], modifier: nil,
                _mapped: true, },
            ]
          end

          it 'runs successfully' do
            post :submit_generate, format: :json, params: params
            assert_success_response!
          end
        end
      end
      shared_examples 'can reduce viz filters' do
        let(:input_vs_filters) { [] }
        before do
          viz_setting.filters = [
            { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: %w[a b], modifier: nil },
          ]
          viz_setting.save!
          vs_params['filters'] = input_vs_filters
        end

        context 'adding filter' do
          let(:input_vs_filters) do
            [
              { path_hash: { field_name: 'name', model_id: rm.id }, operator: 'is', values: %w[a b c d],
                modifier: nil, },
            ]
          end

          it 'runs successfully' do
            post :submit_generate, format: :json, params: params
            assert_success_response!
          end
        end
      end
      shared_examples 'can reduce row limit' do
        let(:source_row_limit) { 200 }
        let(:input_row_limit) { 100 }
        before do
          viz_setting.row_limit = source_row_limit
          viz_setting.save!
          vs_params['settings']['misc'] ||= {}
          vs_params['settings']['misc']['row_limit'] = input_row_limit
        end

        it 'runs successfully' do
          post :submit_generate, format: :json, params: params
          assert_success_response!
        end

        context 'source has no limit' do
          let(:source_row_limit) { 200 }

          it 'runs successfully' do
            post :submit_generate, format: :json, params: params
            assert_success_response!
          end
        end
      end
      shared_examples 'can only reduce row limit' do
        let(:source_row_limit) { 200 }
        let(:input_row_limit) { 100 }
        before do
          viz_setting.row_limit = source_row_limit
          viz_setting.save!
          vs_params['settings']['misc'] ||= {}
          vs_params['settings']['misc']['row_limit'] = input_row_limit
        end

        it 'runs successfully' do
          post :submit_generate, format: :json, params: params
          assert_success_response!
        end

        context 'source has no limit' do
          let(:source_row_limit) { 200 }

          it 'runs successfully' do
            post :submit_generate, format: :json, params: params
            assert_success_response!
          end
        end

        context 'increasing limit' do
          let(:input_row_limit) { 400 }

          it 'raises permission denied' do
            post :submit_generate, format: :json, params: params
            assert_response_status!(403)
            expect(response.body).to match(/allowed to view 200 records only/i)
          end

          context 'infinite limit' do
            let(:input_row_limit) { -1 }

            it 'raises permission denied' do
              post :submit_generate, format: :json, params: params
              assert_response_status!(403)
              expect(response.body).to match(/allowed to view 200 records only/i)
            end
          end
        end
      end
      shared_examples 'can increase row limit' do
        let(:source_row_limit) { 200 }
        let(:input_row_limit) { 100 }
        before do
          viz_setting.row_limit = source_row_limit
          viz_setting.save!
          vs_params['settings']['misc'] ||= {}
          vs_params['settings']['misc']['row_limit'] = input_row_limit
        end

        it 'runs successfully' do
          post :submit_generate, format: :json, params: params
          assert_success_response!
        end

        context 'increasing limit' do
          let(:input_row_limit) { 400 }

          it 'runs successfully' do
            post :submit_generate, format: :json, params: params
            assert_success_response!
          end

          context 'infinite limit' do
            let(:input_row_limit) { -1 }

            it 'runs successfully' do
              post :submit_generate, format: :json, params: params
              assert_success_response!
            end
          end
        end
      end

      context 'user does not have permission to explore model/dataset' do
        context 'user does not have read permisison on source' do
          context 'report' do
            it 'raises permission denied' do
              post :submit_generate, format: :json, params: params
              assert_response_status!(403)
              expect(response.body).to match(/no permission to explore result of query report/i)
            end
          end

          context 'widget' do
            let(:source) { dw }

            it 'raises permission denied' do
              post :submit_generate, format: :json, params: params
              assert_response_status!(403)
              expect(response.body).to match(/no permission to explore result of dashboard widget/i)
            end
          end
        end

        context 'user has read permission on source' do
          context 'report' do
            before do
              admin.share(explorer, :read, qr)
            end

            it 'runs successfully' do
              post :submit_generate, format: :json, params: params
              assert_success_response!
            end

            context 'report widget' do
              let(:source) { dw }

              it 'raises permission denied' do
                post :submit_generate, format: :json, params: params
                assert_response_status!(403)
                expect(response.body).to match(/no permission to explore result of dashboard widget/i)
              end
            end

            context 'modified viz fields' do
              include_context 'modified viz fields'
              it 'raises permission denied' do
                post :submit_generate, format: :json, params: params
                assert_response_status!(403)
                expect(response.body).to match(/no permission to modify visualization scope \(fields\)/i)
              end
            end

            context 'modified adhoc fields' do
              include_context 'modified adhoc fields'
              it 'raises permission denied' do
                post :submit_generate, format: :json, params: params
                assert_response_status!(403)
                expect(response.body).to match(/no permission to modify visualization scope \(business calculations\)/i)
              end
            end

            it_behaves_like('can modify y axis series_settings')
            it_behaves_like('cannot reduce viz filters')
            it_behaves_like('can only reduce row limit')
          end

          context 'widget' do
            before do
              admin.share(explorer, :read, dashboard)
            end

            let(:source) { dw }

            it 'runs successfully' do
              post :submit_generate, format: :json, params: params
              assert_success_response!
            end

            context 'modified viz fields' do
              include_context 'modified viz fields'
              it 'raises permission denied' do
                post :submit_generate, format: :json, params: params
                assert_response_status!(403)
                expect(response.body).to match(/no permission to modify visualization scope \(fields\)/i)
              end
            end

            it_behaves_like('can modify y axis series_settings')
            it_behaves_like('cannot reduce viz filters')
            it_behaves_like('can only reduce row limit')
          end

          context 'metric sheet' do
            before do
              admin.share(explorer, :read, dashboard)
            end

            let(:query_model_sql) do
              <<~SQL
                with t(name, age, birthday, country) as (
                  values('a', 1, '2019-01-02', 'VN'),
                    ('b', 2, '2019-03-14', 'US'),
                    ('c', 3, '2019-01-01', 'VN'),
                    ('d', 4, '2019-02-03', 'VN')
                )
                select name, age, birthday::date, country from t
              SQL
            end
            let(:viz_setting) do
              FactoryBot.create(
                :viz_setting,
                viz_type: 'metric_sheet',
                fields: {
                  date: {
                    path_hash: { field_name: 'birthday', model_id: rm.id },
                    custom_label: '',
                    type: 'datetime',
                    format: { type: 'date', sub_type: 'mmm yyyy' },
                    transformation: 'datetrunc month',
                  },
                  rows: [{
                    path_hash: { field_name: 'name', model_id: rm.id },
                    custom_label: '',
                    type: 'number',
                    aggregation: 'count',
                    format: { type: 'auto' },
                  }],
                },
              )
            end
            let(:source) do
              report = FactoryBot.create :query_report, viz_setting: viz_setting, data_set_id: data_set_id
              FactoryBot.create :dashboard_widget, dashboard: dashboard, source: report
            end

            context 'modified viz setting' do
              before do
                # modify
                vs_params['fields']['date'] = {
                  path_hash: { field_name: 'birthday', model_id: rm.id },
                  custom_label: '',
                  type: 'datetime',
                  format: { type: 'date', sub_type: 'wwww' },
                  transformation: 'datetrunc week',
                  modified: true,
                }
              end

              it 'raises permission denied' do
                post :submit_generate, format: :json, params: params
                assert_response_status!(403)
                expect(response.body).to match(/no permission to modify visualization scope \(fields\)/i)
              end

              context 'with feature toggle enabled' do
                before do
                  FeatureToggle.toggle_global(Viz::Services::ValidateScope::FT_ALLOW_ANY_DATE_SCOPE, true)
                end

                it 'can run' do
                  post :submit_generate, format: :json, params: params
                  assert_success_response!
                end
              end
            end
          end
        end

        context 'source is deleted' do
          before do
            source.destroy!
          end

          it 'raise not found error' do
            post :submit_generate, format: :json, params: params
            assert_response_status!(422)
            expect(response.body).to match(/source does not exist/i)
          end
        end
      end

      context 'user has explore permission on model/dataset' do
        before do
          admin.share(explorer, :read, data_set)
        end

        context 'user does not have read permisison on source' do
          context 'report' do
            it 'raises permission denied' do
              post :submit_generate, format: :json, params: params
              assert_response_status!(403)
              expect(response.body).to match(/no permission to explore result of query report/i)
            end
          end

          context 'widget' do
            let(:source) { dw }

            it 'raises permission denied' do
              post :submit_generate, format: :json, params: params
              assert_response_status!(403)
              expect(response.body).to match(/no permission to explore result of dashboard widget/i)
            end
          end
        end

        context 'user has read permission on source' do
          context 'report' do
            before do
              admin.share(explorer, :read, qr)
            end

            it 'runs successfully' do
              post :submit_generate, format: :json, params: params
              assert_success_response!
            end

            context 'report widget' do
              let(:source) { dw }

              it 'raises permission denied' do
                post :submit_generate, format: :json, params: params
                assert_response_status!(403)
                expect(response.body).to match(/no permission to explore result of dashboard widget/i)
              end
            end

            context 'modified viz fields' do
              include_context 'modified viz fields'
              it 'runs successfully' do
                post :submit_generate, format: :json, params: params
                assert_success_response!
              end
            end

            it_behaves_like('can modify y axis series_settings')
            it_behaves_like('can reduce viz filters')
            it_behaves_like('can increase row limit')
          end

          context 'widget' do
            before do
              admin.share(explorer, :read, dashboard)
            end

            let(:source) { dw }

            it 'runs successfully' do
              post :submit_generate, format: :json, params: params
              assert_success_response!
            end

            context 'modified viz fields' do
              include_context 'modified viz fields'
              it 'runs successfully' do
                post :submit_generate, format: :json, params: params
                assert_success_response!
              end
            end

            it_behaves_like('can modify y axis series_settings')
            it_behaves_like('can reduce viz filters')
            it_behaves_like('can increase row limit')
          end
        end
      end

      context 'embed users' do
        let(:embed_link) do
          el = FactoryBot.create :embed_link, source: dashboard, filter_ownerships: [], version: 3
          el.set_public_user
          el.share_source
          el
        end
        let(:embed_payload) do
          {}
        end
        let(:embed_token) do
          sk = embed_link.secret_key
          jwt_encode(sk, embed_payload, Time.now.to_i + 1000)
        end
        let(:headers) do
          {
            PublicLinks::AuthenticationHelper::EMBED_ID_HEADER => embed_link.hash_code,
            PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER => embed_token,
          }
        end
        let(:source) { dw }

        before do
          sign_out explorer
          request.headers.merge!(headers)
        end

        shared_examples 'authorize via modeling source with embed user' do
          it 'runs successfully' do
            post :submit_generate, format: :json, params: params
            assert_success_response!
          end

          context 'drillthroughs' do
            let(:dashboard2) { FactoryBot.create :dashboard, version: 3 }
            let(:dw) { FactoryBot.create :dashboard_widget, dashboard: dashboard2, source: qr }

            it 'raise permission denied when user do not have permission' do
              post :submit_generate, format: :json, params: params
              assert_response_status!(403)
              expect(response.body).to match(/no permission to explore result of dashboard widget/i)
            end

            context 'with drillthroughs provided in embed payload' do
              let(:embed_payload) do
                {
                  drillthroughs: {
                    dashboard2.id => {},
                  },
                }
              end

              it 'runs successfully' do
                post :submit_generate, format: :json, params: params
                assert_success_response!
              end
            end
          end

          context 'with embed v3 configs' do
            let(:query_model_sql) do
                <<~SQL
                  with t(name,age,created_at) as (
                    values('alice', 1, '2020-01-02'),
                      ('bob', 2, '2022-01-03'),
                      ('calvin', 2, '2022-01-04')
                  )
                  select name, age, created_at::timestamp as created_at from t
              SQL
            end
            let(:name_field_path) do
              DataModeling::Values::FieldPath.new(field_name: 'name', model_id: rm.id)
            end
            let(:name_field_permission_rule) do
              {
                path: "#{data_set.uname}.#{rm.name}.name",
                operator: 'is_not',
                values: ['alice'],
                modifier: nil,
              }
            end
            let(:created_at_permission_rule) do
              {
                path: {
                  dataset: data_set.uname,
                  model: rm.name,
                  field: 'created_at',
                },
                operator: 'last',
                values: ['3'],
                modifier: 'year',
                options: {
                  include_current_period: true,
                },
              }
            end
            let(:filter_source) { FactoryBot.create :manual_filter_source, manual_options: [] }
            let(:filter_definition) do
              FactoryBot.create :dynamic_filter_definition, filter_source: filter_source,
                filter_type: DynamicFilters::Constants::FilterTypes::DATE, label: 'date_filter'
            end
            let!(:filter) do
              FactoryBot.create :dynamic_filter, dynamic_filter_holdable: dashboard, order: 0,
                dynamic_filter_definition: filter_definition
            end
            let(:embed_payload) do
              {
                permissions: {
                  row_based: [
                    name_field_permission_rule,
                    created_at_permission_rule,
                  ],
                },
                filters: {
                  date_filter: {
                    default_condition: {
                      operator: 'last',
                      values: ['2'],
                      modifier: ['year'],
                      options: {
                        include_current_period: true,
                      },
                    },
                  },
                },
              }
            end

            before do
              Timecop.freeze(DateTime.parse('2023-03-03'))

              vs_params[:filters] = [
                {
                  operator: 'is_not',
                  values: ['bob'],
                  path_hash: name_field_path.to_h,
                },
              ]
            end

            it 'add viz filters according to configs' do |ex|
              post :submit_generate, format: :json, params: params
              assert_success_response!

              job = assert_success_async_response!
              assert_success_job!(job)
              expect(job.tag).to eq('adhoc_query')

              SnapshotTest.test!(job.running_data[:parsed_query], rspec_example: ex, snapshot_name: 'embed_v3.sql')

              data = Viz::Data::GeneratorService.async_result(job)
              expect(data.values).to eq([['calvin']])
            end

            context 'with embed workers enabled' do
              before do
                FeatureToggle.toggle_global(Viz::Constants::FT_ENABLE_EMBED_WORKERS_FOR_EXPLORE, true)
              end

              it 'runs the job in embed queue' do
                post :submit_generate, format: :json, params: params
                assert_success_response!

                job = assert_success_async_response!
                assert_success_job!(job)
                expect(job.tag).to eq('embed')
              end
            end
          end
        end

        context 'use v3_configs' do
          before do
            FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, false)
          end

          it_behaves_like 'authorize via modeling source with embed user'
        end

        context 'use portal configs' do
          before do
            FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, true)
          end

          it_behaves_like 'authorize via modeling source with embed user'
        end

        context 'shareable user' do
          let(:permission_rules) do
            {}
          end
          let(:sl) do
            sl = ShareableLink.create!(
              tenant: tenant,
              user: analyst,
              resource: dashboard,
              permission_rules: permission_rules,
            )
            sl.set_public_user
            sl.share_resource
            sl
          end
          let(:headers) do
            {
              PublicLinks::AuthenticationHelper::SHAREABLE_HASHCODE_HEADER => sl.hash_code,
            }
          end
          let(:source) { dw }

          before do
            admin.share(analyst, :read, dashboard)
            sign_out explorer
            request.headers.merge!(headers)
          end

          it 'runs successfully' do
            post :submit_generate, format: :json, params: params
            assert_success_response!

            job_id = JSON.parse(response.body)['job_id']
            expect(VizDataAbility.new(sl.public_user).can?(:read, Job.find(job_id))).to be true
          end

          context 'with permission rules' do
            before do
              # enable to check that when permission rules are applied
              # every thing works as expected
              FeatureToggle.toggle_global(DataSet::FT_STRICT_PERMISSION_RULES, true)
            end

            let(:name_field_permission_rule) do
              {
                field_path: {
                  data_set_id: data_set.id,
                  model_id: rm.id,
                  field_name: 'name',
                  is_metric: false,
                },
                condition: {
                  operator: 'is',
                  values: ['bob'],
                  modifier: nil,
                },
              }
            end
            let(:permission_rules) do
              {
                row_based: [
                  name_field_permission_rule,
                ],
              }
            end

            it 'add viz filters according to the rules' do
              post :submit_generate, format: :json, params: params
              assert_success_response!

              job = Job.find(JSON.parse(response.body)['job_id'])
              assert_success_job!(job)

              data = Viz::Data::GeneratorService.async_result(job)
              expect(data.values).to eq([['bob']])
            end
          end
        end
      end
    end

    context 'submit_generate with exploring option' do
      before do
        options_params['exploring'] = true
      end

      it 'will use default limit' do
        post :submit_generate, format: :json, params: params
        assert_success_response!

        res = JSON.parse(response.body)

        job = Job.find(res['job_id'])
        assert_success_job!(job)
        parsed_sql = job.data.dig(:running, :parsed_query)

        expected_sql = "WITH new_sql_model AS (\n  SELECT\n    T0.\"name\" AS \"name\",\n    T0.\"age\" AS \"age\"\n  FROM\n    (\n      with t(name,age) as (\n        values('alice', 1),\n          ('bob', 2)\n      )\n      select * from t\n    ) T0\n)\nSELECT\n  T0.\"name\" AS \"name\",\n  T0.\"age\" AS \"age\"\nFROM\n  new_sql_model T0\nLIMIT 100000\n"

        expect(parsed_sql).to eq expected_sql
      end
    end

    context 'user is an explorer and is shared to explore Dataset' do
      let(:explorer) { FactoryBot.create(:explorer, tenant: admin.tenant) }
      let(:data_set_id) { data_set.id }

      before do
        FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
        admin.share(explorer, :read, data_set)
        data_set.update!(root_model_id: rm.id)
        sign_in explorer
        vs_params['fields'] = {
          table_fields: [
            {
              path_hash: { field_name: rm.fields.first.name, model_id: rm.id },
              custom_label: '',
              type: rm.fields.first.type,
              format: { type: 'auto' },
            },
          ],
        }
      end

      it 'allows exploration' do
        post :submit_generate, format: :json, params: params
        assert_success_response!
      end
    end

    context 'Dataset' do
      include_context 'test_tenant'

      let(:model1) { create_data_modeling_model_from_table(ds, 'products') }
      let(:model2) { create_data_modeling_model_from_table(ds, 'orders') }
      let(:data_set) do
        ds = DataSet.create_from_models(
          data_models: [model1, model2],
          title: 'New dataset',
          category_id: 0,
          data_source_id: model1.data_source.id,
          owner_id: admin.id,
          tenant_id: tenant.id,
        )
        ds.save!
        ds
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:options_params) do
        { page: 1, page_size: 25, sort: '-1_asc', sort_by_column_type: false, show_aggregated: false, exploring: true }
      end

      include_context 'data_modeling_schema_with_data'

      before do
        FeatureToggle.toggle_global('data_sets:enabled', true)
        FeatureToggle.toggle_global('data_source:enable_schema_info', true)
        FeatureToggle.toggle_global('data_models:explore_controls', true)
        DataSourceVersions::SchemaSynchronizationService.new(ds).execute
      end

      context 'model not found' do
        let(:vs_params) do
          {
            viz_type: 'data_table',
            adhoc_fields: [],
            fields: {
              table_fields: [
                {
                  path_hash: { field_name: 'category_id', model_id: -3 },
                  custom_label: '',
                  type: 'number',
                  format: { type: 'number', sub_type: 'auto' },
                },
              ],
            },
            filters: [],
            format: {},
          }
        end

        it 'raises informative error' do
          post :submit_generate, format: :json, params: params
          assert_response_status!(422)
          expect(response.body).to match(/.*category_id.*but the Model does not exist in Dataset.*new_dataset/i)
        end
      end

      context 'fields cannot be reached from one to another' do
        let(:vs_params) do
          {
            viz_type: 'data_table',
            adhoc_fields: [],
            fields: {
              table_fields: [
                {
                  path_hash: { field_name: 'category_id', model_id: model1.id },
                  custom_label: '',
                  type: 'number',
                  format: { type: 'number', sub_type: 'auto' },
                },
                {
                  path_hash: { field_name: 'discount', model_id: model2.id },
                  custom_label: '',
                  type: 'number',
                  format: { type: 'number', sub_type: 'auto' },
                },
              ],
            },
            filters: [],
            format: {},
          }
        end

        it 'returns error' do
          post :submit_generate, format: :json, params: params
          expected = {
            error: 'Can\'t combine selected fields due to missing relationships between them.',
            error_details: { related_docs: 'https://docs.holistics.io/guides/add-and-manage-your-model-relationships/' },
          }.to_json

          assert_response_status!(422)
          expect(response.body).to eq expected
        end
      end

      describe 'use sql gem' do
        before do
          FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
        end

        describe 'use adhoc field to substract two date fields' do
          let(:vs_params) do
            {
              viz_type: 'data_table',
              fields: {
                table_fields: [
                  {
                    label: 'test',
                    path_hash: { field_name: 'f_1' },
                    type: 'number',
                    format: { type: 'number', sub_type: 'auto' },
                  },
                ],
              },
              filters: [],
              adhoc_fields: [
                {
                  name: 'f_1',
                  syntax: 'aml',
                  sql: "epoch(#{model1.name}.created_at) - epoch(#{model1.name}.created_at)",
                  label: 'time_diff',
                },
              ],
              format: {},
            }
          end

          it 'results in second' do
            post :submit_generate, format: :json, params: params
            assert_success_response!

            job = Job.find JSON.parse(response.body)['job_id']
            assert_success_job!(job)

            data = Viz::Data::GeneratorService.async_result(job)
            expect(data.values).to eq [['0.000000']]
          end
        end

        describe 'use adhoc field to substract two measure' do
          let(:vs_params) do
            {
              viz_type: 'data_table',
              fields: {
                table_fields: [
                  {
                    label: 'test',
                    path_hash: { field_name: 'f_1' },
                    type: 'number',
                    format: { type: 'number', sub_type: 'auto' },
                  },
                ],
              },
              filters: [],
              adhoc_fields: [
                {
                  name: 'f_1',
                  syntax: 'aml',
                  sql: "count(#{model1.name}.created_at) - count(#{model1.name}.id)",
                  label: 'time_diff',
                },
              ],
              format: {},
            }
          end

          it 'results in duration' do
            post :submit_generate, format: :json, params: params
            assert_success_response!

            job = Job.find JSON.parse(response.body)['job_id']
            assert_success_job!(job)

            data = Viz::Data::GeneratorService.async_result(job)
            expect(data.values).to eq [['0']]
          end
        end
      end

      describe 'filter on aggregated fields' do
        let(:vs_params) do
          {
            viz_type: 'data_table',
            fields: {
              table_fields: [
                {
                  path_hash: { field_name: 'category_id', model_id: model1.id },
                  type: 'number',
                  format: { type: 'number', sub_type: 'auto' },
                  aggregation: 'count',
                },
              ],
            },
            filters: [
              {
                path_hash: { field_name: 'category_id', model_id: model1.id },
                aggregation: 'count',
                operator: 'greater_than',
                values: [4],
              },
            ],
            format: {},
          }
        end

        it 'filters the aggregated fields correctly' do
          post :submit_generate, format: :json, params: params
          assert_success_response!

          job = Job.find JSON.parse(response.body)['job_id']
          assert_success_job!(job)

          data = Viz::Data::GeneratorService.async_result(job)
          expect(data.values).to eq []
        end

        context 'the aggregated field is not selected' do
          let(:vs_params) do
            {
              viz_type: 'data_table',
              fields: {
                table_fields: [
                  {
                    path_hash: { field_name: 'name', model_id: model1.id },
                    type: 'text',
                  },
                ],
              },
              filters: [
                {
                  path_hash: { field_name: 'price', model_id: model1.id },
                  aggregation: 'sum',
                  operator: 'greater_than',
                  values: [3],
                },
              ],
              format: {},
            }
          end

          it 'filters the aggregated fields correctly' do
            post :submit_generate, format: :json, params: params
            assert_success_response!

            job = Job.find JSON.parse(response.body)['job_id']
            assert_success_job!(job)

            data = Viz::Data::GeneratorService.async_result(job)
            expect(data.values).to eq [['egg'], ['bread']]
          end
        end

        context 'the aggregated field has different type from its original field' do
          let(:vs_params) do
            {
              viz_type: 'data_table',
              fields: {
                table_fields: [
                  {
                    path_hash: { field_name: 'category_id', model_id: model1.id },
                    type: 'number',
                    format: { type: 'number', sub_type: 'auto' },
                  },
                ],
              },
              filters: [
                {
                  path_hash: { field_name: 'created_at', model_id: model1.id },
                  aggregation: 'count',
                  operator: 'greater_than',
                  values: [4],
                },
              ],
              format: {},
            }
          end

          it 'filters the aggregated fields correctly' do
            post :submit_generate, format: :json, params: params
            assert_success_response!

            job = Job.find JSON.parse(response.body)['job_id']
            assert_success_job!(job)

            data = Viz::Data::GeneratorService.async_result(job)
            expect(data.values).to eq []
          end
        end
      end
    end

    context 'with available drillthrough destinations' do
      let(:query_model_sql) do
        <<~SQL
          with t(name, age, birthday, country) as (
            values('a', 1, '2019-01-02', 'VN'),
              ('b', 2, '2019-03-14', 'US'),
              ('c', 3, '2019-01-01', 'VN'),
              ('d', 4, '2019-02-03', 'VN')
          )
          select name, age, birthday::date, country from t
        SQL
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:viz_setting) do
        FactoryBot.create(
          :viz_setting,
          fields: {
            table_fields: [
              {
                path_hash: { field_name: 'birthday', model_id: rm.id },
                custom_label: '',
                type: 'date',
                format: { type: 'auto' },
                transformation: 'datetrunc month',
              },
              {
                path_hash: { field_name: 'name', model_id: rm.id },
                custom_label: '',
                type: 'text',
                format: { type: 'auto' },
              },
              {
                path_hash: { field_name: 'name', model_id: rm.id },
                custom_label: '',
                type: 'text',
                format: { type: 'auto' },
              },
              {
                path_hash: { field_name: 'age', model_id: rm.id },
                custom_label: '',
                type: 'number',
                format: { type: 'auto' },
              },
              {
                path_hash: { field_name: 'name', model_id: rm.id },
                custom_label: '',
                type: 'number',
                aggregation: 'count',
                format: { type: 'auto' },
              },
            ],
          },
          filters: [
            {
              operator: 'transform_pop_relative',
              path_hash: { field_name: 'birthday', model_id: rm.id },
              values: [1],
              modifier: 'month',
            },
          ],
        )
      end
      let(:vs_params) do
        JSON.parse(VizSettingSerializer.new(viz_setting, root: false).to_json)
      end
      let(:dashboard) { FactoryBot.create :dashboard, title: 'B' }

      let(:field_path) { DataModeling::Values::FieldPath.new(field_name: 'name', model_id: query_data_model.id) }
      let(:filter_source) { FactoryBot.create :dm_field_filter_source, data_set: data_set, field_path: field_path }
      let(:filter_definition) { FactoryBot.create :dynamic_filter_definition, filter_source: filter_source }
      let!(:dynamic_filter) do
        FactoryBot.create :dynamic_filter, definition: filter_definition, filter_holdable: dashboard,
                                           drillthrough_enabled: true
      end
      let!(:dynamic_filterx) do
        FactoryBot.create :dynamic_filter, definition: filter_definition, filter_holdable: dashboard
      end

      let(:field_path2) { DataModeling::Values::FieldPath.new(field_name: 'age', model_id: query_data_model.id) }
      let(:filter_source2) { FactoryBot.create :dm_field_filter_source, data_set: data_set, field_path: field_path2 }
      let(:filter_definition2) { FactoryBot.create :dynamic_filter_definition, filter_source: filter_source2 }
      let!(:dynamic_filter2) do
        FactoryBot.create :dynamic_filter, definition: filter_definition2, filter_holdable: dashboard,
                                           drillthrough_enabled: true
      end

      let!(:dynamic_filter3) do
        FactoryBot.create :dynamic_filter, definition: filter_definition, filter_holdable: dashboard,
                                           drillthrough_enabled: true
      end

      let(:dashboard2) { FactoryBot.create :dashboard, title: 'A' }
      let!(:dynamic_filter4) do
        FactoryBot.create :dynamic_filter, definition: filter_definition2, filter_holdable: dashboard2,
                                           drillthrough_enabled: true
      end

      let(:field_path5) { DataModeling::Values::FieldPath.new(field_name: 'birthday', model_id: query_data_model.id) }
      let(:filter_source5) { FactoryBot.create :dm_field_filter_source, data_set: data_set, field_path: field_path5 }
      let(:filter_definition5) { FactoryBot.create :dynamic_filter_definition, filter_source: filter_source5 }
      let!(:dynamic_filter5) do
        FactoryBot.create :dynamic_filter, definition: filter_definition5, filter_holdable: dashboard,
                                           drillthrough_enabled: true
      end
      let(:expected_drillthroughs) do
        [
          {
            dashboard_id: dashboard2.id,
            dashboard_title: dashboard2.title,
            field_drillthroughs: [
              {
                field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'age',
                              joins_path: nil, is_metric: false, },
                applied_condition: nil,
                aggregation: nil,
                dynamic_filter_id: dynamic_filter4.id,
              },
            ],
          },
          {
            dashboard_id: dashboard.id,
            dashboard_title: dashboard.title,
            field_drillthroughs: [
              {
                field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'birthday',
                              joins_path: nil, is_metric: false, },
                applied_condition: nil,
                aggregation: nil,
                dynamic_filter_id: dynamic_filter5.id,
              },
              {
                field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'name',
                              joins_path: nil, is_metric: false, },
                applied_condition: nil,
                aggregation: nil,
                dynamic_filter_id: dynamic_filter.id,
              },
              {
                field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'name',
                              joins_path: nil, is_metric: false, },
                applied_condition: nil,
                aggregation: nil,
                dynamic_filter_id: dynamic_filter3.id,
              },
              {
                field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'age',
                              joins_path: nil, is_metric: false, },
                applied_condition: nil,
                aggregation: nil,
                dynamic_filter_id: dynamic_filter2.id,
              },
            ],
          },
        ]
      end

      context 'embed_users' do
        let(:embed_link) do
          el = FactoryBot.create :embed_link, source: dashboard, filter_ownerships: [], version: 3
          el.set_public_user
          el.share_source
          el
        end
        let(:drillthroughs) do
          {}
        end
        let(:embed_payload) do
          {
            drillthroughs: drillthroughs,
          }
        end
        let(:embed_token) do
          sk = embed_link.secret_key
          jwt_encode(sk, embed_payload, Time.now.to_i + 1000)
        end
        let(:headers) do
          {
            PublicLinks::AuthenticationHelper::EMBED_ID_HEADER => embed_link.hash_code,
            PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER => embed_token,
          }
        end
        let(:qr) { FactoryBot.create :query_report, viz_setting: viz_setting, data_set_id: data_set_id }
        let(:dw) { FactoryBot.create :dashboard_widget, dashboard: dashboard, source: qr }
        let(:source_params) { { type: dw.class.name, id: dw.id } }

        before do
          sign_out admin
          request.headers.merge!(headers)
        end

        shared_examples 'drillthrough with embed_user' do
          it 'lists only current source if drillthroughs payload is empty' do
            post :submit_generate, format: :json, params: params
            assert_success_response!
            body = JSON.parse(response.body).rsk
            drillthroughs = body[:drillthroughs]
            expect(drillthroughs).to eq [expected_drillthroughs.second]
          end

          context 'add dashboard2 in drillthoughs payload' do
            let(:drillthroughs) do
              {
                dashboard2.id => {},
              }
            end

            it 'lists all drillthroughs' do
              post :submit_generate, format: :json, params: params
              assert_success_response!
              body = JSON.parse(response.body).rsk
              drillthroughs = body[:drillthroughs]
              expect(drillthroughs).to eq expected_drillthroughs
            end
          end

          context 'no drillthroughs enabled' do
            before do
              dynamic_filter.update!(drillthrough_enabled: false)
              dynamic_filter2.update!(drillthrough_enabled: false)
              dynamic_filter3.update!(drillthrough_enabled: false)
              dynamic_filter4.update!(drillthrough_enabled: false)
              dynamic_filter5.update!(drillthrough_enabled: false)
            end

            it 'returns no drillthrough' do
              post :submit_generate, format: :json, params: params
              assert_success_response!
              body = JSON.parse(response.body).rsk
              drillthroughs = body[:drillthroughs]
              expect(drillthroughs).to eq []
            end
          end
        end

        context 'use v3_configs' do
          before do
            FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, false)
          end

          it_behaves_like 'drillthrough with embed_user'
        end

        context 'use portal configs' do
          before do
            FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, true)
          end

          it_behaves_like 'drillthrough with embed_user'
        end
      end

      it 'lists all possible drillthroughs' do
        post :submit_generate, format: :json, params: params
        assert_success_response!
        body = JSON.parse(response.body).rsk
        drillthroughs = body[:drillthroughs]
        expect(drillthroughs).to eq(
          [
            {
              dashboard_id: dashboard2.id,
              dashboard_title: dashboard2.title,
              field_drillthroughs: [
                {
                  field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'age',
                                joins_path: nil, is_metric: false, },
                  applied_condition: nil,
                  aggregation: nil,
                  dynamic_filter_id: dynamic_filter4.id,
                },
              ],
            },
            {
              dashboard_id: dashboard.id,
              dashboard_title: dashboard.title,
              field_drillthroughs: [
                {
                  field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'birthday',
                                joins_path: nil, is_metric: false, },
                  applied_condition: nil,
                  aggregation: nil,
                  dynamic_filter_id: dynamic_filter5.id,
                },
                {
                  field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'name',
                                joins_path: nil, is_metric: false, },
                  applied_condition: nil,
                  aggregation: nil,
                  dynamic_filter_id: dynamic_filter.id,
                },
                {
                  field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'name',
                                joins_path: nil, is_metric: false, },
                  applied_condition: nil,
                  aggregation: nil,
                  dynamic_filter_id: dynamic_filter3.id,
                },
                {
                  field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'age',
                                joins_path: nil, is_metric: false, },
                  applied_condition: nil,
                  aggregation: nil,
                  dynamic_filter_id: dynamic_filter2.id,
                },
              ],
            },
          ],
        )
      end

      context 'only some destinations can be accessed' do
        before do
          FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
          admin.share(explorer, :read, data_set)
          admin.share(explorer, :read, dashboard2)
          sign_in explorer
        end

        it 'lists the accessible ones only' do
          post :submit_generate, format: :json, params: params
          assert_success_response!
          drillthroughs = JSON.parse(response.body)['drillthroughs'].rsk
          expect(drillthroughs).to eq(
            [
              {
                dashboard_id: dashboard2.id,
                dashboard_title: dashboard2.title,
                field_drillthroughs: [
                  field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'age',
                                joins_path: nil, is_metric: false, },
                  applied_condition: nil,
                  aggregation: nil,
                  dynamic_filter_id: dynamic_filter4.id,
                ],
              },
            ],
          )
        end
      end

      context 'with viz filters' do
        let(:field_path_country) do
          DataModeling::Values::FieldPath.new(field_name: 'country', model_id: query_data_model.id)
        end
        let(:filter_source_country) do
          FactoryBot.create :dm_field_filter_source, data_set: data_set, field_path: field_path_country
        end
        let(:filter_definition_country) do
          FactoryBot.create :dynamic_filter_definition, filter_source: filter_source_country
        end
        let!(:dynamic_filter_country) do
          FactoryBot.create :dynamic_filter, definition: filter_definition_country, filter_holdable: dashboard,
                                             drillthrough_enabled: true
        end

        before do
          viz_setting.filters = [
            {
              path_hash: { field_name: 'country', model_id: query_data_model.id },
              type: 'text',
              operator: 'is',
              values: ['US'],
            },
            {
              path_hash: { field_name: 'country', model_id: query_data_model.id },
              type: 'text',
              operator: 'contains',
              values: ['S'],
            },
          ]
          viz_setting.save!
        end

        it 'includes the filter in drillthrough paths' do
          post :submit_generate, format: :json, params: params
          assert_success_response!
          drillthroughs = JSON.parse(response.body)['drillthroughs'].rsk
          expect(drillthroughs).to eq(
            [
              {
                dashboard_id: dashboard2.id,
                dashboard_title: dashboard2.title,
                field_drillthroughs: [
                  {
                    field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'age',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: nil,
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter4.id,
                  },
                ],
              },
              {
                dashboard_id: dashboard.id,
                dashboard_title: dashboard.title,
                field_drillthroughs: [
                  {
                    field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'country',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: { operator: 'is', values: ['US'] },
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter_country.id,
                  },
                  {
                    field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'country',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: { operator: 'contains', values: ['S'] },
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter_country.id,
                  },
                  {
                    field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'birthday',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: nil,
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter5.id,
                  },
                  {
                    field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'name',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: nil,
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter.id,
                  },
                  {
                    field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'name',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: nil,
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter3.id,
                  },
                  {
                    field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'age',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: nil,
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter2.id,
                  },
                ],
              },
            ],
          )
        end
      end

      context 'multiple models' do
        include_context 'joined_query_models_dataset'
        before do
          viz_setting.fields[:table_fields] = [
            {
              path_hash: { field_name: 'name', model_id: joined_query_data_model.id },
              custom_label: '',
              type: 'text',
              format: { type: 'auto' },
            },
            {
              path_hash: { field_name: 'name', model_id: rm.id },
              custom_label: '',
              type: 'text',
              format: { type: 'auto' },
            },
            {
              path_hash: { field_name: 'name', model_id: rm.id },
              custom_label: '',
              type: 'text',
              format: { type: 'auto' },
            },
            {
              path_hash: { field_name: 'age', model_id: rm.id },
              custom_label: '',
              type: 'number',
              format: { type: 'auto' },
            },
          ]
          viz_setting.save!
        end

        let(:data_set) { query_model_data_set }
        let(:field_path6) do
          DataModeling::Values::FieldPath.new(field_name: 'name', model_id: joined_query_data_model.id)
        end
        let(:filter_source6) { FactoryBot.create :dm_field_filter_source, data_set: data_set, field_path: field_path6 }
        let(:filter_definition6) { FactoryBot.create :dynamic_filter_definition, filter_source: filter_source6 }
        let!(:dynamic_filter6) do
          FactoryBot.create :dynamic_filter, definition: filter_definition6, filter_holdable: dashboard,
                                             drillthrough_enabled: true
        end

        it 'lists drillthroughs correctly' do
          post :submit_generate, format: :json, params: params
          assert_success_response!
          body = JSON.parse(response.body).rsk
          drillthroughs = body[:drillthroughs]
          expect(drillthroughs).to eq(
            [
              {
                dashboard_id: dashboard2.id,
                dashboard_title: dashboard2.title,
                field_drillthroughs: [
                  {
                    field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'age',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: nil,
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter4.id,
                  },
                ],
              },
              {
                dashboard_id: dashboard.id,
                dashboard_title: dashboard.title,
                field_drillthroughs: [
                  {
                    field_path: { data_set_id: data_set.id, model_id: joined_query_data_model.id, field_name: 'name',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: nil,
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter6.id,
                  },
                  {
                    field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'name',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: nil,
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter.id,
                  },
                  {
                    field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'name',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: nil,
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter3.id,
                  },
                  {
                    field_path: { data_set_id: data_set.id, model_id: query_data_model.id, field_name: 'age',
                                  joins_path: nil, is_metric: false, },
                    applied_condition: nil,
                    aggregation: nil,
                    dynamic_filter_id: dynamic_filter2.id,
                  },
                ],
              },
            ],
          )
        end
      end
    end

    context 'running totals' do
      include_context 'data_set'

      let(:viz_setting) do
        FactoryBot.create(
          :viz_setting,
          viz_type: 'line_chart',
          fields: {
            series: {
              type: 'text',
              format: { type: 'text' },
              path_hash: { model_id: products_model.id, field_name: 'status' },
            },
            x_axis: {
              type: 'datetime',
              format: { type: 'date', sub_type: 'mmm yyyy' },
              path_hash: { model_id: products_model.id, field_name: 'created_at' },
              transformation: 'datetrunc month',
            },
            y_axes: [
              {
                columns: [
                  {
                    type: 'auto',
                    color: 'auto',
                    format: { type: 'number', format: {} },
                    path_hash: { model_id: products_model.id, field_name: 'price' },
                    aggregation: 'running sum',
                    custom_label: nil,
                  },
                  {
                    type: 'auto',
                    color: 'auto',
                    format: { type: 'number', format: {} },
                    path_hash: { model_id: products_model.id, field_name: 'price' },
                    aggregation: 'running min',
                    custom_label: nil,
                  },
                  {
                    type: 'auto',
                    color: 'auto',
                    format: { type: 'number', format: {} },
                    path_hash: { model_id: products_model.id, field_name: 'price' },
                    aggregation: 'running max',
                    custom_label: nil,
                  },
                  {
                    type: 'auto',
                    color: 'auto',
                    format: { type: 'number', format: {} },
                    path_hash: { model_id: products_model.id, field_name: 'price' },
                    aggregation: 'running avg',
                    custom_label: nil,
                  },
                ],
              },
            ],
          },
          filters: [],
          format: {},
          settings: {},
        )
      end

      let(:params) do
        {
          viz_setting: viz_setting,
          data_set_id: data_set.id,
          options: options_params,
          source: source_params,
        }
      end

      before do
        FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
        FeatureToggle.toggle_global(::Viz::Constants::FT_PIVOT_V2, true)
        connector = Connectors.from_ds(get_test_ds)
        connector.exec_sql(
          <<~SQL,
            INSERT INTO data_modeling.products(name, merchant_id, category_id, "Price", "Status", "Created At")
            (
              VALUES
                ('fish', 1, 1, 100, 'available',   '2019-01-09T00:00:00Z'),
                ('fish', 1, 1, 200, 'unavailable', '2019-01-09T00:00:00Z'),
                ('fish', 1, 1, 300, 'available',   '2019-02-09T00:00:00Z'),
                ('fish', 1, 1, 400, 'unavailable', '2019-02-09T00:00:00Z'),
                ('fish', 1, 1, 500, 'available',   '2019-03-09T00:00:00Z'), -- march only has available
                ('fish', 1, 1, 600, 'unavailable', '2019-04-09T00:00:00Z'), -- april only has unavailable
                ('fish', 1, 1, 700, 'available',   '2019-05-09T00:00:00Z'),
                ('fish', 1, 1, 800, 'unavailable', '2019-05-09T00:00:00Z'),
                ('fish', 1, 1, 900, 'available',   '2019-08-09T00:00:00Z') -- gap in june and july
            )
          SQL
        )
      end

      it 'is correct' do |ex|
        post :submit_generate, format: :json, params: params
        assert_success_response!

        post :submit_generate, format: :json, params: params
        assert_success_response!

        res = JSON.parse(response.body)
        _, values, meta = res['data']['generated']['result']['data']

        csv = meta['columns'].map { |col| col['original_col_name'] }.to_csv
        csv += values.map(&:to_csv).join
        SnapshotTest.test!(csv, rspec_example: ex, snapshot_name: 'running_total.csv')
      end
    end

    context 'when filter by top N' do
      include_context 'data_set'
      let(:viz_setting) do
        FactoryBot.create(
          :viz_setting,
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                type: 'text',
                path_hash: {
                  field_name: 'name',
                  model_id: products_model.id,
                },
              },
              {
                type: 'text',
                path_hash: {
                  field_name: 'status',
                  model_id: products_model.id,
                },
              },
            ],
          },
          filters: [
            {
              operator: 'top',
              values: [1],
              modifier: { model_id: products_model.id, field_name: 'price', aggregation: 'sum' },
              path_hash: { model_id: products_model.id, field_name: 'name' },
            },
          ],
        )
      end

      let(:params) do
        {
          viz_setting: viz_setting,
          data_set_id: data_set.id,
          options: options_params,
          source: source_params,
        }
      end

      before do
        connector = Connectors.from_ds(get_test_ds)
        connector.exec_sql(
          <<~SQL,
            INSERT INTO data_modeling.products(name, merchant_id, category_id, "Price", "Status", "Created At")
            (
              VALUES
                ('fish', 1, 1, null, 'available', '2019-08-09T00:00:00Z')
            )
          SQL
        )
      end

      it 'corrects' do
        post :submit_generate, format: :json, params: params
        assert_success_response!

        job = Job.find JSON.parse(response.body)['job_id']
        assert_success_job!(job)

        data = Viz::Data::GeneratorService.async_result(job)
        expect(data.values).to eq [%w[egg available]]
      end

      it 'when value is negative' do
        viz_setting.filters[0][:values] = [-1]
        post :submit_generate, format: :json, params: params
        assert_response_status!(422)
        expect(response.body).to match(%r{TOP/BOTTOM condition value must be greater than 0})
      end

      it 'when value is empty' do
        viz_setting.filters[0][:values] = []
        post :submit_generate, format: :json, params: params
        assert_response_status!(422)
        expect(response.body).to match(/Invalid N value/i)
      end

      it 'when modifier is empty' do
        viz_setting.filters[0][:modifier] = {}
        post :submit_generate, format: :json, params: params
        assert_response_status!(422)
        expect(response.body).to match(%r{Missing TOP/BOTTOM N modifier})
      end

      it 'top filter is empty' do
        query_model_sql = <<~SQL
          with t(id, name, children) as (
            values(1, 'alice', 1),
              (2, 'bob', 2),
              (3, 'bob', 1)
          )
          select * from t where id > 3
        SQL
        query_model = create_query_data_model(admin, ds, 'new_sql_model', query_model_sql)
        data_set.data_models << query_model

        viz_setting = FactoryBot.create(
          :viz_setting,
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                type: 'text',
                path_hash: {
                  field_name: 'id',
                  model_id: query_model.id,
                },
              },
              {
                type: 'text',
                path_hash: {
                  field_name: 'name',
                  model_id: query_model.id,
                },
              },
            ],
          },
          filters: [
            {
              operator: 'top',
              values: [1],
              modifier: { model_id: query_model.id, field_name: 'children', aggregation: 'sum' },
              path_hash: { model_id: query_model.id, field_name: 'name' },
            },
          ],
        )

        post :submit_generate, format: :json, params: params.merge(viz_setting: viz_setting)
        job = Job.find JSON.parse(response.body)['job_id']
        assert_success_job!(job)

        data = Viz::Data::GeneratorService.async_result(job)
        expect(data.values.map(&:first)).to eq([])
      end

      it 'excludes null' do
        viz_setting.filters[0][:values] = [5]

        post :submit_generate, format: :json, params: params.merge(viz_setting: viz_setting)
        assert_success_response!

        job = Job.find JSON.parse(response.body)['job_id']
        assert_success_job!(job)

        data = Viz::Data::GeneratorService.async_result(job)
        expect(data.values.map(&:first)).to contain_exactly('bread', 'milk', 'egg', 'bread') # exclude fish
      end
    end

    context 'data model with external fields' do
      before do
        FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
        FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', true)
        request.headers['Content-Type'] = 'application/json'
      end

      include_context 'simple_query_model'

      let(:sql_values) do
        <<~SQL
          values
            ('alice', 2),
            ('alice', 2),
            ('bob', 4)
        SQL
      end
      let(:root_model_id) { data_model.id }
      let(:data_set_id) { nil }
      let(:vs_params) do
        {
          viz_type: 'data_table',
          fields: {
            table_fields: [
              { path_hash: { model_id: data_model.id, field_name: 'name' } },
              { path_hash: { model_id: data_model.id, field_name: 'amount_plus_ten' } },
              { path_hash: { model_id: data_model.id, field_name: 'sum' } },
            ],
          },
          format: {},
          filters: [],
          settings: {},
        }
      end
      let(:query_model_sql) do
        <<~SQL
          with t(name, amount) as (
            #{sql_values}
          )
          select * from t
        SQL
      end
      let(:external_fields) do
        [
          DataModeling::Explores::Dimension.new(
            name: 'amount_plus_ten',
            label: 'Amount Plus Ten',
            sql: '{{ #THIS.amount }} + 10',
            type: 'integer',
            description: '',
          ),
          DataModeling::Explores::Measure.new(
            name: 'sum',
            label: 'Sum',
            sql: '{{ #THIS.amount }}',
            syntax: 'sql',
            type: 'number',
            description: '',
            aggregation_type: 'sum',
          ),
        ]
      end
      let(:data_model) do
        query_data_model
      end

      before do
        data_model.external_fields = external_fields
        data_model.save!
      end

      it 'able to preview custom dimension and measure' do
        post :submit_generate, format: :json, params: params
        assert_success_response!

        job = Job.find JSON.parse(response.body)['job_id']
        assert_success_job!(job)

        data = Viz::Data::GeneratorService.async_result(job)

        expect(data.values).to eq(
          [
            %w[alice 12 4],
            %w[bob 14 4],
          ],
        )
      end
    end

    context 'pgcache model' do
      let!(:job) { FactoryBot.create(:job) }
      let(:sql_values) do
        <<~SQL
          values
            ('alice', '2020-01', 2),
            ('alice', '2020-01', 2),
            ('bob', '2020-01', 4)
        SQL
      end
      let(:sql) do
        <<~SQL
          with t(name, month, amount) as (
            #{sql_values}
          )
          select * from t
        SQL
      end
      let(:cache_col_types) do
        [
          ::PostgresCache::ColType::String,
          ::PostgresCache::ColType::Date,
          ::PostgresCache::ColType::Number,
        ]
      end
      let(:pgcache_model) do
        ThreadContext.reset(:job)
        ThreadContext.set(:async_job, job)
        executor = Queries::Services::ExecuteSql.new(data_source: get_test_ds)
        ServiceDecorators::PostgresCached.new(
          user: get_test_admin,
          bust_cache: true,
          cache_col_types: cache_col_types,
          serialize_mode: ServiceDecorators::PostgresCached::SerializeMode::Serialized,
          model_type: ServiceDecorators::PostgresCached::ModelType::Holistics,
        ).wrap(executor).call(sql)
      end
      let(:root_model_id) { pgcache_model.id }
      let(:data_set_id) { nil }
      let(:pivot_fields) do
        {
          rows: [
            { path_hash: { model_id: 123, field_name: 'name' } },
          ],
          columns: [
            { path_hash: { model_id: 123, field_name: 'month' } },
          ],
          values: [
            # frontend submits empty string for custom_label in measures on pgcache models ¯\_(ツ)_/¯
            { path_hash: { model_id: 123, field_name: 'amount' }, aggregation: 'max', custom_label: '' },
            { path_hash: { field_name: 'amount' }, aggregation: 'min' },
          ],
        }
      end
      let(:other_settings) do
        {
          column_total: true,
        }
      end
      let(:vs_params) do
        {
          viz_type: 'pivot_table',
          fields: {
            pivot_data: pivot_fields,
          },
          format: {},
          filters: [],
          settings: {
            others: other_settings,
          },
        }
      end

      before do
        FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
        FeatureToggle.toggle_global('data_models:sql_generation_gem_on_single_model', true)
        FeatureToggle.toggle_global(::Viz::Constants::FT_PIVOT_V2, true)
        request.headers['Content-Type'] = 'application/json'
      end

      context 'report 2.0 with timezone enabled' do
        include_context 'timezone_setup'
        let(:tz) { 'Asia/Singapore' }

        let(:sql_values) do
          <<~SQL
            values
              ('alice', '2020-01-02 03:04:05+00:00', 2),
              ('alice', '2020-01-03 03:04:05+00:00', 2),
              ('bob', '2020-01-04 03:04:05+00:00', 4)
          SQL
        end
        let(:sql_standalone_report) do
          create :query_report, query: sql
        end
        let(:cache_col_types) do
          [
            ::PostgresCache::ColType::String,
            ::PostgresCache::ColType::Datetime,
            ::PostgresCache::ColType::Number,
          ]
        end
        let(:source_params) do
          { type: sql_standalone_report.class.name, id: sql_standalone_report.id }
        end
        let(:vs_params) do
          {
            viz_type: 'data_table',
            fields: {
              table_fields: [
                { path_hash: { model_id: 123, field_name: 'name' } },
                { path_hash: { model_id: 123, field_name: 'month' } },
                { path_hash: { model_id: 123, field_name: 'amount' } },
              ],
            },
            format: {},
            filters: [
              {
                path_hash: { field_name: 'month' },
                operator: 'matches',
                values: ['2020 to 2021'],
              },
            ],
            settings: {
              others: other_settings,
            },
          }
        end

        it 'does not apply timezone' do
          post :submit_generate, format: :json, params: params
          assert_success_response!

          res = JSON.parse(response.body)

          values = res['data']['generated']['result']['values']
          expect(values).to eq(
            [
              ['alice', '2020-01-02 03:04:05.000000', '2'],
              ['alice', '2020-01-03 03:04:05.000000', '2'],
              ['bob', '2020-01-04 03:04:05.000000', '4'],
            ],
          )
        end
      end

      it 'explores synchronously and returns correct pivot totals' do
        post :submit_generate, format: :json, params: params
        assert_success_response!

        res = JSON.parse(response.body)

        extra_details = res['data']['generated']['extra_details']
        expect(extra_details['explore_opts']['measures'].map { |m| m['rendering_label'] }).to eq(
          [
            'Max of amount',
            'Min of amount',
          ],
        )

        _, values, meta = res['data']['generated']['result']['data']
        expect(values).to eq(
          [
            %w[alice 2 2],
            %w[bob 4 4],
          ],
        )
        expect(meta['column_totals']).to eq(%w[4 2])
      end

      context 'no data' do
        before do
          vs_params[:filters] << {
            path_hash: { field_name: 'name' },
            operator: 'is_null',
            values: [],
          }
        end

        it 'returns correct response' do
          post :submit_generate, format: :json, params: params
          assert_success_response!

          res = JSON.parse(response.body)
          _, values, meta = res['data']['generated']['result']['data']
          expect(values).to eq([])
          expect(meta['num_rows']).to eq 0
        end
      end

      context 'no pivot rows' do
        let(:pivot_fields) do
          {
            rows: [],
            columns: [
              { path_hash: { field_name: 'name' } },
            ],
            values: [
              { path_hash: { field_name: 'amount' }, aggregation: 'min' },
            ],
          }
        end

        it 'returns correct response' do
          post :submit_generate, format: :json, params: params
          assert_success_response!

          res = JSON.parse(response.body)
          _, values, meta = res['data']['generated']['result']['data']
          expect(values).to eq(
            [
              [1, '2', '4'],
            ],
          )
          expect(meta['num_rows']).to eq 1
        end

        context 'no data' do
          before do
            vs_params[:filters] << {
              path_hash: { field_name: 'name' },
              operator: 'is_null',
              values: [],
            }
          end

          it 'returns correct response' do
            post :submit_generate, format: :json, params: params
            assert_success_response!

            res = JSON.parse(response.body)
            _, values, meta = res['data']['generated']['result']['data']
            expect(values).to eq([])
            expect(meta['num_rows']).to eq 0
          end
        end
      end

      describe 'sorting on pivot values' do
        let(:sql) do
          <<~SQL
            with t(country, city, month, amount) as (
              values
                ('vietnam', 'hcm', '2020-02', -4),
                ('singapore', 'singapore', '2020-01', 1),
                (null, 'singapore', '2020-01', 2),
                ('vietnam', 'hn', '2020-01', 8),
                ('vietnam', 'hcm', '2020-01', -2),
                ('vietnam', 'hn', '2020-02', -24),
                ('vietnam', 'dn', '2020-01', -4),
                ('vietnam', 'dn', '2020-02', -2),
                ('singapore', 'singapore', '2020-02', -9),
                ('england', 'london', '2020-01', 10),
                ('england', 'london', '2020-02', -20)
            )
            select * from t
          SQL
        end

        let(:cache_col_types) do
          [
            ::PostgresCache::ColType::String,
            ::PostgresCache::ColType::String,
            ::PostgresCache::ColType::String,
            ::PostgresCache::ColType::Number,
          ]
        end

        let(:pivot_fields) do
          {
            rows: [
              { path_hash: { field_name: 'country' } },
              { path_hash: { field_name: 'city' } },
            ],
            columns: [
              { path_hash: { field_name: 'month' } },
            ],
            values: [
              { path_hash: { field_name: 'amount' }, aggregation: 'avg' },
            ],
          }
        end

        before do
          options_params.merge!(
            sort: '3_desc',
          )
        end

        context 'with no totals' do
          let(:other_settings) do
            {}
          end

          it 'sorts by individual row values' do |ex|
            expect(::Utils).to receive(:add_limit_to_query).and_wrap_original do |m, *args, **kwargs|
              SnapshotTest.test!(args[0], rspec_example: ex, snapshot_name: 'pgcache_model_pivot_with_no_totals.sql')
              m.call(*args, **kwargs)
            end

            post :submit_generate, format: :json, params: params
            assert_success_response!

            res = JSON.parse(response.body)

            _, values = res['data']['generated']['result']['data']
            pretty_test!(
              values,
              [
                # 10 > 8 > 1
                ['england', 'london', '10.0000000000000000', '-20.0000000000000000'],
                ['vietnam', 'hn', '8.0000000000000000', '-24.0000000000000000'],
                # dn and hcm are placed before singapore, because they are in same category with hn (vietnam), although their values are smaller than singapore (1)
                ['vietnam', 'dn', '-4.0000000000000000', '-2.0000000000000000'], # dn is placed before hcm because dn > hcm (default asc order)
                ['vietnam', 'hcm', '-2.0000000000000000', '-4.0000000000000000'],
                [nil, 'singapore', '2.0000000000000000', nil],
                ['singapore', 'singapore', '1.00000000000000000000', '-9.0000000000000000'],
              ],
            )
          end

          context 'with lots of fields' do
            let(:sql) do
              values = <<~SQL
                ('vietnam', 'hcm', '2020-02', -4),
                ('singapore', 'singapore', '2020-01', 1),
                ('vietnam', 'hn', '2020-01', 8),
                ('vietnam', 'hcm', '2020-01', -2),
                ('vietnam', 'hn', '2020-02', -24),
                ('vietnam', 'dn', '2020-01', -4),
                ('vietnam', 'dn', '2020-02', -2),
                ('singapore', 'singapore', '2020-02', -9),
                ('england', 'london', '2020-01', 10),
                ('england', 'london', '2020-02', -20)
              SQL
              values_sql = 'values' + 100_000.times.map do
                values
              end.join(",\n")
              <<~SQL
                with t(country, city, month, amount) as (
                  #{values_sql}
                )
                select * from t
              SQL
            end

            let(:pivot_fields) do
              {
                rows: [
                  { path_hash: { field_name: 'country' } },
                ] * 10,
                columns: [
                  { path_hash: { field_name: 'month' } },
                ] * 10,
                values: [
                  { path_hash: { field_name: 'amount' }, aggregation: 'avg' },
                ] * 10,
              }
            end

            it 'runs fast' do
              skip_on_circleci!
              params # trigger lazy variables
              t = Benchmark.realtime do
                post :submit_generate, format: :json, params: params
                assert_success_response!
              end
              puts "Took #{t * 1000}ms".yellow
            end
          end

          context 'multiple sortings 1' do
            before do
              options_params.merge!(
                sort: [
                  {
                    field_index: 0,
                    order_direction: 'desc',
                  },
                  {
                    field_index: 3,
                    order_direction: 'asc',
                  },
                  {
                    field_index: 1,
                    order_direction: 'desc',
                  },
                ],
              )
            end

            it 'sorts correctly following order of sortings' do
              post :submit_generate, format: :json, params: params
              assert_success_response!

              res = JSON.parse(response.body)

              _, values = res['data']['generated']['result']['data']
              pretty_test!(
                values,
                [
                  # nil > vietnam > singapore > england
                  # -24 < -2
                  # hcm > dn
                  [nil, 'singapore', '2.0000000000000000', nil],
                  ['vietnam', 'hn', '8.0000000000000000', '-24.0000000000000000'],
                  ['vietnam', 'hcm', '-2.0000000000000000', '-4.0000000000000000'],
                  ['vietnam', 'dn', '-4.0000000000000000', '-2.0000000000000000'],
                  ['singapore', 'singapore', '1.00000000000000000000', '-9.0000000000000000'],
                  ['england', 'london', '10.0000000000000000', '-20.0000000000000000'],
                ],
              )
            end
          end

          context 'multiple sortings 2' do
            before do
              options_params.merge!(
                sort: [
                  {
                    field_index: 0,
                    order_direction: 'desc',
                  },
                  {
                    field_index: 3,
                    order_direction: 'asc',
                  },
                  {
                    field_index: 1,
                    order_direction: 'asc',
                  },
                ],
              )
            end

            it 'sorts correctly following order of sortings' do
              post :submit_generate, format: :json, params: params
              assert_success_response!

              res = JSON.parse(response.body)

              _, values = res['data']['generated']['result']['data']
              pretty_test!(
                values,
                [
                  # nil > vietnam > singapore > england
                  # -24 < -2
                  # dn < hcm
                  [nil, 'singapore', '2.0000000000000000', nil],
                  ['vietnam', 'hn', '8.0000000000000000', '-24.0000000000000000'],
                  ['vietnam', 'dn', '-4.0000000000000000', '-2.0000000000000000'],
                  ['vietnam', 'hcm', '-2.0000000000000000', '-4.0000000000000000'],
                  ['singapore', 'singapore', '1.00000000000000000000', '-9.0000000000000000'],
                  ['england', 'london', '10.0000000000000000', '-20.0000000000000000'],
                ],
              )
            end
          end

          context 'multiple sortings 3' do
            let(:sql) do
              <<~SQL
                with t(country, city, month, amount) as (
                  values
                    ('vietnam', 'hcm', '2020-02', -4),
                    ('singapore', 'hn', '2020-01', 1),
                    ('vietnam', 'hn', '2020-01', 8),
                    ('vietnam', 'hcm', '2020-01', -2),
                    ('vietnam', 'hn', '2020-02', -24),
                    ('vietnam', 'dn', '2020-01', -4),
                    ('vietnam', 'dn', '2020-02', -2),
                    ('singapore', 'hn', '2020-02', -9),
                    ('england', 'hcm', '2020-01', 10),
                    ('england', 'hcm', '2020-02', -20)
                )
                select * from t
              SQL
            end

            before do
              options_params.merge!(
                sort: [
                  {
                    field_index: 1,
                    order_direction: 'desc',
                  },
                  {
                    field_index: 0,
                    order_direction: 'asc',
                  },
                ],
              )
            end

            it 'sorts correctly following order of sortings' do
              post :submit_generate, format: :json, params: params
              assert_success_response!

              res = JSON.parse(response.body)

              _, values = res['data']['generated']['result']['data']
              pretty_test!(
                values,
                [
                  # hn > hcm
                  # singapore < vietnam
                  ['singapore', 'hn', '1.00000000000000000000', '-9.0000000000000000'],
                  ['vietnam', 'hn', '8.0000000000000000', '-24.0000000000000000'],
                  ['vietnam', 'hcm', '-2.0000000000000000', '-4.0000000000000000'],
                  ['vietnam', 'dn', '-4.0000000000000000', '-2.0000000000000000'],
                  ['england', 'hcm', '10.0000000000000000', '-20.0000000000000000'],
                ],
              )
            end

            context 'multiple sortings 4' do
              before do
                options_params.merge!(
                  sort: [
                    {
                      field_index: 1,
                      order_direction: 'desc',
                    },
                    {
                      field_index: 0,
                      order_direction: 'desc',
                    },
                  ],
                )
              end

              shared_examples 'sorts correctly following order of sortings' do
                it 'sorts correctly following order of sortings' do
                  post :submit_generate, format: :json, params: params
                  assert_success_response!

                  res = JSON.parse(response.body)

                  _, values = res['data']['generated']['result']['data']
                  pretty_test!(
                    values,
                    [
                      # hn > hcm > dn
                      # vietnam > singapore
                      ['vietnam', 'hn', '8.0000000000000000', '-24.0000000000000000'],
                      ['vietnam', 'hcm', '-2.0000000000000000', '-4.0000000000000000'],
                      ['vietnam', 'dn', '-4.0000000000000000', '-2.0000000000000000'],
                      ['singapore', 'hn', '1.00000000000000000000', '-9.0000000000000000'],
                      ['england', 'hcm', '10.0000000000000000', '-20.0000000000000000'],
                    ],
                  )
                end
              end

              it_behaves_like 'sorts correctly following order of sortings'

              context 'with sort before limit enabled' do
                before do
                  FeatureToggle.toggle_global(Viz::Constants::FT_SORT_BEFORE_LIMIT, true)
                end

                context 'sorts submitted via options' do
                  it 'fallbacks to DEFAULT sorting' do
                    post :submit_generate, format: :json, params: params
                    assert_success_response!

                    res = JSON.parse(response.body)

                    _, values = res['data']['generated']['result']['data']
                    pretty_test!(
                      values,
                      [
                        # england < singapore < vietnam
                        # dn < hcm < hn
                        ['england', 'hcm', '10.0000000000000000', '-20.0000000000000000'],
                        ['singapore', 'hn', '1.00000000000000000000', '-9.0000000000000000'],
                        ['vietnam', 'dn', '-4.0000000000000000', '-2.0000000000000000'],
                        ['vietnam', 'hcm', '-2.0000000000000000', '-4.0000000000000000'],
                        ['vietnam', 'hn', '8.0000000000000000', '-24.0000000000000000'],
                      ],
                    )
                  end
                end

                context 'sorts submitted via setting, not options' do
                  before do
                    options_params.merge!(
                      sort: [],
                    )
                    vs_params[:settings][:sort] = [
                      {
                        column: 1,
                        sortOrder: false,
                      },
                      {
                        column: 0,
                        sortOrder: false,
                      },
                    ]
                  end

                  it_behaves_like 'sorts correctly following order of sortings'
                end
              end
            end
          end
        end

        context 'with row totals, no sub totals' do
          let(:other_settings) do
            {
              row_total: true,
            }
          end

          it 'sorts by row totals' do
            post :submit_generate, format: :json, params: params
            assert_success_response!

            res = JSON.parse(response.body)

            _, values, meta = res['data']['generated']['result']['data']
            pretty_test!(
              values,
              [
                # 2 > -3 > -4 > -5
                [nil, 'singapore', '2.0000000000000000', nil, '2.0000000000000000'],
                ['vietnam', 'dn', '-4.0000000000000000', '-2.0000000000000000', '-3.0000000000000000'],
                ['vietnam', 'hcm', '-2.0000000000000000', '-4.0000000000000000', '-3.0000000000000000'],
                ['vietnam', 'hn', '8.0000000000000000', '-24.0000000000000000', '-8.0000000000000000'],
                ['singapore', 'singapore', '1.00000000000000000000', '-9.0000000000000000', '-4.0000000000000000'],
                ['england', 'london', '10.0000000000000000', '-20.0000000000000000', '-5.0000000000000000'],
              ],
            )
            expect(meta['num_rows']).to eq 6
          end

          context 'row dimension contains NULLs' do
            let(:sql) do
              <<~SQL
                with t(country, city, month, amount) as (
                  values
                    ('vietnam', 'hcm', '2020-02', -4),
                    ('singapore', 'singapore', '2020-01', 1),
                    (null, 'singapore', '2020-01', 2),
                    ('vietnam', 'hn', '2020-01', 8),
                    ('vietnam', 'hcm', '2020-01', -2),
                    ('vietnam', 'hn', '2020-02', -24),
                    ('vietnam', null, '2020-01', -4),
                    ('vietnam', null, '2020-02', -2),
                    ('singapore', 'singapore', '2020-02', -9),
                    ('england', null, '2020-01', 10),
                    ('england', null, '2020-02', -20)
                )
                select * from t
              SQL
            end

            it 'still sorts and counts rows correctly' do
              post :submit_generate, format: :json, params: params
              assert_success_response!

              res = JSON.parse(response.body)

              _, values, meta = res['data']['generated']['result']['data']
              pretty_test!(
                values,
                [
                  # 2 > -3 > -4 > -5
                  [nil, 'singapore', '2.0000000000000000', nil, '2.0000000000000000'],
                  # -3 = -3 but 'hcm' < NULL in ASC (auto default dimension order)
                  ['vietnam', 'hcm', '-2.0000000000000000', '-4.0000000000000000', '-3.0000000000000000'],
                  ['vietnam', nil, '-4.0000000000000000', '-2.0000000000000000', '-3.0000000000000000'],
                  ['vietnam', 'hn', '8.0000000000000000', '-24.0000000000000000', '-8.0000000000000000'],
                  ['singapore', 'singapore', '1.00000000000000000000', '-9.0000000000000000', '-4.0000000000000000'],
                  ['england', nil, '10.0000000000000000', '-20.0000000000000000', '-5.0000000000000000'],
                ],
              )
              expect(meta['num_rows']).to eq 6
            end
          end
        end

        context 'with row sub totals' do
          let(:other_settings) do
            {
              row_total: true,
              sub_total: true,
            }
          end

          it 'sorts by category totals' do
            post :submit_generate, format: :json, params: params
            assert_success_response!

            res = JSON.parse(response.body)

            _, values, meta = res['data']['generated']['result']['data']
            pretty_test!(
              values,
              [
                # 2 > -4
                [nil, '_holistics_sub_total_', '2.0000000000000000', nil, '2.0000000000000000'],
                [nil, 'singapore', '2.0000000000000000', nil, '2.0000000000000000'],
                # -4 > -4.67
                ['singapore', '_holistics_sub_total_', '1.00000000000000000000', '-9.0000000000000000',
                 '-4.0000000000000000',],
                ['singapore', 'singapore', '1.00000000000000000000', '-9.0000000000000000', '-4.0000000000000000'],
                ['vietnam', '_holistics_sub_total_', '0.66666666666666666667', '-10.0000000000000000',
                 '-4.6666666666666667',],
                ['vietnam', 'dn', '-4.0000000000000000', '-2.0000000000000000', '-3.0000000000000000'],
                ['vietnam', 'hcm', '-2.0000000000000000', '-4.0000000000000000', '-3.0000000000000000'],
                ['vietnam', 'hn', '8.0000000000000000', '-24.0000000000000000', '-8.0000000000000000'],
                ['england', '_holistics_sub_total_', '10.0000000000000000', '-20.0000000000000000',
                 '-5.0000000000000000',],
                ['england', 'london', '10.0000000000000000', '-20.0000000000000000', '-5.0000000000000000'],
              ],
            )
            expect(meta['num_rows']).to eq 10
          end

          context 'row dimension contains NULLs' do
            let(:sql) do
              <<~SQL
                with t(country, city, month, amount) as (
                  values
                    ('vietnam', 'hcm', '2020-02', -4),
                    ('singapore', 'singapore', '2020-01', 1),
                    (null, 'singapore', '2020-01', 2),
                    ('vietnam', 'hn', '2020-01', 8),
                    ('vietnam', 'hcm', '2020-01', -2),
                    ('vietnam', 'hn', '2020-02', -24),
                    ('vietnam', null, '2020-01', -4),
                    ('vietnam', null, '2020-02', -2),
                    ('singapore', 'singapore', '2020-02', -9),
                    ('england', null, '2020-01', 10),
                    ('england', null, '2020-02', -20)
                )
                select * from t
              SQL
            end

            it 'still sorts and counts rows correctly' do
              post :submit_generate, format: :json, params: params
              assert_success_response!

              res = JSON.parse(response.body)

              _, values, meta = res['data']['generated']['result']['data']
              pretty_test!(
                values,
                [
                  # 2 > -4
                  [nil, '_holistics_sub_total_', '2.0000000000000000', nil, '2.0000000000000000'],
                  [nil, 'singapore', '2.0000000000000000', nil, '2.0000000000000000'],
                  # -4 > -4.67
                  ['singapore', '_holistics_sub_total_', '1.00000000000000000000', '-9.0000000000000000',
                   '-4.0000000000000000',],
                  ['singapore', 'singapore', '1.00000000000000000000', '-9.0000000000000000', '-4.0000000000000000'],
                  ['vietnam', '_holistics_sub_total_', '0.66666666666666666667', '-10.0000000000000000',
                   '-4.6666666666666667',],
                  # -3 = -3 but 'hcm' < NULL in ASC (auto default dimension order)
                  ['vietnam', 'hcm', '-2.0000000000000000', '-4.0000000000000000', '-3.0000000000000000'],
                  ['vietnam', nil, '-4.0000000000000000', '-2.0000000000000000', '-3.0000000000000000'],
                  ['vietnam', 'hn', '8.0000000000000000', '-24.0000000000000000', '-8.0000000000000000'],
                  ['england', '_holistics_sub_total_', '10.0000000000000000', '-20.0000000000000000',
                   '-5.0000000000000000',],
                  ['england', nil, '10.0000000000000000', '-20.0000000000000000', '-5.0000000000000000'],
                ],
              )
              expect(meta['num_rows']).to eq 10
            end
          end

          context 'sort by dimension' do
            before do
              options_params.merge!(
                sort: '1_desc',
              )
            end

            it 'is not affected by total headers' do
              post :submit_generate, format: :json, params: params
              assert_success_response!

              res = JSON.parse(response.body)

              _, values, meta = res['data']['generated']['result']['data']
              pretty_test!(
                values,
                [
                  # singapore > london > hn (sort field index 1 DESC)
                  #   then singapore < nil (default ASC dimension sorting on field index 0)
                  ['singapore', '_holistics_sub_total_', '1.00000000000000000000', '-9.0000000000000000',
                   '-4.0000000000000000',],
                  ['singapore', 'singapore', '1.00000000000000000000', '-9.0000000000000000', '-4.0000000000000000'],
                  [nil, '_holistics_sub_total_', '2.0000000000000000', nil, '2.0000000000000000'],
                  [nil, 'singapore', '2.0000000000000000', nil, '2.0000000000000000'],
                  ['england', '_holistics_sub_total_', '10.0000000000000000', '-20.0000000000000000',
                   '-5.0000000000000000',],
                  ['england', 'london', '10.0000000000000000', '-20.0000000000000000', '-5.0000000000000000'],
                  ['vietnam', '_holistics_sub_total_', '0.66666666666666666667', '-10.0000000000000000',
                   '-4.6666666666666667',],
                  ['vietnam', 'hn', '8.0000000000000000', '-24.0000000000000000', '-8.0000000000000000'],
                  ['vietnam', 'hcm', '-2.0000000000000000', '-4.0000000000000000', '-3.0000000000000000'],
                  ['vietnam', 'dn', '-4.0000000000000000', '-2.0000000000000000', '-3.0000000000000000'],
                ],
              )
              expect(meta['num_rows']).to eq 10
            end
          end
        end
      end

      describe 'sorting on pivot columns' do
        let(:sql) do
          <<~SQL
            with t(country, city, month, amount) as (
              values
                ('vietnam', 'hcm', '02', 2),
                ('vietnam', 'dn', '03', 4),
                ('zzz', 'aa', '03', 8),
                ('vietnam', 'hcm', '04', 1)
            )
            select * from t
          SQL
        end

        let(:cache_col_types) do
          [
            ::PostgresCache::ColType::String,
            ::PostgresCache::ColType::String,
            ::PostgresCache::ColType::String,
            ::PostgresCache::ColType::Number,
          ]
        end

        let(:pivot_fields) do
          {
            rows: [
              { path_hash: { field_name: 'country' } },
            ],
            columns: [
              { path_hash: { field_name: 'month' } },
              { path_hash: { field_name: 'city' } },
            ],
            values: [
              { path_hash: { field_name: 'amount' }, aggregation: 'max' },
            ],
          }
        end

        before do
          options_params.merge!(
            sort: [
              {
                field_index: 2,
                order_direction: 'asc',
              },
              {
                field_index: 1,
                order_direction: 'asc',
              },
            ],
          )
        end

        it 'sorts correctly by column' do
          post :submit_generate, format: :json, params: params
          assert_success_response!

          res = JSON.parse(response.body)

          _, values = res['data']['generated']['result']['data']
          pretty_test!(
            values,
            [
              # vietnam < zzz (default row sorting)
              # aa < hcm
              # 02 < 04
              ['vietnam', nil, '4', '2', '1'],
              ['zzz', '8', nil, nil, nil],
            ],
          )
        end

        context 'with sort before limit enabled' do
          before do
            FeatureToggle.toggle_global(Viz::Constants::FT_SORT_BEFORE_LIMIT, true)
          end

          context 'sorts submitted via setting, not options' do
            before do
              options_params.merge!(
                sort: [],
              )
              vs_params[:settings][:sort] = [
                {
                  field_index: 2,
                  order_direction: 'asc',
                },
                {
                  field_index: 1,
                  order_direction: 'asc',
                },
              ]
              vs_params[:settings][:misc] = {
                row_limit: 1,
              }
            end

            it 'applies limit correctly' do
              post :submit_generate, format: :json, params: params
              assert_success_response!

              res = JSON.parse(response.body)

              _, values = res['data']['generated']['result']['data']
              pretty_test!(
                values,
                [
                  ['vietnam', nil, '4', '2', '1'],
                ],
              )
            end
          end
        end
      end

      context 'viz_setting uses full_path' do
        let(:vs_params) do
          {
            viz_type: 'pivot_table',
            fields: {
              pivot_data: {
                rows: [
                  { full_path: '0' },
                ],
                columns: [
                  { full_path: '1' },
                ],
                values: [
                  { full_path: '2', aggregation: 'min' },
                ],
              },
            },
            format: {},
            filters: [],
            settings: {
              others: {
                column_total: true,
              },
            },
          }
        end

        it 'raises informative error' do
          post :submit_generate, format: :json, params: params
          assert_response_status!(422)
          expect(response.body).to match(/legacy settings/)
        end
      end

      context 'data_table with no table_fields' do
        let(:vs_params) do
          {
            viz_type: 'data_table',
            fields: {
              table_fields: [],
            },
            format: {
              name: {
                type: 'string',
                sub_type: 'auto',
              },
              month: {
                type: 'date',
                sub_type: 'auto',
              },
              num: {
                type: 'auto',
              },
            },
            filters: [],
            settings: {},
          }
        end

        it 'includes all model fields in the result' do
          post :submit_generate, format: :json, params: params
          assert_success_response!

          res = JSON.parse(response.body)

          values = res['data']['generated']['result']['values']
          expect(values).to eq(
            [
              ['alice', '2020-01-01', '2'],
              ['alice', '2020-01-01', '2'],
              ['bob', '2020-01-01', '4'],
            ],
          )
          fields = res['data']['generated']['extra_details']['fields']
          expect(fields[0]['format'].symbolize_keys).to eq({ type: 'string', sub_type: 'auto' })
          expect(fields[1]['format'].symbolize_keys).to eq({ type: 'date', sub_type: 'auto' })
          expect(fields[2]['format'].symbolize_keys).to eq({ type: 'auto' })
        end
      end

      context 'empty pivot table' do
        let(:vs_params) do
          {
            viz_type: 'pivot_table',
            fields: {
              pivot_data: {
                rows: [],
                columns: [],
                values: [],
              },
            },
            format: {},
            filters: [],
            settings: {},
          }
        end

        it 'returns error normally' do
          post :submit_generate, format: :json, params: params
          assert_response_status!(422)

          expect(response.body).to match(/Empty result set/i)
        end
      end

      context 'field being guessed as text but formatted as date' do
        let(:sql_values) do
          <<~SQL
            values
              ('alice', '2020-01-01', '2'),
              ('alice', '2020-01-01', '2'),
              ('bob', '2020-01-01', '1')
          SQL
        end
        let(:cache_col_types) do
          [
            ::PostgresCache::ColType::String,
            ::PostgresCache::ColType::String,
            ::PostgresCache::ColType::String,
          ]
        end
        let(:pivot_fields) do
          {
            rows: [
              { path_hash: { model_id: 123, field_name: 'name' } },
            ],
            columns: [
              { path_hash: { model_id: 123, field_name: 'month' } },
              { path_hash: { model_id: 123, field_name: 'month' }, transformation: 'datetrunc month',
                format: { type: 'date', sub_type: 'mmm yyyy' }, },
              { path_hash: { model_id: 123, field_name: 'month' }, transformation: 'datetrunc day',
                format: { type: 'timestamp' }, },
              { path_hash: { model_id: 123, field_name: 'month' } },
            ],
            values: [
              { path_hash: { model_id: 123, field_name: 'amount' }, aggregation: 'sum', format: { type: 'auto' } },
              { path_hash: { model_id: 123, field_name: 'name' }, aggregation: 'count', format: { type: 'number' } },
            ],
          }
        end
        let(:other_settings) do
          {
            column_total: true,
            row_total: true,
          }
        end

        it 'works fine' do |ex|
          post :submit_generate, format: :json, params: params
          assert_success_response!

          res = JSON.parse(response.body)

          _, values, meta = res['data']['generated']['result']['data']
          expect(values).to eq(
            [
              %w[alice 4 2 4 2],
              %w[bob 1 1 1 1],
            ],
          )
          expect(meta['column_totals']).to eq(%w[5 3 5 3])

          data_set, viz_setting = Viz::Data::WrapModelInDataSet.new.call(pgcache_model, VizSetting.new(vs_params))
          _, root_model = Viz::Data::V3::FieldResolver.new(
            viz_setting: viz_setting,
            data_set: data_set,
            current_user: admin,
            permission_rules: DataModeling::Values::PermissionRules::Object.new,
            query_processing_timezone: '',
          ).call
          SnapshotTest.test!(root_model.to_sql, rspec_example: ex,
                                                snapshot_name: 'sql_gen_gem_single_model_multiple_col_types.sql',)
        end
      end

      context 'with week_start_day' do
        before do
          pivot_fields[:columns][0][:transformation] = 'datetrunc week'
          vs_params[:settings][:week_start_day] = 'friday'
        end

        it 'explores correctly' do
          post :submit_generate, format: :json, params: params
          assert_success_response!

          res = JSON.parse(response.body)

          extra_details = res['data']['generated']['extra_details']
          expect(extra_details['explore_opts']['measures'].map { |m| m['rendering_label'] }).to eq(
            [
              'Max of amount',
              'Min of amount',
            ],
          )

          _, values, meta = res['data']['generated']['result']['data']
          expect(values).to eq(
            [
              %w[alice 2 2],
              %w[bob 4 4],
            ],
          )
          expect(meta['column_totals']).to eq(%w[4 2])
        end
      end

      context 'pop' do
        let(:sql) do
          <<~SQL
            with t(country, city, created_at, amount) as (
              values
                ('vietnam', 'hcm', '2020-02-03', -4),
                ('singapore', 'singapore', '2020-01-03', 1),
                ('vietnam', 'hn', '2020-01-19', 8),
                ('vietnam', 'hcm', '2020-01-23', -2),
                ('vietnam', 'hn', '2020-02-23', -24),
                ('vietnam', 'dn', '2020-01-04', -4),
                ('vietnam', 'dn', '2020-02-12', -2),
                ('singapore', 'singapore', '2020-02-22', -9),
                ('england', 'london', '2020-01-11', 10),
                ('england', 'london', '2020-02-09', -20)
            )
            select country, city, created_at::date, amount from t
          SQL
        end

        let(:cache_col_types) do
          [
            ::PostgresCache::ColType::String,
            ::PostgresCache::ColType::String,
            ::PostgresCache::ColType::Datetime,
            ::PostgresCache::ColType::Number,
          ]
        end

        let(:pivot_fields) do
          {
            rows: [
              {
                path_hash: { field_name: 'created_at' },
                transformation: 'datetrunc month',
                format: { type: 'date', sub_type: 'mmm yyyy' },
              },
              {
                path_hash: { field_name: 'created_at' },
                transformation: 'datetrunc year',
                format: { type: 'timestamp' },
              },
              {
                path_hash: { field_name: 'created_at' },
                transformation: 'datetrunc year',
                format: { type: 'date', sub_type: 'yyyy' },
              },
            ],
            columns: [],
            values: [
              { path_hash: { field_name: 'amount' }, aggregation: 'sum' },
            ],
          }
        end

        before do
          vs_params[:settings] = {
            pop_settings: {
              type: 'relative',
              field: {
                type: 'datetime',
                format: { type: 'date', sub_type: 'mmm yyyy' },
                path_hash: { field_name: 'created_at' },
              },
              offset: 1,
              period: 'month',
              # condition: { values: [], operator: 'between' },
            },
          }
        end

        it 'performs PoP on datetime' do
          post :submit_generate, format: :json, params: params
          assert_success_response!

          res = JSON.parse(response.body)

          _, values = res['data']['generated']['result']['data']
          expect(values[0][3..]).to eq(['13', nil])
          expect(values[0][0]).to match(/^2020-01-01T00:00:00/)
          expect(values[0][1]).to match(/^2020-01-01T00:00:00/)
          expect(values[0][2]).to match(/^2020-01-01T00:00:00/)
          expect(values[1][3..]).to eq(['-59', '13'])
          expect(values[1][0]).to match(/^2020-02-01T00:00:00/)
          expect(values[1][1]).to match(/^2020-01-01T00:00:00/)
          expect(values[1][1]).to match(/^2020-01-01T00:00:00/)
        end

        context 'field is guessed as string' do
          let(:cache_col_types) do
            [
              ::PostgresCache::ColType::String,
              ::PostgresCache::ColType::String,
              ::PostgresCache::ColType::String,
              ::PostgresCache::ColType::Number,
            ]
          end

          it 'performs PoP on datetime' do
            post :submit_generate, format: :json, params: params
            assert_success_response!

            res = JSON.parse(response.body)

            _, values = res['data']['generated']['result']['data']
            expect(values[0][3..]).to eq(['13', nil])
            expect(values[0][0]).to match(/^2020-01-01T00:00:00/)
            expect(values[0][1]).to match(/^2020-01-01T00:00:00/)
            expect(values[0][2]).to match(/^2020-01-01T00:00:00/)
            expect(values[1][3..]).to eq(['-59', '13'])
            expect(values[1][0]).to match(/^2020-02-01T00:00:00/)
            expect(values[1][1]).to match(/^2020-01-01T00:00:00/)
            expect(values[1][1]).to match(/^2020-01-01T00:00:00/)
          end
        end

        context 'field is guessed as date' do
          let(:cache_col_types) do
            [
              ::PostgresCache::ColType::String,
              ::PostgresCache::ColType::String,
              ::PostgresCache::ColType::Date,
              ::PostgresCache::ColType::Number,
            ]
          end

          it 'performs PoP on datetime' do
            post :submit_generate, format: :json, params: params
            assert_success_response!

            res = JSON.parse(response.body)

            _, values = res['data']['generated']['result']['data']
            expect(values[0][3..]).to eq(['13', nil])
            expect(values[0][0]).to match(/^2020-01-01T00:00:00/)
            expect(values[0][1]).to match(/^2020-01-01T00:00:00/)
            expect(values[0][2]).to match(/^2020-01-01T00:00:00/)
            expect(values[1][3..]).to eq(['-59', '13'])
            expect(values[1][0]).to match(/^2020-02-01T00:00:00/)
            expect(values[1][1]).to match(/^2020-01-01T00:00:00/)
            expect(values[1][1]).to match(/^2020-01-01T00:00:00/)
          end

          context 'with time condition', otel: true, type: :request do
            before do
              vs_params[:filters] << {
                operator: 'matches',
                values: ['2020-02-02 - 2020-03-01'],
                path_hash: { field_name: 'created_at' },
              }
            end

            it 'returns last period data, even though the last period data does not match the condition' do |ex|
              post '/viz_data/submit_generate', as: :json, params: params

              assert_success_response!

              res = JSON.parse(response.body)

              _, values = res['data']['generated']['result']['data']
              expect(values[0][3..]).to eq(['-59', '13'])
              expect(values[0][0]).to match(/^2020-02-01T00:00:00/)
              expect(values[0][1]).to match(/^2020-01-01T00:00:00/)
              expect(values[0][1]).to match(/^2020-01-01T00:00:00/)

              exec_sql_span = otel_finished_spans.find do |s|
                s.name == '#<Class:PostgresCache>#set_by_sql_from_pg_cache'
              end
              SnapshotTest.test!(
                exec_sql_span.attributes['h.sql'],
                rspec_example: ex,
                snapshot_name: 'single_model_pop_on_date_field_with_filter.sql',
              )
            end
          end
        end

        context 'field is formatted as date' do
          let(:pivot_fields) do
            {
              rows: [
                {
                  path_hash: { field_name: 'created_at' },
                  transformation: 'datetrunc month',
                  format: { type: 'date', sub_type: 'mmm yyyy' },
                },
              ],
              columns: [],
              values: [
                { path_hash: { field_name: 'amount' }, aggregation: 'sum' },
              ],
            }
          end

          it 'performs PoP on datetime' do
            post :submit_generate, format: :json, params: params
            assert_success_response!

            res = JSON.parse(response.body)

            _, values = res['data']['generated']['result']['data']
            expect(values[0][1..]).to eq(['13', nil])
            expect(values[0][0]).to match(/^2020-01-01T00:00:00/)
            expect(values[1][1..]).to eq(['-59', '13'])
            expect(values[1][0]).to match(/^2020-02-01T00:00:00/)
          end

          context 'field is guessed as string' do
            let(:cache_col_types) do
              [
                ::PostgresCache::ColType::String,
                ::PostgresCache::ColType::String,
                ::PostgresCache::ColType::String,
                ::PostgresCache::ColType::Number,
              ]
            end

            it 'performs PoP on datetime' do
              post :submit_generate, format: :json, params: params
              assert_success_response!

              res = JSON.parse(response.body)

              _, values = res['data']['generated']['result']['data']
              expect(values[0][1..]).to eq(['13', nil])
              expect(values[0][0]).to match(/^2020-01-01T00:00:00/)
              expect(values[1][1..]).to eq(['-59', '13'])
              expect(values[1][0]).to match(/^2020-02-01T00:00:00/)
            end
          end

          context 'field is guessed as date' do
            let(:cache_col_types) do
              [
                ::PostgresCache::ColType::String,
                ::PostgresCache::ColType::String,
                ::PostgresCache::ColType::Date,
                ::PostgresCache::ColType::Number,
              ]
            end

            it 'performs PoP on datetime' do
              post :submit_generate, format: :json, params: params
              assert_success_response!

              res = JSON.parse(response.body)

              _, values = res['data']['generated']['result']['data']
              expect(values[0][1..]).to eq(['13', nil])
              expect(values[0][0]).to match(/^2020-01-01T00:00:00/)
              expect(values[1][1..]).to eq(['-59', '13'])
              expect(values[1][0]).to match(/^2020-02-01T00:00:00/)
            end
          end
        end
      end

      context 'invalid condition values' do
        before do
          vs_params[:filters] << {
            operator: 'is',
            values: ['ahihi'],
            path_hash: { field_name: 'amount' },

          }
        end

        it 'raises informative error' do
          post :submit_generate, format: :json, params: params
          assert_response_status!(422)

          expect(response.body).to include('Invalid value for type number: ahihi')
        end
      end

      context 'cache expired' do
        before do
          PostgresCache.clear(pgcache_model.backend.pgcache_key)
        end

        it 'raises informative error' do
          post :submit_generate, format: :json, params: params
          assert_response_status!(422)

          expect(response.body).to include('The Holistics cache entry for this result has expired')
        end
      end
    end

    context 'model of Postgres with `money` column' do
      before do
        FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
      end

      include_context 'simple_query_model'
      let(:query_model_sql) do
        <<~SQL
          select '3535353'::money as revenue
        SQL
      end
      let(:data_set) do
        ds = FactoryBot.create :data_set, root_model_id: nil
        ds.data_models << query_data_model
        ds.save!
        ds.reload
        ds
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:vs_params) do
        {
          viz_type: 'data_table',
          fields: {
            table_fields: [
              { path_hash: { model_id: query_data_model.id, field_name: 'revenue', aggregation: 'SUM' } },
            ],
          },
          format: {},
          filters: [],
          settings: {},
        }
      end

      it 'works with `money` column as a number field' do
        post :submit_generate, format: :json, params: params
        job = assert_success_async_response!

        data = Viz::Data::GeneratorService.async_result(job)
        # Using #to_i because different machines have different locales (LC_MONETARY) and hence different precisions
        expect(data.values.map { |r| r.map(&:to_i) }).to eq([[3_535_353]])
      end

      context 'with placeholder conditions' do
        before do
          vs_params[:filters] << {
            operator: 'is',
            values: [DataModeling::Values::Condition::NIL_PLACEHOLDER],
            path_hash: { model_id: query_data_model.id, field_name: 'revenue' },
          }
        end

        it 'ignores placholder conditions' do
          post :submit_generate, format: :json, params: params
          job = assert_success_async_response!

          data = Viz::Data::GeneratorService.async_result(job)
          # Using #to_i because different machines have different locales (LC_MONETARY) and hence different precisions
          expect(data.values.map { |r| r.map(&:to_i) }).to eq([[3_535_353]])
        end
      end
    end

    context 'model with special number values' do
      include_context 'simple_query_model'
      let(:query_model_sql) do
        <<~SQL
          select 'infinity'::float as infi
        SQL
      end
      let(:data_set) do
        ds = FactoryBot.create :data_set, root_model_id: nil
        ds.data_models << query_data_model
        ds.save!
        ds.reload
        ds
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:vs_params) do
        {
          viz_type: 'data_table',
          fields: {
            table_fields: [
              { path_hash: { model_id: query_data_model.id, field_name: 'infi' } },
            ],
          },
          format: {},
          filters: [],
          settings: {},
        }
      end

      before do
        FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
        FeatureToggle.toggle_global(::Viz::Constants::FT_TABLE_V2, true)
      end

      it 'keeps Infinity values' do
        post :submit_generate, format: :json, params: params
        job = assert_success_async_response!

        result = Viz::Data::GeneratorService.async_result(job)
        expect(result.values).to eq([['Infinity']])
      end
    end

    context 'a query model has error' do
      let!(:dm1) do
        create_query_data_model(admin, ds, 'new_sql_model', 'select 1 as id, 2 as name')
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:table_fields) { [] }
      let(:vs_params) do
        {
          viz_type: 'data_table',
          fields: {
            table_fields: table_fields,
          },
          format: {},
          filters: [],
          settings: {},
        }
      end
      let!(:dm2) do
        create_query_data_model(admin, ds, 'new_sql_model2', "select {{ #dm.id }} a from {{ ##{dm1.name} dm }}")
      end
      let(:data_set) do
        ds = FactoryBot.create :data_set, root_model_id: nil
        ds.data_models << dm1
        ds.data_models << dm2
        ds.save!
        ds.reload
        ds
      end

      before do
        dm1.backend.update!(query: 'select 2 as name')
        dm1.refresh_fields(sync_with_db: true)
      end

      context 'errorneous model is not used in viz' do
        let(:table_fields) do
          [
            { path_hash: { model_id: dm1.id, field_name: 'name' } },
          ]
        end

        it 'works fine' do
          post :submit_generate, format: :json, params: params
          assert_success_async_response!
        end
      end

      context 'errorneous model is used in viz' do
        let(:table_fields) do
          [
            { path_hash: { model_id: dm2.id, field_name: 'a' } },
          ]
        end

        it 'raises informative error' do
          post :submit_generate, format: :json, params: params
          assert_response_status!(422)

          expect(response.body).to include('Field `id` not found')
        end
      end
    end

    context 'query model dataset' do
      let(:query_model_sql) do
        <<~SQL
          with t("name", "group", "amount") as (
            values
              ('x', 'a', 1),
              ('y', 'a', 2),
              ('z', 'b', 4),
              ('w', 'c', 8)
          )
          select * from t
        SQL
      end
      let(:data_set) do
        ds = FactoryBot.create :data_set, root_model_id: nil
        ds.data_models << query_data_model
        ds.save!
        ds.reload
        ds
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }

      before do
        FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
        FeatureToggle.toggle_global(::Viz::Constants::FT_PIVOT_V2, true)
        FeatureToggle.toggle_global(Viz::Constants::FT_SORT_BEFORE_LIMIT, true)
        request.headers['Content-Type'] = 'application/json'
      end

      context 'simple data table' do
        let(:vs_params) do
          {
            viz_type: 'data_table',
            fields: {
              table_fields: [
                { path_hash: { model_id: query_data_model.id, field_name: 'name' } },
              ],
            },
            format: {},
            filters: [],
            settings: {},
          }
        end
        let(:source_params) { { type: data_set.class.name, id: data_set_id, action: 'explore' } }

        it 'generates correct SQL' do |ex|
          post :submit_generate, format: :json, params: params
          job = assert_success_async_response!

          log = assert_job_log!(job, /generated SQL/i)
          SnapshotTest.test!(log.message, rspec_example: ex, snapshot_name: 'submit_generate_generated_sql.log')
        end
      end

      context 'pivot with colliding biz cals' do
        let(:vs_params) do
          {
            viz_type: 'pivot_table',
            fields: {
              pivot_data: {
                rows: [
                  { type: 'text', path_hash: { field_name: 'biz_name' } },
                ],
                columns: [
                  { type: 'text', path_hash: { field_name: 'biz_group' } },
                ],
                values: [
                  { type: 'number', path_hash: { model_id: query_data_model.id, field_name: 'amount' },
                    aggregation: 'sum', },
                ],
              },
            },
            format: {},
            filters: [],
            settings: {
              others: {
                column_total: true,
              },
            },
            adhoc_fields: [
              {
                name: 'biz_name',
                syntax: 'aml',
                sql: "case(when: #{query_data_model.name}.name == 'x', then: 'X', else: 'NOT X')",
                type: 'text',
              },
              {
                name: 'biz_group',
                syntax: 'aml',
                sql: "case(when: #{query_data_model.name}.group == 'a', then: 'A', else: 'NOT A')",
                type: 'text',
              },
            ],
          }
        end

        it 'renders biz cal cells correctly based on the element fields of biz cals' do
          post :submit_generate, format: :json, params: params
          job = assert_success_async_response!

          result = Viz::Data::GeneratorService.async_result(job)
          expect(result.values).to eq(
            [
              ['NOT X', '2', nil, nil],
              ['NOT X', nil, nil, '4'],
              ['NOT X', nil, '8', nil],
              ['X', '1', nil, nil],
            ],
          )
          expect(result.fields).to eq(
            ['biz_name__0', 'A_|_s_nsm_a_1a5214__2', 'NOT A_|_s_nsm_a_1a5214__2', 'NOT A_|_s_nsm_a_1a5214__2'],
          )
          expect(result.data[2].column_totals).to eq(
            ['3', '8', '4'],
          )
        end
      end

      context 'pivot with filter with aggregation' do
        let(:options_params) do
          { page: 1, page_size: 1 }
        end
        let(:vs_params) do
          {
            viz_type: 'pivot_table',
            fields: {
              pivot_data: {
                rows: [
                  { type: 'text', path_hash: { model_id: query_data_model.id, field_name: 'group' } },
                ],
                columns: [],
                values: [
                  { type: 'number', path_hash: { model_id: query_data_model.id, field_name: 'amount' },
                    aggregation: 'sum', },
                ],
              },
            },
            format: {},
            filters: [
              { path_hash: { field_name: 'amount', model_id: query_data_model.id }, aggregation: 'sum',
                operator: 'greater_than', values: ['3'], },
            ],
            settings: {
              others: {
                column_total: true,
              },
            },
            adhoc_fields: [],
          }
        end

        it 'paginates correctly' do
          post :submit_generate, format: :json, params: params
          job = assert_success_async_response!

          result = Viz::Data::GeneratorService.async_result(job)
          pretty_test!(
            result.values,
            [
              ['b', '4'],
            ],
          )
        end

        context 'total does not satisfy filter condition' do
          before do
            vs_params[:filters] = [
              { path_hash: { field_name: 'amount', model_id: query_data_model.id }, aggregation: 'sum',
                operator: 'less_than', values: ['10'], },
              { path_hash: { field_name: 'amount', model_id: query_data_model.id }, aggregation: 'sum',
                operator: 'greater_than', values: ['3'], },
            ]
          end

          it 'still keeps the total', otel: true do |_ex|
            post :submit_generate, format: :json, params: params
            job = assert_success_async_response!

            result = Viz::Data::GeneratorService.async_result(job)
            pretty_test!(
              result.values,
              [
                ['b', '4'],
              ],
            )

            expect(result.data[2].column_totals).to eq(
              ['15'],
            )
          end
        end
      end

      context 'pivot with non-number aggregations' do
        let(:vs_params) do
          {
            viz_type: 'pivot_table',
            fields: {
              pivot_data: {
                rows: [
                  { type: 'text', path_hash: { model_id: query_data_model.id, field_name: 'group' } },
                ],
                columns: [],
                values: [
                  { type: 'number', path_hash: { model_id: query_data_model.id, field_name: 'amount' },
                    aggregation: 'sum', },
                  { type: 'text', path_hash: { model_id: query_data_model.id, field_name: 'agg_name' },
                    aggregation: 'custom', },
                ],
              },
            },
            format: {},
            filters: [],
            settings: {
              others: {
                column_total: true,
                row_total: true,
              },
            },
          }
        end

        before do
          query_data_model.external_fields << DataModeling::Explores::Measure.new(
            name: 'agg_name',
            label: 'Aggregated Names',
            sql: 'string_agg({{ #THIS.name }}, \', \')',
            syntax: 'sql',
            type: 'text',
            description: '',
            aggregation_type: 'custom',
          )
          query_data_model.save!
        end

        it 'works' do
          post :submit_generate, format: :json, params: params
          job = assert_success_async_response!

          result = Viz::Data::GeneratorService.async_result(job)
          pretty_test!(
            result.values,
            [
              ['a', '3', 'x, y'],
              ['b', '4', 'z'],
              ['c', '8', 'w'],
            ],
          )
          expect(result.data[2].column_totals).to eq(
            ['15', 'x, y, z, w'],
          )
        end

        context 'otel tracing', otel: true, type: :request do
          it 'performs otel tracing' do |ex|
            # reset OTel after triggering the lazy processings of `params` to reduce noise
            params
            otel_reset

            # skip immediate queuing so that the queuing happens outside of the controller trace,
            # simulating the scenario where the job cannot start immediately right after its creation, due to full job queue.
            # instead, it has to wait for available slot and is queued later by another process
            ENV['JOB_SKIP_IMMEDIATE_QUEUING'] = '1'

            post '/viz_data/submit_generate', params: params, as: :json
            job = assert_async_response!
            expect(job.status).to eq('created')

            Job.queue_next_job(tenant_id: job.tenant_id, tag: 'adhoc_query')
            assert_success_job!(job)

            controller_span = otel_finished_spans.find { |s| s.name == 'VizDataController#submit_generate' }
            expect(controller_span).to be_present
            expect(controller_span.attributes['h.tenant_id']).to eq(job.tenant_id.to_s)
            expect(controller_span.attributes['h.user_id']).to eq(job.user_id.to_s)

            sidekiq_queue_span = otel_finished_spans.find { |s| s.name == 'WorkerAdapters::SidekiqAdapter#queue' }
            expect(sidekiq_queue_span.trace_id).not_to eq(controller_span.trace_id)

            exec_sql_span = otel_finished_spans.find { |s| s.name == 'Connector#exec_sql' }
            expect(exec_sql_span.attributes['h.dbtype']).to eq('postgresql')
            expect(exec_sql_span.attributes['h.data_source_id']).to eq(get_test_ds.id.to_s)
            expect(exec_sql_span.attributes['h.tenant_id']).to eq(tenant.id.to_s)
            expect(exec_sql_span.attributes['h.user_id']).to eq(admin.id.to_s)

            SnapshotTest.test!(
              exec_sql_span.attributes['h.query'].sub(/.*?\n/, ''), # strip the job id comment
              rspec_example: ex,
              snapshot_name: 'non_number_agg.sql',
            )
            expect(exec_sql_span.attributes['h.fields_count']).to eq '7'
            expect(exec_sql_span.attributes['h.records_count']).to eq '4'

            to_sql_span = otel_finished_spans.find { |s| s.name == 'Modeling::Models::DataModel#to_sql' }
            expect(to_sql_span).to be_present
            expect(to_sql_span.attributes['h.tenant_id']).to eq(tenant.id.to_s)
            expect(to_sql_span.attributes['h.user_id']).to eq(admin.id.to_s)

            new_job_span = otel_finished_spans.find { |s| s.name == 'Job.new_job' }
            expect(new_job_span).to be_present
            expect(new_job_span.attributes['h.tenant_id']).to eq(job.tenant_id.to_s)
            expect(new_job_span.attributes['h.user_id']).to eq(job.user_id.to_s)

            next_job_sql_span = otel_finished_spans.find { |s| s.name == 'Job.get_next_job_sql' }
            expect(next_job_sql_span).to be_present
            # h.queue_tenant_id is the tenant_id of the next job
            # h.tenant_id is the tenant_id in the thread that triggers the queuing
            expect(next_job_sql_span.attributes['h.queue_tid']).to eq(job.tenant_id.to_s)
            expect(next_job_sql_span.attributes['h.queue_tag']).to eq(job.tag.to_s)

            push_to_sidekiq_span = otel_finished_spans.find { |s| s.name == 'Job.push_to_sidekiq' }
            expect(push_to_sidekiq_span).to be_present
            job_source_execution_span = otel_finished_spans.find do |s|
              s.name == 'Job#execute(DataModel.execute_model_query)'
            end
            expect(job_source_execution_span).to be_present
            # test baggage
            expect(job_source_execution_span.attributes['baggage.h.tenant_id.value']).to eq(job.tenant_id.to_s)
            expect(job_source_execution_span.attributes['baggage.h.user_id.value']).to eq(job.user_id.to_s)
            # although the job is queued by another process, the execution span is still correctly traced within the same trace of the controller span
            expect(job_source_execution_span.trace_id).to eq(controller_span.trace_id)
            expect(HOtel::HSpan.current_span(job.otel_context).trace_id).to eq(controller_span.trace_id)
            expect(HOtel::HSpan.current_span(job.running_otel_context).trace_id).to eq(controller_span.trace_id)

            expect(job.otel_context_raw).to be_present
            job_span_context = OpenTelemetry::Trace.current_span(job.otel_context).context
            expect(job_span_context.trace_id).to eq(new_job_span.trace_id)
            expect(job_span_context.trace_id).to eq(push_to_sidekiq_span.trace_id)
            expect(job_span_context.trace_id).to eq(job_source_execution_span.trace_id)

            bulk_load_span = otel_finished_spans.find { |s| s.name.match?(/bulk_load_v2/) }
            expect(bulk_load_span).to be_present

            # other spans
            other_spans = [
              'Job#set_status',
              'Job#update_pending_jobs_cache',
              'Job#upsert_last_run',
              'Job#save_status_for_v2!#update!',
            ]
            other_spans.each do |name|
              expect(otel_finished_spans.find { |s| s.name == name }).to be_present
            end

            Viz::Data::GeneratorService.async_result(job)
            pgcache_query_spans = otel_finished_spans.select { |s| s.name.include? 'ExecuteSql#call(pgcache)' }
            SnapshotTest.test!(
              pgcache_query_spans.map(&:attributes),
              rspec_example: ex,
              snapshot_name: 'pgcache_query_spans',
            )
          end

          context 'without bulk load v2' do
            before do
              FeatureToggle.toggle_global(PostgresCache::FT_USE_BULK_LOAD_V2, false)
            end

            it 'traces bulk_load' do
              # reset OTel after triggering the lazy processings of `params` to reduce noise
              params
              otel_reset

              post '/viz_data/submit_generate', params: params, as: :json
              assert_success_async_response!

              bulk_load_span = otel_finished_spans.find { |s| s.name.match?(/bulk_load$/) }
              expect(bulk_load_span).to be_present
            end
          end

          it 'does not fully perform otel tracing when FT is off' do
            FeatureToggle.toggle_global(HOtel::FT_OTEL_ENABLED, false)

            post '/viz_data/submit_generate', params: params, as: :json
            assert_success_async_response!

            controller_span = otel_finished_spans.find { |s| s.name == 'VizDataController#submit_generate' }
            expect(controller_span).not_to be_present

            to_sql_span = otel_finished_spans.find { |s| s.name == 'Modeling::Models::DataModel#to_sql' }
            expect(to_sql_span).not_to be_present
          end

          it 'does not perform otel tracing when otel configs are disabled', otel_sampler: false,
                                                                             otel_propagation: false do
            post '/viz_data/submit_generate', params: params, as: :json
            assert_success_async_response!

            expect(otel_finished_spans.size).to eq(0)
          end
        end
      end

      context 'with uneven column headers' do
        let(:query_model_sql) do
          <<~SQL
            with t("name", "group", "tag", "amount") as (
              values
                ('a', 'q', 'x', 1),
                ('a', 'r', 'y', 2),
                ('b', 'q', 'y', 4)
            )
            select * from t
          SQL
        end

        let(:vs_params) do
          {
            viz_type: 'pivot_table',
            fields: {
              pivot_data: {
                rows: [
                  { type: 'text', path_hash: { model_id: query_data_model.id, field_name: 'name' } },
                ],
                columns: [
                  { type: 'text', path_hash: { model_id: query_data_model.id, field_name: 'group' } },
                  { type: 'text', path_hash: { model_id: query_data_model.id, field_name: 'tag' } },
                ],
                values: [
                  { type: 'number', path_hash: { model_id: query_data_model.id, field_name: 'amount' },
                    aggregation: 'sum', },
                ],
              },
            },
            format: {},
            filters: [],
            settings: {
              others: {
                sub_total: true,
                row_total: true,
                column_total: false,
              },
            },
          }
        end

        context 'subtotals enabled' do
          it 'renders the cells correctly' do
            post :submit_generate, format: :json, params: params
            job = assert_success_async_response!

            result = Viz::Data::GeneratorService.async_result(job)
            expect(result.values).to eq(
              [
                #      |      q       |    r    | total
                #      | x |  y | tot | x | tot | total
                # name
                ['a', '1', nil, '1', '2', '2', '3'],
                ['b', nil, '4', '4', nil, nil, '4'],
              ],
            )
            expect(result.fields).to eq(
              [
                'nsm_n_2cb565__0',
                'q_|_x_|_s_nsm_a_1a5214__3',
                'q_|_y_|_s_nsm_a_1a5214__3',
                'q_|__holistics_sub_total__|_s_nsm_a_1a5214__3',
                'r_|_y_|_s_nsm_a_1a5214__3',
                'r_|__holistics_sub_total__|_s_nsm_a_1a5214__3',
                '_holistics_total__|__holistics_total__|_s_nsm_a_1a5214__3',
              ],
            )
            expect(result.data[2].column_totals).to be_nil
          end
        end

        context 'subtotals disabled' do
          before do
            vs_params[:settings][:others][:sub_total] = false
          end

          it 'does not return the subtotals' do
            post :submit_generate, format: :json, params: params
            job = assert_success_async_response!

            result = Viz::Data::GeneratorService.async_result(job)
            expect(result.values).to eq(
              [
                #      |   q    | r | total
                #      | x |  y | x | total
                # name
                ['a', '1', nil, '2', '3'],
                ['b', nil, '4', nil, '4'],
              ],
            )
            expect(result.fields).to eq(
              [
                'nsm_n_2cb565__0',
                'q_|_x_|_s_nsm_a_1a5214__3',
                'q_|_y_|_s_nsm_a_1a5214__3',
                'r_|_y_|_s_nsm_a_1a5214__3',
                '_holistics_total__|__holistics_total__|_s_nsm_a_1a5214__3',
              ],
            )
            expect(result.data[2].column_totals).to be_nil
          end
        end
      end

      context 'with 100-pivot-column limit' do
        let(:query_model_sql) do
          <<~SQL
            with t(x) as (
              select generate_series(0, 120, 1)
            )
            select * from t
          SQL
        end

        let(:vs_params) do
          {
            viz_type: 'pivot_table',
            fields: {
              pivot_data: {
                rows: [
                  { type: 'number', path_hash: { model_id: query_data_model.id, field_name: 'x' } },
                ],
                columns: [
                  { type: 'number', path_hash: { model_id: query_data_model.id, field_name: 'x' } },
                ],
                values: [
                  { type: 'number', path_hash: { model_id: query_data_model.id, field_name: 'x' }, aggregation: 'sum' },
                ],
              },
            },
            format: {},
            filters: [],
            settings: {
              others: {
                column_total: true,
                row_total: true,
              },
            },
          }
        end

        before do
          FeatureToggle.toggle_global(::Viz::Constants::FT_LIMIT_100_PIVOT_COLUMNS, true)
        end

        it 'renders 100 columns only' do |ex|
          post :submit_generate, format: :json, params: params
          job = assert_success_async_response!

          result = Viz::Data::GeneratorService.async_result(job)
          data = {
            fields: result.fields,
            values: result.values,
            column_totals: result.data[2][:column_totals],
          }
          SnapshotTest.test!(data, rspec_example: ex, snapshot_name: 'pivot_table_with_100_pivot_columns.json')
        end

        context 'line chart' do
          let(:vs_params) do
            {
              viz_type: 'line_chart',
              fields: {
                series: {
                  path_hash: { model_id: query_data_model.name, field_name: 'x' },
                },
                x_axis: {
                  path_hash: { model_id: query_data_model.name, field_name: 'x' },
                },
                y_axes: [
                  label: 'Y Axis 1',
                  columns: [
                    {
                      aggregation: 'count',
                      path_hash: { model_id: query_data_model.name, field_name: 'x' },
                    },
                  ],
                ],
              },
              format: {},
              filters: [],
              settings: {
                others: {
                  column_total: true,
                  row_total: true,
                },
              },
            }
          end

          it 'renders all columns' do |ex|
            post :submit_generate, format: :json, params: params
            job = assert_success_async_response!

            result = Viz::Data::GeneratorService.async_result(job)
            data = {
              fields: result.fields,
              values: result.values,
              column_totals: result.data[2][:column_totals],
            }
            SnapshotTest.test!(data, rspec_example: ex, snapshot_name: 'line_chart_with_100_pivot_columns.json')
            expect(result.data[2][:column_totals].size).to eq 121
          end

          context 'with limit FT enabled for charts' do
            before do
              FeatureToggle.toggle_global(::Viz::Constants::FT_LIMIT_100_CHART_PIVOT_COLUMNS, true)
            end

            it 'returns 101 columns only' do |ex|
              post :submit_generate, format: :json, params: params
              job = assert_success_async_response!

              result = Viz::Data::GeneratorService.async_result(job)
              data = {
                fields: result.fields,
                values: result.values,
                column_totals: result.data[2][:column_totals],
              }
              SnapshotTest.test!(data, rspec_example: ex, snapshot_name: 'line_chart_with_100_chart_pivot_columns.json')
              expect(result.data[2][:column_totals].size).to eq 101
              expect(result.meta[:col_page_size]).to eq 101
              expect(result.meta[:viz_type]).to eq 'line_chart'
            end
          end
        end

        context 'sort_before_limit' do
          before do
            FeatureToggle.toggle_global(Viz::Constants::FT_SORT_BEFORE_LIMIT, true)
          end

          it 'renders 100 columns only' do |ex|
            post :submit_generate, format: :json, params: params
            job = assert_success_async_response!

            result = Viz::Data::GeneratorService.async_result(job)
            data = {
              fields: result.fields,
              values: result.values,
              column_totals: result.data[2][:column_totals],
            }
            SnapshotTest.test!(data, rspec_example: ex, snapshot_name: 'pivot_table_with_100_pivot_columns.json')
          end
        end
      end

      context 'bigquery' do
        let(:ds) { get_test_bigquery_ds }
        let(:query_model_sql) do
          <<~SQL
            select
              '1' as a,
              '2' as b,
              '3' as C
          SQL
        end

        let(:vs_params) do
          {
            viz_type: 'data_table',
            fields: {
              table_fields: [
                {
                  path_hash: {
                    model_id: query_data_model.id,
                    field_name: 'a',
                  },
                },
                {
                  path_hash: {
                    model_id: query_data_model.id,
                    field_name: 'b',
                  },
                },
                {
                  path_hash: {
                    model_id: query_data_model.id,
                    field_name: 'c',
                  },
                },
              ],
            },
            filters: [],
            format: {},
            settings: {},
          }
        end

        it 'returns bigquery cost in meta' do
          post :submit_generate, format: :json, params: params
          job = assert_success_async_response!

          data = Viz::Data::GeneratorService.async_result(job)
          expect(data.values).to eq([['1', '2', '3']])
          expect(data.meta[:dw_stats]).to eq({ cost: 0, cost_unit: 'B' })
        end
      end
    end

    context 'process with timezone' do
      include_context 'timezone_setup'

      let(:query_model_sql) do
        <<~SQL
          select
            10 as "val",
            '2021-10-20 12:00:00+00:00'::timestamptz as "time",
            '2021-10-20'::date as "date"
        SQL
      end

      let(:source_filter) do
        []
      end
      let(:vs_params) do
        {
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                type: 'number',
                format: {},
                path_hash: {
                  model_id: query_data_model.id,
                  field_name: 'val',
                },
              },
              {
                type: 'datetime',
                format: {},
                path_hash: {
                  model_id: query_data_model.id,
                  field_name: 'time',
                },
              },
              {
                type: 'date',
                format: {},
                path_hash: {
                  model_id: query_data_model.id,
                  field_name: 'date',
                },
              },
            ],
          },
          filters: source_filter,
          format: {},
          settings: {},
        }
      end
      let(:tz) { 'Asia/Singapore' }
      let(:data_set) do
        ds = FactoryBot.create :data_set, root_model_id: nil
        ds.data_models << query_data_model
        ds.save!
        ds.reload
        ds
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:source_params) { { type: data_set.class.name, id: data_set.id, action: 'explore' } }

      def correct_submmit_generate_with_timezone(params, timezone, value)
        post :submit_generate, format: :json, params: params
        assert_success_response!

        res = JSON.parse(response.body)

        job = Job.find(res['job_id'])
        assert_success_job!(job)

        cache_meta = PostgresCache.fetch_metadata(job.data[:cache_key])
        expect(cache_meta['query_processing_timezone']).to eq timezone

        get :get_generate_results, format: :json, params: { job_id: job.id }
        assert_success_response!

        res = JSON.parse(response.body)
        expect(res['data']['values']).to eql(value)
      end

      shared_examples 'returns correct data' do
        let(:expected_data) do
          [['10', '2021-10-20 20:00:00.000000', '2021-10-20']]
        end

        let(:expected_timezone) do
          'Asia/Singapore'
        end

        it 'correct submit generate with timezone' do
          correct_submmit_generate_with_timezone(params, expected_timezone, expected_data)
        end
      end

      context 'timezone enabled' do
        context 'explore model' do
          context 'data set' do
            context 'with tenant timezone' do
              it_behaves_like 'returns correct data'
            end

            context 'with explicit timezone' do
              let(:explicit_timezone) { 'Asia/Tokyo' }
              let(:source_params) do
                { type: data_set.class.name, id: data_set.id, action: 'explore', timezone: explicit_timezone }
              end

              it_behaves_like 'returns correct data' do
                let(:expected_data) do
                  [['10', '2021-10-20 21:00:00.000000', '2021-10-20']]
                end

                let(:expected_timezone) do
                  'Asia/Tokyo'
                end
              end
            end
          end
        end

        context 'use dashboard timezone' do
          shared_examples 'returns correct data' do
            it 'with tz' do
              correct_submmit_generate_with_timezone(params, dashboard_timezone, expected_data)
            end
          end

          context 'dashboard widget (Dashboard 3.0)' do
            let(:dashboard_timezone) { 'Asia/Tokyo' }
            let(:dashboard) { create :dashboard, version: 3, settings: { timezone: dashboard_timezone } }
            let(:report) { create :query_report, data_set_id: data_set.id }
            let(:widget) { create :dashboard_widget, dashboard: dashboard, source: report }
            let(:source_params) { { type: widget.class.name, id: widget.id, action: 'explore' } }

            it_behaves_like 'returns correct data' do
              let(:expected_data) do
                [['10', '2021-10-20 21:00:00.000000', '2021-10-20']]
              end
              let(:expected_timezone) do
                dashboard_timezone
              end
            end
          end

          context 'viz block (Dashboard 4.0)' do
            include_context 'dashboard_v4_timezone_context'
            let(:dashboard_timezone) { 'Asia/Tokyo' }
            let(:dashboard) { create_dashboard_form_viz_setting(vs_params, data_set.id, dashboard_timezone) }
            let(:source_params) { { type: 'VizBlock', id: "#{dashboard.id}:v1", action: 'view' } }
            let(:viz_block_v1_label) { 'ahihi' }

            it_behaves_like 'returns correct data' do
              let(:expected_data) do
                [['10', '2021-10-20 21:00:00.000000', '2021-10-20']]
              end
              let(:expected_timezone) do
                dashboard_timezone
              end
            end

            it 'saves origin correctly' do
              post :submit_generate, format: :json, params: params
              job = assert_async_response!
              expect(job.origin.entity_type).to eq('Dashboard')
              expect(job.origin.entity_id).to eq(dashboard.id)
              expect(job.origin.details).to eq(
                block_type: 'VizBlock',
                block_id: 'v1',
                block_display_title: viz_block_v1_label,
              )
            end
          end
        end

        context 'with condition on custom measure' do
          before do
            query_data_model.external_fields << DataModeling::Explores::Measure.new(
              name: 'max_time',
              label: 'Max Time',
              sql: 'max({{ #THIS.time }})',
              type: 'datetime',
              description: '',
              aggregation_type: 'custom',
              explore: query_data_model,
              model: query_data_model,
            )
          end

          let(:vs_params) do
            {
              viz_type: 'data_table',
              fields: {
                table_fields: [
                  {
                    type: 'number',
                    format: {},
                    path_hash: {
                      model_id: query_data_model.id,
                      field_name: 'val',
                    },
                  },
                  {
                    type: 'datetime',
                    format: {},
                    path_hash: {
                      model_id: query_data_model.id,
                      field_name: 'time',
                    },
                  },
                  {
                    type: 'date',
                    format: {},
                    path_hash: {
                      model_id: query_data_model.id,
                      field_name: 'date',
                    },
                  },
                ],
              },
              filters: [
                {
                  label: 'Adhoc filter',
                  operator: 'matches',
                  path_hash: {
                    field_name: 'max_time',
                    model_id: query_data_model.id,
                  },
                  values: ['2021-10-20'],
                },
              ],
              format: {},
              settings: {},
            }
          end

          context 'data set' do
            it_behaves_like 'returns correct data'
          end

          context 'viz block (Dashboard 4.0)' do
            include_context 'dashboard_v4_timezone_context'
            let(:dashboard) { create_dashboard_form_viz_setting(vs_params, data_set.id, tz) }
            let(:source_params) { { type: 'VizBlock', id: "#{dashboard.id}:v1" } }

            it_behaves_like 'returns correct data'
          end
        end

        context 'with filters' do
          let(:query_model_sql) do
            <<~SQL
              select *
              from (
                values
                  (10, 'Dat', '2021-10-20 16:30:00+00:00'::timestamptz, '2021-10-20'),
                  (20, 'Ne', '2021-10-20 23:00:00+00:00'::timestamptz, '2021-10-20'),
                  (30, 'Mina', '2021-10-21 12:00:00+00:00'::timestamptz, '2021-10-21')
              )
              as t ("val", "name", "time", "date")
            SQL
          end

          let(:time_condition) do
            {
              operator: 'matches',
              values: ['2021-10-21'],
            }
          end

          let(:source_filter) do
            [
              time_condition.merge(
                path_hash: {
                  field_name: 'time',
                  model_id: query_data_model.id,
                },
              ),
            ]
          end

          shared_examples 'returns correct data' do
            # Tenant timezone: Asia/Singapore (+08:00)
            # Dashboard timezone: Asia/Bangkok (+07:00)
            # NOTE: Please make sure the test data cover the filter test
            # With the data below, when filtering on 2021-10-21 with dashboard timezone, it will return 2021-10-20 23:00:00+00:00 and 2021-10-21 12:00:00+00:00 only
            # Because 2021-10-20 16:30:00+00:00 -> 2021-10-20 23:30:00+07:00

            it 'resolves timezone correctly' do
              correct_submmit_generate_with_timezone(params, expected_timezone, expected_data)
            end
          end
          context 'Dashboard widget (3.0)' do
            let(:dashboard_timezone) { 'Asia/Bangkok' }
            let(:dashboard) { create :dashboard, version: 3, settings: { timezone: dashboard_timezone } }
            let(:report) { create :query_report, data_set_id: data_set.id }
            let(:widget) { create :dashboard_widget, dashboard: dashboard, source: report }
            let(:source_params) { { type: widget.class.name, id: widget.id, action: 'explore' } }

            it_behaves_like 'returns correct data' do
              let(:expected_data) do
                [['30', '2021-10-21 19:00:00.000000', '2021-10-21'], ['20', '2021-10-21 06:00:00.000000', '2021-10-20']]
              end

              let(:expected_timezone) do
                'Asia/Bangkok'
              end
            end
          end

          context 'viz block (Dashboard 4.0)' do
            include_context 'dashboard_v4_timezone_context'
            let(:dashboard_timezone) { 'Asia/Bangkok' }
            let(:dashboard) { create_dashboard_form_viz_setting(vs_params, data_set.id, dashboard_timezone) }
            let(:source_params) { { type: 'VizBlock', id: "#{dashboard.id}:v1" } }

            it_behaves_like 'returns correct data' do
              let(:expected_data) do
                [['30', '2021-10-21 19:00:00.000000', '2021-10-21'], ['20', '2021-10-21 06:00:00.000000', '2021-10-20']]
              end

              let(:expected_timezone) do
                'Asia/Bangkok'
              end
            end
          end
        end
      end

      context 'timezone disabled' do
        before do
          FeatureToggle.toggle_global(Tenant::FT_NEW_TIMEZONE_CONFIG, false)
        end

        context 'Dashboard widget (3.0)' do
          let(:dashboard_timezone) { 'Asia/Bangkok' }
          let(:dashboard) { create :dashboard, version: 3, settings: { timezone: dashboard_timezone } }
          let(:report) { create :query_report, data_set_id: data_set.id }
          let(:widget) { create :dashboard_widget, dashboard: dashboard, source: report }
          let(:source_params) { { type: widget.class.name, id: widget.id, action: 'explore' } }

          it_behaves_like 'returns correct data' do
            let(:expected_data) do
              [['10', '2021-10-20T12:00:00.000+00:00', '2021-10-20']]
            end

            let(:expected_timezone) do
              nil
            end
          end
        end

        context 'viz block (Dashboard 4.0)' do
          include_context 'dashboard_v4_timezone_context'
          let(:dashboard) { create_dashboard_form_viz_setting(vs_params, data_set.id, tz) }
          let(:source_params) { { type: 'VizBlock', id: "#{dashboard.id}:v1" } }

          it_behaves_like 'returns correct data' do
            let(:expected_data) do
              [['10', '2021-10-20T12:00:00.000+00:00', '2021-10-20']]
            end

            let(:expected_timezone) do
              nil
            end
          end
        end
      end

      context 'dashboard timezone disabled' do
        before do
          FeatureToggle.toggle_global(Timezone::Helper::FT_DASHBOARD_TIMEZONE, false)
        end

        context 'dashboard widget (Dashboard 3.0)' do
          let(:dashboard_timezone) { 'Asia/Tokyo' }
          let(:dashboard) { create :dashboard, version: 3, settings: { timezone: dashboard_timezone } }
          let(:report) { create :query_report, data_set_id: data_set.id }
          let(:widget) { create :dashboard_widget, dashboard: dashboard, source: report }
          let(:source_params) { { type: widget.class.name, id: widget.id, action: 'explore' } }

          it_behaves_like 'returns correct data'
        end

        context 'viz block (Dashboard 4.0)' do
          include_context 'dashboard_v4_timezone_context'
          let(:dashboard_timezone) { 'Asia/Tokyo' }
          let(:dashboard) { create_dashboard_form_viz_setting(vs_params, data_set.id, dashboard_timezone) }
          let(:source_params) { { type: 'VizBlock', id: "#{dashboard.id}:v1" } }

          it_behaves_like 'returns correct data'
        end
      end
    end

    context 'pop' do
      include_context 'timezone_setup'

      let(:query_model_sql) do
        <<~SQL
          SELECT *
            FROM (VALUES
              ('2020-01-01 23:00:00'::timestamp, 10),
              ('2020-02-01 23:00:00'::timestamp, 20),
              ('2020-02-29 22:30:00'::timestamp, 50),
              ('2020-03-01 23:00:00'::timestamp, 30),
              ('2020-03-31 22:00:00'::timestamp, 40)
            )
          AS t (date_and_time, value)
        SQL
      end
      let(:vs_params) do
        {
          viz_type: 'line_chart',
          fields: {
            series: {},
            x_axis: {
              type: 'datetime',
              format: { type: 'date', sub_type: 'mmm yyyy' },
              path_hash: { model_id: query_data_model.id, field_name: 'date_and_time' },
              transformation: 'datetrunc month',
            },
            y_axes: [
              {
                columns: [
                  {
                    type: 'auto',
                    color: 'auto',
                    format: { type: 'number', format: {} },
                    path_hash: { model_id: query_data_model.id, field_name: 'value' },
                    aggregation: 'sum',
                    custom_label: nil,
                  },
                ],
              },
            ],
          },
          filters: [],
          format: {},
          settings: {},
        }
      end
      let(:tz) { 'Europe/Copenhagen' }
      let(:data_set) do
        ds = FactoryBot.create :data_set, root_model_id: nil
        ds.data_models << query_data_model
        ds.save!
        ds.reload
        ds
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:source_params) { { type: data_set.class.name, id: data_set.id } }

      before do
        FeatureToggle.toggle_global(Dashboard::FT_V3_CREATION, true)
      end

      shared_examples 'returns correct data' do
        let(:expected_data) do
          [
            ['2020-01-01 00:00:00.000000', '10', nil],
            ['2020-02-01 00:00:00.000000', '70', '10'],
            ['2020-03-01 00:00:00.000000', '30', '70'],
            ['2020-04-01 00:00:00.000000', '40', '30'],
          ]
        end

        it 'with tz' do
          post :submit_generate, format: :json, params: params
          assert_success_response!

          res = JSON.parse(response.body)

          job = Job.find(res['job_id'])
          assert_success_job!(job)

          get :get_generate_results, format: :json, params: { job_id: job.id }
          assert_success_response!

          res = JSON.parse(response.body)
          expect(res['data']['data'][1]).to eql(expected_data)
        end
      end

      context 'when settings is submitted via `pop_settings`' do
        let(:vs_params) do
          super().tap do |vs|
            vs[:settings][:pop_settings] = {
              type: 'relative',
              field: {
                type: 'datetime',
                uuid: nil,
                format: { type: 'date', sub_type: 'mmm yyyy' },
                path_hash: { model_id: query_data_model.id, field_name: 'date_and_time' },
                aggregation: nil,
                custom_label: nil,
                transformation: nil,
              },
              offset: 1,
              period: 'month',
              condition: { values: [], operator: 'between' },
            }
          end
        end

        context 'Data set' do
          it_behaves_like 'returns correct data'
        end

        context 'viz block (Dashboard 4.0)' do
          include_context 'dashboard_v4_timezone_context'
          let(:dashboard) { create_dashboard_form_viz_setting(vs_params, data_set.id, tz) }
          let(:source_params) { { type: 'VizBlock', id: "#{dashboard.id}:v1" } }

          it_behaves_like 'returns correct data'
        end
      end

      context 'when settings is submitted via Interactive Control' do
        let(:vs_params) do
          super().tap do |vs|
            vs[:filters] = [{
              operator: 'transform_pop_relative',
              path_hash: { model_id: query_data_model.id, field_name: 'date_and_time' },
              values: [1],
              modifier: 'month',
            }]
          end
        end

        it_behaves_like 'returns correct data'

        context 'viz block (Dashboard 4.0)' do
          include_context 'dashboard_v4_timezone_context'
          let(:dashboard) { create_dashboard_form_viz_setting(vs_params, data_set.id, tz) }
          let(:source_params) { { type: 'VizBlock', id: "#{dashboard.id}:v1" } }

          it_behaves_like 'returns correct data'
        end
      end

      context 'when settings is submitted via both `pop_settings` and Interactive Control' do
        let(:vs_params) do
          super().tap do |vs|
            vs[:settings][:pop_settings] = {
              type: 'relative',
              field: {
                type: 'datetime',
                uuid: nil,
                format: { type: 'date', sub_type: 'mmm yyyy' },
                path_hash: { model_id: query_data_model.id, field_name: 'date_and_time' },
                aggregation: nil,
                custom_label: nil,
                transformation: nil,
              },
              offset: 1,
              period: 'month',
              condition: { values: [], operator: 'between' },
            }
            vs[:filters] = [{
              operator: 'transform_pop_relative',
              path_hash: { model_id: query_data_model.id, field_name: 'date_and_time' },
              values: [2],
              modifier: 'month',
            }]
          end
        end

        context 'Data set' do
          it_behaves_like 'returns correct data' do
            let(:expected_data) do
              [
                ['2020-01-01 00:00:00.000000', '10', nil],
                ['2020-02-01 00:00:00.000000', '70', nil],
                ['2020-03-01 00:00:00.000000', '30', '10'],
                ['2020-04-01 00:00:00.000000', '40', '70'],
              ]
            end
          end
        end

        context 'viz block (Dashboard 4.0)' do
          include_context 'dashboard_v4_timezone_context'
          let(:dashboard) { create_dashboard_form_viz_setting(vs_params, data_set.id, tz) }
          let(:source_params) { { type: 'VizBlock', id: "#{dashboard.id}:v1" } }

          let(:expected_data) do
            [
              ['2020-01-01 00:00:00.000000', '10', nil],
              ['2020-02-01 00:00:00.000000', '70', nil],
              ['2020-03-01 00:00:00.000000', '30', '10'],
              ['2020-04-01 00:00:00.000000', '40', '70'],
            ]
          end
        end
      end

      context 'when settings is submitted via Interactive Control alongside Top-N and other filters on the date field' do
        let(:vs_params) do
          super().tap do |vs|
            vs[:filters] = [
              {
                operator: 'top',
                modifier: { model_id: query_data_model.id, field_name: 'value', aggregation: 'sum' },
                path_hash: { model_id: query_data_model.id, field_name: 'value' },
                values: [4],
              },
              {
                operator: 'after',
                path_hash: { model_id: query_data_model.id, field_name: 'date_and_time' },
                values: ['2020-01-01'],
              },
              {
                operator: 'transform_pop_relative',
                path_hash: { model_id: query_data_model.id, field_name: 'date_and_time' },
                values: [1],
                modifier: 'month',
              },
            ]
          end
        end

        context 'Data set' do
          it_behaves_like 'returns correct data' do
            let(:expected_data) do
              [
                ['2020-02-01 00:00:00.000000', '70', nil],
                ['2020-03-01 00:00:00.000000', '30', '70'],
                ['2020-04-01 00:00:00.000000', '40', '30'],
              ]
            end
          end
        end

        context 'viz block (Dashboard 4.0)' do
          include_context 'dashboard_v4_timezone_context'
          let(:dashboard) { create_dashboard_form_viz_setting(vs_params, data_set.id, tz) }
          let(:source_params) { { type: 'VizBlock', id: "#{dashboard.id}:v1" } }

          it_behaves_like 'returns correct data' do
            let(:expected_data) do
              [
                ['2020-02-01 00:00:00.000000', '70', nil],
                ['2020-03-01 00:00:00.000000', '30', '70'],
                ['2020-04-01 00:00:00.000000', '40', '30'],
              ]
            end
          end
        end
      end
    end

    context 'with Date Drill' do
      include_context 'simple_query_model_for_date_drill'

      let(:vs_params) do
        super().tap do |vs|
          vs[:fields][:table_fields] = [
            {
              custom_label: nil, format: {},
              transformation: 'datetrunc month',
              path_hash: { model_id: query_data_model.name, field_name: 'date_and_time' },
            },
            {
              custom_label: nil, format: {}, aggregation: 'sum',
              path_hash: { model_id: query_data_model.name, field_name: 'value' },
            },
          ]

          vs[:filters] = [{
            operator: 'transform_date_drill',
            path_hash: { model_id: query_data_model.name, field_name: 'date_and_time' },
            values: ['datetrunc quarter'],
          }]
        end
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:source_params) { { type: data_set.class.name, id: data_set.id } }

      before do
        FeatureToggle.toggle_global(DynamicFilter::FT_INTERACTIVE_CONTROL_DATE_DRILL, true)
      end

      def assert_return_expected_data!(expected_data, data_keys: ['values'])
        post :submit_generate, format: :json, params: params
        assert_success_response!

        res = JSON.parse(response.body)
        job = Job.find(res['job_id'])
        assert_success_job!(job)

        get :get_generate_results, format: :json, params: { job_id: job.id }
        assert_success_response!

        res = JSON.parse(response.body)
        expect(res['data'].dig(*data_keys)).to match_array(expected_data)
      end

      shared_examples 'retains original date transformation' do
        it 'retains original date transformation' do
          assert_return_expected_data!([
            ['2019-11-01 00:00:00.000000', '10'],
            ['2019-12-01 00:00:00.000000', '50'],
            ['2020-01-01 00:00:00.000000', '40'],
            ['2020-02-01 00:00:00.000000', '110'],
            ['2020-03-01 00:00:00.000000', '70'],
            ['2020-04-01 00:00:00.000000', '80'],
          ])
        end
      end

      it 'returns correct data' do
        assert_return_expected_data!([
          ['2019-10-01 00:00:00.000000', '60'],
          ['2020-01-01 00:00:00.000000', '220'],
          ['2020-04-01 00:00:00.000000', '80'],
        ])
      end

      context 'when multiple conditions are specified' do
        let(:vs_params) do
          super().tap do |vs|
            vs[:filters] << {
              operator: 'transform_date_drill',
              path_hash: { model_id: query_data_model.name, field_name: 'date_and_time' },
              values: ['datetrunc year'],
            }
          end
        end

        it 'only applies the last one' do
          assert_return_expected_data!([
            ['2019-01-01 00:00:00.000000', '60'],
            ['2020-01-01 00:00:00.000000', '300'],
          ])
        end
      end

      context 'when the same date dimension is selected with multiple granularities' do
        let(:vs_params) do
          super().tap do |vs|
            vs[:viz_type] = 'pivot_table'
            vs[:fields] = {
              pivot_data: {
                columns: [
                  {
                    custom_label: nil, format: {},
                    transformation: 'datetrunc year',
                    path_hash: { model_id: query_data_model.name, field_name: 'date_and_time' },
                  },
                ],
                rows: [
                  {
                    custom_label: nil, format: {},
                    transformation: 'datetrunc month',
                    path_hash: { model_id: query_data_model.name, field_name: 'date_and_time' },
                  },
                ],
                values: [
                  {
                    custom_label: nil, format: {}, aggregation: 'sum',
                    path_hash: { model_id: query_data_model.name, field_name: 'value' },
                  },
                ],
              },
            }
          end
        end

        it 'only applies to the first one (based on fields collected by each schema)' do
          assert_return_expected_data!(
            [
              ['2019-11-01 00:00:00.000000', '10', nil, nil],
              ['2019-12-01 00:00:00.000000', '50', nil, nil],
              ['2020-01-01 00:00:00.000000', nil, '40', nil],
              ['2020-02-01 00:00:00.000000', nil, '110', nil],
              ['2020-03-01 00:00:00.000000', nil, '70', nil],
              ['2020-04-01 00:00:00.000000', nil, nil, '80'],
            ],
            data_keys: ['data', 1],
          )
        end
      end

      describe 'ignores' do
        context 'when FT is disabled' do
          before do
            FeatureToggle.toggle_global(DynamicFilter::FT_INTERACTIVE_CONTROL_DATE_DRILL, false)
          end

          include_examples 'retains original date transformation'
        end

        context 'when value is invalid' do
          let(:vs_params) do
            super().tap do |vs|
              vs[:filters][0][:values] = ['what is this']
            end
          end

          include_examples 'retains original date transformation'
        end

        context 'when value is none' do
          let(:vs_params) do
            super().tap do |vs|
              vs[:filters][0][:values] = ['none']
            end
          end

          include_examples 'retains original date transformation'
        end

        context 'when mapped to an unknown field' do
          let(:vs_params) do
            super().tap do |vs|
              vs[:filters][0][:path_hash] = { model_id: query_data_model.name, field_name: 'umbala' }
            end
          end

          include_examples 'retains original date transformation'
        end
      end
    end

    context 'snowflake data source', require_snowflake: true do
      let(:ds) { snowflake_testdb_ds }
      let(:query_model_sql) do
        <<~SQL
          select
            '1' as "a",
            '2' as b,
            '3' as "C"
        SQL
      end
      let(:data_set) do
        ds = FactoryBot.create :data_set, root_model_id: nil
        ds.data_models << query_data_model
        ds.save!
        ds.reload
        ds
      end
      let(:root_model_id) { nil }
      let(:data_set_id) { data_set.id }
      let(:vs_params) do
        {
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                path_hash: {
                  model_id: query_data_model.id,
                  field_name: 'a',
                },
              },
              {
                path_hash: {
                  model_id: query_data_model.id,
                  field_name: 'b',
                },
              },
              {
                path_hash: {
                  model_id: query_data_model.id,
                  field_name: 'c',
                },
              },
            ],
          },
          filters: [],
          format: {},
          settings: {},
        }
      end

      shared_examples 'works fine with snowflake' do
        it 'works fine' do
          post :submit_generate, format: :json, params: params
          job = assert_success_async_response!

          data = Viz::Data::GeneratorService.async_result(job)
          expect(data.values).to eq([['1', '2', '3']])
        end
      end

      shared_examples 'works properly with snowflake identifiers' do
        before do
          FeatureToggle.toggle_global('snowflake:node_js_connector', true)
        end

        context 'QUOTED_IDENTIFIERS_IGNORE_CASE is false' do
          let(:ds) { snowflake_testdb_ds }

          it_behaves_like('works fine with snowflake')
        end

        context 'QUOTED_IDENTIFIERS_IGNORE_CASE is true' do
          let(:ds) { snowflake_test_ds_identifier_case_insensitive }

          it_behaves_like('works fine with snowflake')
        end
      end

      it_behaves_like('works properly with snowflake identifiers')

      context 'with sql gen gem' do
        before do
          FeatureToggle.toggle_global('data_models:sql_generation_gem', true)
        end

        it_behaves_like('works properly with snowflake identifiers')
      end
    end

    context 'using AMQL', env: AmlStudio::WorkingEnvironment::Env::AmlStudio do
      include_context 'aml_studio_dataset'

      before do
        FeatureToggle.toggle_global(DataModel::FT_AQL, true)
        FeatureToggle.toggle_global(DataModel::FT_AQL_AS_DEFAULT, true)
        request.headers[HolisticsSetting::HEADER_AML_ENV] = AmlStudio::WorkingEnvironment::Env::AmlStudio.serialize
        params[:project_id] = project.id
      end

      let(:vs_params) do
        {
          viz_type: 'data_table',
          adhoc_fields: [],
          fields: {
            table_fields: [
              {
                path_hash: { field_name: 'id', model_id: 'simple' },
                custom_label: '',
                type: 'number',
                format: { type: 'number', sub_type: 'auto' },
              },
            ],
          },
          filters: [],
          format: {},
        }
      end
      let(:data_set_id) { 'test' }
      let(:root_model_id) { nil }

      it 'stores the executed aql' do
        post :submit_generate, params: params, format: :json
        job = assert_success_async_response!

        result = Viz::Data::GeneratorService.async_result(job)
        expect(result.meta[:executed_aql]).to eq("explore {\n  dimensions {\n    s_i_e2f690: simple.id\n  }\n}")
      end
    end

    context 'AML Studio 2.0 with AmlStudio WorkingEnv', env: AmlStudio::WorkingEnvironment::Env::AmlStudio do
      include_context 'aml_studio_dataset'

      let(:vs_params) do
        {
          viz_type: 'data_table',
          adhoc_fields: [],
          fields: {
            table_fields: [
              {
                path_hash: { field_name: 'id', model_id: 'simple' },
                custom_label: '',
                type: 'number',
                format: { type: 'number', sub_type: 'auto' },
              },
            ],
          },
          filters: [],
          format: {},
        }
      end
      let(:data_set_id) { 'test' }
      let(:root_model_id) { nil }

      before do
        request.headers[HolisticsSetting::HEADER_AML_ENV] = AmlStudio::WorkingEnvironment::Env::AmlStudio.serialize
        params[:project_id] = project.id
      end

      context 'invalid input' do
        it 'raises error for invalid project' do
          params[:project_id] = -1
          post :submit_generate, params: params, format: :json

          assert_response_status!(404)
        end

        it 'raise error for invalid dataset_id' do
          params[:data_set_id] = 'invalid_data_set'
          post :submit_generate, params: params, format: :json

          assert_response_status!(422)
          expect(response.body).to match(/Dataset `invalid_data_set` not found/i)
        end
      end

      context 'preview from a dataset' do
        it 'successfully preview from a dataset' do
          post :submit_generate, params: params, format: :json
          job = assert_success_async_response!

          result = Viz::Data::GeneratorService.async_result(job)
          expect(result.values).to eq([['1']])
        end
      end

      context 'preview from a dataset with binding from front end' do
        before do
          FeatureToggle.toggle_global(AmlStudio::Project::FT_UTILIZE_FE_PROCESSING, true)
        end

        it 'successfully preview from a dataset' do
          # expect not to call aml server
          expect_any_instance_of(Aml::Client).not_to receive(:get_bindings_by_fqns_from_wd)
          expect_any_instance_of(Aml::Client).not_to receive(:get_bindings_by_fqns)

          dataset_binding = {
            name: 'test',
            _type: 'Dataset',
            value: {
              name: 'test',
              label: 'Test data set',
              description: 'ahihi',
              owner: '<EMAIL>',
              distinct_rows: true,
              metric: {
                test_metric: {
                  name: 'test_metric',
                  hidden: false,
                  label: 'Test Metric',
                  type: 'number',
                  definition: {
                    __type__: 'Heredoc',
                    name: 'aql',
                    content: 'count(data_modeling_orders.id) ',
                  },
                  __childIdx__: 6,
                  __doc__: '',
                },
              },
              __fqn__: 'test',
              data_source_name: 'pg',
              models: [
                {
                  name: 'data_modeling_orders',
                  label: 'Orders',
                  description: '',
                  owner: '<EMAIL>',
                  data_source_name: 'pg',
                  dimension: {
                    id: {
                      name: 'id',
                      hidden: false,
                      definition: {
                        __type__: 'Heredoc',
                        name: 'sql',
                        content: '{{ #SOURCE.id }}',
                      },
                      label: 'Id',
                      type: 'number',
                      __childIdx__: 4,
                      __doc__: '',
                    },
                  },
                  measure: {},
                  __fqn__: 'data_modeling_orders',
                  type: 'table',
                  table_name: '\'data_modeling\'.\'orders\'',
                  __doc__: '',
                  __type__: 'TableModel',
                },
                {
                  name: 'simple',
                  label: 'simple',
                  description: '',
                  owner: '<EMAIL>',
                  data_source_name: 'pg',
                  models: [],
                  dimension: {
                    id: {
                      name: 'id',
                      hidden: false,
                      definition: {
                        __type__: 'Heredoc',
                        name: 'sql',
                        content: '{{ #SOURCE.id }}',
                      },
                      label: 'Id',
                      type: 'number',
                      __childIdx__: 4,
                      __doc__: '',
                    },
                  },
                  __fqn__: 'simple',
                  type: 'query',
                  query: {
                    __type__: 'Heredoc',
                    name: 'sql',
                    content: 'select 1 id',
                  },
                  __doc__: '',
                  __type__: 'QueryModel',
                },
              ],
              relationships: [],
              __doc__: '',
              __type__: 'Dataset',
            },
          }

          params[:binding_json] = dataset_binding
          post :submit_generate, params: params, format: :json
          job = assert_success_async_response!

          result = Viz::Data::GeneratorService.async_result(job)

          # expect to have result
          expect(result.values).to eq([['1']])
        end
      end
    end

    describe 'belong to correct job queue' do
      it 'belongs to adhoc_query queue' do
        post :submit_generate, format: :json, params: params
        job = assert_async_response!
        expect(job.tag).to eq 'adhoc_query'
      end
    end

    context 'Dashboard V4 (Dashboard as code)' do
      let(:admin) { get_test_admin }
      let!(:new_tenant) do
        FactoryBot.create :tenant
      end

      let(:data_set) do
        ds = FactoryBot.create :data_set, root_model_id: nil
        ds.data_models << query_data_model
        ds.tenant = new_tenant
        ds.save!
        ds.reload
        ds
      end

      let(:dashboard_new_tenant) do
        create(
          :dashboard,
          {
            **dashboard_v4_table_timezone_params,
            tenant_id: new_tenant.id,
          },
        )
      end

      include_context 'dashboards_v4'
      before do
        FeatureToggle.toggle_global('dashboards_v4:creation', true)
        FeatureToggle.toggle_global('dashboards_v3:creation', true)
      end

      context 'permission' do
        let(:params_edit_reporting) do
          params = get_params_submit_gererate_dashboard_4(dashboard_table_timezone)
          #  the viz different with dashboard, we check in permission dataset
          params[:viz_setting][:fields]['table_fields'] = [params[:viz_setting][:fields]['table_fields'].first]
          params[:source] = {
            type: 'VizBlock',
            id: "#{dashboard_table_timezone.id}:unamerandom",
            action: 'edit',
          }
          params
        end

        let(:params_edit_modeling) do
          params = get_params_submit_gererate_dashboard_4(dashboard_aml_dataset_based)

          #  the viz different with dashboard, we check in permission dataset
          params[:viz_setting][:fields]['table_fields'] = [params[:viz_setting][:fields]['table_fields'].first]
          params[:source] = {
            type: 'VizBlock',
            id: "#{dashboard_table_timezone.id}:unamerandom",
            action: 'preview',
          }
          params
        end

        let(:params_view_reporting) do
          params = get_params_submit_gererate_dashboard_4(dashboard_table_timezone)
        end

        let(:params_view_modeling) do
          params = get_params_submit_gererate_dashboard_4(dashboard_aml_dataset_based)
        end

        def check_permission_error(response, message)
          assert_response_status!(403)
          errors = JSON.parse(response.body)['errors']
          expect(errors.size).to eq 1
          expect(errors.first).to match(message)
        end

        def check_submit_success(response)
          assert_success_response!

          job = Job.find(JSON.parse(response.body)['job_id'])
          assert_success_job!(job)
        end

        context 'view dashboard' do
          it 'in dashboard modeling' do
            post :submit_generate, format: :json, params: params_view_modeling
            check_submit_success(response)
          end

          it 'in dashboard reporting' do
            post :submit_generate, format: :json, params: params_view_reporting
            check_submit_success(response)
          end
        end

        context 'with biz user' do
          before do
            sign_in biz_user
          end

          context 'raise error permission' do
            context 'view dashboard' do
              it 'in reporting' do
                post :submit_generate, format: :json, params: params_view_reporting
                check_permission_error(response, /No permission to explore result of VizBlock/)
              end

              it 'validate viz setting when share dashboard to access read dashboard and not access dataset' do
                admin.share(biz_user, :read, dashboard_table_timezone)
                post :submit_generate, format: :json, params: params_view_reporting
                check_submit_success(response)
              end
            end

            context 'edit dashboard' do
              it 'in dashboard reporting' do
                post :submit_generate, format: :json, params: params_edit_reporting
                check_permission_error(response, /You do not have permission to explore this Dataset/)
              end

              it 'in dashboard modeling' do
                post :submit_generate, format: :json, params: params_edit_modeling
                check_permission_error(response, /You do not have permission to explore this Dataset/)
              end
            end
          end
        end

        context 'editing dashboard' do
          it 'in dashboard reporting' do
            post :submit_generate, format: :json, params: params_edit_reporting
            check_submit_success(response)
          end

          it 'in dashboard modelling' do
            post :submit_generate, format: :json, params: params_edit_modeling
            check_submit_success(response)
          end
        end
      end

      context 'cache settings' do
        def check_cache_duration_correct!(response, duration)
          assert_success_response!

          job = Job.find(JSON.parse(response.body)['job_id'])
          assert_success_job!(job)
          cache_key = job.data[:cache_key]
          metadata = PostgresCache.fetch_metadata(cache_key)

          test_cache_duration!(metadata, duration)
        end

        it 'get correct cache settings from dashboard' do
          post :submit_generate, format: :json,
                                 params: get_params_submit_gererate_dashboard_4(dashboardv4_with_settings)
          check_cache_duration_correct!(response, dashboardv4_with_settings.definition['settings']['cache_duration'])
        end

        context 'default settings' do
          it 'don\'t have default cache settings in tenant settings' do
            post :submit_generate, format: :json,
                                   params: get_params_submit_gererate_dashboard_4(dashboard_aml_dataset_based)
            check_cache_duration_correct!(response, 24 * 60)
          end

          it 'default cache settings in tenant settings' do
            admin.tenant.set_setting('report_cache_duration', 7200)

            post :submit_generate, format: :json,
                                   params: get_params_submit_gererate_dashboard_4(dashboard_aml_dataset_based)
            check_cache_duration_correct!(response, 7200)
          end
        end
      end
    end

    context 'Allow public user bust cache' do
      include_context 'dashboards_v4'
      let(:bust_cache) { false }
      let(:params) {
        get_params_submit_gererate_dashboard_4(dashboard_table_timezone, bust_cache: bust_cache)
      }

      shared_examples 'works fine' do
        it 'submit generate success' do
          post :submit_generate, format: :json, params: params
          assert_success_response!

          job = Job.find(JSON.parse(response.body)['job_id'])
          assert_success_job!(job)
        end
      end

      shared_examples 'permission error' do
        it 'raise permission error' do
          post :submit_generate, format: :json, params: params

          assert_response_status!(403)
          errors = JSON.parse(response.body)['errors']
          expect(errors.size).to eq 1
          expect(errors.first).to match(/do not have permission/)
        end
      end

      shared_examples 'submit generate success both bust_cache and non bust_cache' do
        context 'no bust_cache' do
          it_behaves_like 'works fine'
        end

        context 'bust_cache' do
          let(:bust_cache) { true }
          it_behaves_like 'works fine'
        end
      end

      context 'enabled public user bust cache' do
        before do
          FeatureToggle.toggle_global('embed_link:allow_public_user_bust_cache', true)
        end

        context 'non public user' do
          it_behaves_like 'submit generate success both bust_cache and non bust_cache'
        end


        context 'public user' do
          include_context 'setup_login_embed_user'
          let(:source) { dashboard_table_timezone }

          context 'use v3_configs' do
            before do
              FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, false)
            end

            it_behaves_like 'submit generate success both bust_cache and non bust_cache'
          end

          context 'use portal configs' do
            before do
              FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, true)
            end

            it_behaves_like 'submit generate success both bust_cache and non bust_cache'
          end
        end
      end

      context 'disabled public user bust cache' do
        before do
          FeatureToggle.toggle_global('embed_link:allow_public_user_bust_cache', false)
        end

        context 'non public user' do
          it_behaves_like 'submit generate success both bust_cache and non bust_cache'
        end

        context 'public user' do
          include_context 'setup_login_embed_user'
          let(:source) { dashboard_table_timezone }

          context 'bust_cache' do
            let(:bust_cache) { true }

            context 'use v3_configs' do
              before do
                FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, false)
              end

              it_behaves_like 'permission error'
            end

            context 'use portal configs' do
              before do
                FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, true)
              end

              it_behaves_like 'permission error'
            end
          end

          context 'no bust_cache' do
            context 'use v3_configs' do
              before do
                FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, false)
              end

              it_behaves_like 'works fine'
            end

            context 'use portal configs' do
              before do
                FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, true)
              end

              it_behaves_like 'works fine'
            end
          end
        end
      end
    end

    context 'Allow only authorized data set explore when edit dashboard widget' do
      let(:viz_setting) do
        FactoryBot.create(
          :viz_setting,
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                custom_label: nil, format: {},
                path_hash: { model_id: rm.name, field_name: 'name' },
              },
              {
                path_hash: { model_id: rm.name, field_name: 'age' },
              },
            ],
          },
          format: {},
          filters: [],
          settings: {},
        )
      end
      let(:dashboard) do
        FactoryBot.create(:dashboard, tenant: tenant, version: 3,
                                      settings: { timezone: nil, allow_to_change_timezone: true }, owner: admin,)
      end
      let(:query_report) do
        FactoryBot.create(:query_report, data_set: data_set, viz_setting: viz_setting, owner: admin)
      end
      let(:dashboard_widget) do
        FactoryBot.create(:dashboard_widget, dashboard: dashboard, source: query_report)
      end

      let(:data_set_2) do
        ds = FactoryBot.create :data_set, root_model_id: nil
        ds.data_models << rm
        ds.save!
        ds.reload
        ds
      end

      let(:source_params) do
        {
          type: 'DashboardWidget',
          id: dashboard_widget.id,
          action: 'edit',
        }
      end

      let(:params) do
        {
          viz_setting: viz_setting,
          root_model_id: nil,
          data_set_id: data_set_2.id,
          options: {
            page: 1,
            page_size: 25,
            show_aggregated: false,
            sort: [],
            sort_by_column_type: false,
            exploring: true,
            bust_cache: false,
            force_cached: false
          },
          source: source_params,
        }
      end

      it 'works fine' do
        post :submit_generate, format: :json, params: params
        assert_success_response!

        job = Job.find(JSON.parse(response.body)['job_id'])
        assert_success_job!(job)
      end

      context "raise error" do
        let(:source_params) do
          {
            type: 'DashboardWidget',
            id: dashboard_widget.id,
          }
        end

        it 'raise error when different data set and no action edit' do
          post :submit_generate, format: :json, params: params

          assert_response_status!(403)
          errors = JSON.parse(response.body)['errors']
          expect(errors.size).to eq 1
          expect(errors.first).to match(/Dataset ID mismatches dataset of explored object/)
        end
      end
    end
  end

  describe '#submit_export' do
    include_context 'data_modeling_schema'
    before do
      FeatureToggle.toggle_global('data_source:enable_schema_info', true)
      DataSourceVersions::SchemaSynchronizationService.new(get_test_ds).execute
    end

    include_context 'data_explore_ctx'

    let(:orders_model) do
      DataModel.from_table(ds_id: ds.id, fqname: 'data_modeling.orders')
    end
    let(:data_model) { orders_model }
    let(:viz_setting) { FactoryBot.create :viz_setting }
    let(:dashboard) do
      FactoryBot.create :dashboard, title: 'Product Overview', tenant_id: admin.tenant_id
    end
    let(:model_report) do
      FactoryBot.create :query_report_new, root_model: data_model
    end
    let(:business_user) { get_test_user }

    let(:dashboard_wg) do
      FactoryBot.create(
        :dashboard_widget,
        source_type: 'QueryReport',
        source_id: model_report.id,
        dashboard: dashboard,
        tenant: dashboard.tenant,
      )
    end

    before do
      request.headers['Accept'] = 'application/json'
      request.headers['Content-Type'] = 'application/json'

      model_report.viz_setting = viz_setting
      model_report.save!
      model_report.reload

      S3::ExportAndDownload.mocked = true
    end

    after do
      S3::ExportAndDownload.mocked = false
    end

    context 'with timezone' do
      include_context 'timezone_dynamic_dashboard'
      before do
        @results = nil
        allow(Viz::Exporting::ExporterService).to receive(:export_to_filepath).and_wrap_original do |method, *args, **kwargs|
          results = method.call(*args, **kwargs)
          @results = CSV.parse(results)
          results
        end
      end

      let(:expected_data) { [] }

      shared_examples 'submit export widget' do
        it 'exports correct data' do
          post(
            :submit_export,
            params: {
              viz_setting: timezone_viz_setting,
              data_set_id: query_model_data_set.id,
              include_chart: false,
              export_format: 'csv',
              source: { type: 'DashboardWidget', id: timezone_widget.id, action: 'explore' },
            },
            format: :json,
          )

          assert_success_async_response!
          expect(@results).to match_array(expected_data)
        end
      end

      context 'timezone enabled' do
        context 'dashboard timezone enabled' do
          let(:expected_data) do
            [
              ['Val', 'Time', 'Date'],
              ['10', '2021-10-20 23:30:00 +0000', '2021-10-20'],
              ['20', '2021-10-21 06:00:00 +0000', '2021-10-20'],
              ['30', '2021-10-21 19:00:00 +0000', '2021-10-21'],
            ]
          end

          it_behaves_like 'submit export widget'

          context 'with filters' do
            let(:timezone_viz_setting_filters) do
              [
                time_condition.merge(
                  path_hash: {
                    field_name: 'time',
                    model_id: query_data_model.id,
                  },
                ),
              ]
            end
            let(:expected_data) do
              [
                ['Val', 'Time', 'Date'],
                ['20', '2021-10-21 06:00:00 +0000', '2021-10-20'],
                ['30', '2021-10-21 19:00:00 +0000', '2021-10-21'],
              ]
            end

            it_behaves_like 'submit export widget'
          end
        end

        context 'dashboard timezone disabled' do
          before do
            FeatureToggle.toggle_global(Timezone::Helper::FT_DASHBOARD_TIMEZONE, false)
          end

          let(:expected_data) do
            [
              ['Val', 'Time', 'Date'],
              ['10', '2021-10-21 00:30:00 +0000', '2021-10-20'],
              ['20', '2021-10-21 07:00:00 +0000', '2021-10-20'],
              ['30', '2021-10-21 20:00:00 +0000', '2021-10-21'],
            ]
          end

          it_behaves_like 'submit export widget'
        end
      end

      context 'timezone disabled' do
        before do
          FeatureToggle.toggle_global(Tenant::FT_NEW_TIMEZONE_CONFIG, false)
        end

        let(:expected_data) do
          [
            ['Val', 'Time', 'Date'],
            ['10', '2021-10-20 16:30:00 +0000', '2021-10-20'],
            ['20', '2021-10-20 23:00:00 +0000', '2021-10-20'],
            ['30', '2021-10-21 12:00:00 +0000', '2021-10-21'],
          ]
        end

        it_behaves_like 'submit export widget'
      end
    end

    context 'with Date Drill' do
      include_context 'simple_query_model_for_date_drill'

      let(:viz_setting) do
        FactoryBot.create(
          :viz_setting,
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                custom_label: nil, format: {},
                transformation: 'datetrunc month',
                path_hash: { model_id: query_data_model.name, field_name: 'date_and_time' },
              },
              {
                custom_label: nil, format: {}, aggregation: 'sum',
                path_hash: { model_id: query_data_model.name, field_name: 'value' },
              },
            ],
          },
          format: {},
          filters: [{
            operator: 'transform_date_drill',
            path_hash: { model_id: query_data_model.name, field_name: 'date_and_time' },
            values: ['datetrunc quarter'],
          }],
          settings: {},
        )
      end
      let(:dashboard) do
        FactoryBot.create(:dashboard, tenant: tenant, version: 3,
                                      settings: { timezone: tz, allow_to_change_timezone: true }, owner: admin,)
      end
      let(:query_report) do
        FactoryBot.create(:query_report, data_set: data_set, viz_setting: viz_setting, owner: admin)
      end
      let(:dashboard_widget) do
        FactoryBot.create(:dashboard_widget, dashboard: dashboard, source: query_report)
      end

      before do
        FeatureToggle.toggle_global(DynamicFilter::FT_INTERACTIVE_CONTROL_DATE_DRILL, true)
      end

      before do
        @results = nil
        allow(Viz::Exporting::ExporterService).to receive(:export_to_filepath).and_wrap_original do |method, *args, **kwargs|
          results = method.call(*args, **kwargs)
          @results = CSV.parse(results)
          results
        end
      end

      def assert_return_expected_data!(expected_data)
        post(
          :submit_export,
          params: {
            viz_setting: viz_setting,
            data_set_id: data_set.id,
            include_chart: false,
            export_format: 'csv',
            source: { type: 'DashboardWidget', id: dashboard_widget.id, action: 'explore' },
          },
          format: :json,
        )

        assert_success_async_response!
        expect(@results).to match_array(expected_data)
      end

      it 'exports correct data' do
        assert_return_expected_data!([
          ['Quarter Date And Time', 'Sum of Value'],
          ['2019-10-01 00:00:00.000000', '60'],
          ['2020-01-01 00:00:00.000000', '220'],
          ['2020-04-01 00:00:00.000000', '80'],
        ])
      end

      context 'when the same date dimension is selected with multiple granularities' do
        let(:viz_setting) do
          super().tap do |vs|
            vs.viz_type = 'pivot_table'
            vs.fields = {
              pivot_data: {
                columns: [
                  {
                    custom_label: nil, format: {},
                    transformation: 'datetrunc year',
                    path_hash: { model_id: query_data_model.name, field_name: 'date_and_time' },
                  },
                ],
                rows: [
                  {
                    custom_label: nil, format: {},
                    transformation: 'datetrunc month',
                    path_hash: { model_id: query_data_model.name, field_name: 'date_and_time' },
                  },
                ],
                values: [
                  {
                    custom_label: nil, format: {}, aggregation: 'sum',
                    path_hash: { model_id: query_data_model.name, field_name: 'value' },
                  },
                ],
              },
            }
          end
        end

        it 'only applies to the first one (based on fields collected by each schema)' do
          assert_return_expected_data!([
            ['Quarter Date And Time', 'Year Date And Time', 'Sum of Value'],
            [nil, '2019-10-01 00:00:00.000000', nil],
            [nil, '2020-01-01 00:00:00.000000', nil],
            [nil, '2020-04-01 00:00:00.000000', nil],
            ['2019-10-01 00:00:00.000000', '2019-01-01 00:00:00.000000', '60'],
            ['2020-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '220'],
            ['2020-04-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '80'],
          ])
        end
      end
    end

    it 'returns ok if biz user is shared dashboard' do
      admin.share(business_user, :read, dashboard)
      sign_in business_user
      post(
        :submit_export,
        params: { viz_setting: viz_setting, root_model_id: data_model.id, include_chart: false,
                  source: { type: 'DashboardWidget', id: dashboard_wg.id }, },
        format: :json,
      )
      assert_success_async_response!
    end

    it 'returns permission denided if biz_user is not shared dashboard' do
      sign_in business_user
      post(
        :submit_export,
        params: { viz_setting: viz_setting, root_model_id: data_model.id, include_chart: false,
                  source: { type: 'DashboardWidget', id: dashboard_wg.id }, },
        format: :json,
      )
      assert_response_status!(403)
    end

    it 'returns ok if biz user is shared query report' do
      admin.share(business_user, :read, model_report)
      sign_in business_user
      post(
        :submit_export,
        params: { viz_setting: viz_setting, root_model_id: data_model.id, include_chart: false,
                  source: { type: 'QueryReport', id: model_report.id }, },
        format: :json,
      )
      assert_success_async_response!
    end

    it 'returns permission denided if biz user is not shared query report' do
      sign_in business_user
      post(
        :submit_export,
        params: { viz_setting: viz_setting, root_model_id: data_model.id, include_chart: false,
                  source: { type: 'QueryReport', id: model_report.id }, },
        format: :json,
      )
      assert_response_status!(403)
    end

    it 'returns invalid parameter if source type is not QueryReport or DashboardWidget' do
      sign_in business_user
      post(
        :submit_export,
        params: { viz_setting: viz_setting, root_model_id: data_model.id, include_chart: false,
                  source: { type: 'invalid', id: model_report.id }, },
        format: :json,
      )
      assert_response_status!(422)
      expect(response.body).to match(/source type is not allowed/i)
    end

    it 'returns permission denided if params does not have source params and user is biz_user' do
      sign_in business_user
      post(
        :submit_export,
        params: { viz_setting: viz_setting, root_model_id: data_model.id, include_chart: false },
        format: :json,
      )
      assert_response_status!(403)
    end
  end

  describe '#save_as_model_report' do
    let(:rm_sql) { "select 'sql generated by data model'" }

    before do
      request.content_type = 'application/json'
    end

    it 'creates a model report' do
      post :save_as_model_report, format: :json, params: {
        report: {
          title: 'I have a bad feeling about this',
          is_adhoc: false,
          category_id: 0,
          data_set_id: data_set.id,
        },
        viz_setting: {
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                custom_label: nil, format: {},
                path_hash: { model_id: rm.id, field_name: 'name' },
              },
            ],
          },
          filters: [],
        },
      }

      r = QueryReport.last

      expect(response.status).to eq 200
      expect(r.data_set_id).to eq data_set.id
      expect(r.viz_setting.viz_type).to eq 'data_table'

      job_id = JSON.parse(response.body)['job_id']
      post :save_report_result, format: :json, params: { job_id: job_id }
      report_result = JSON.parse(response.body)['data']
      expect(report_result['widget']).to be_nil
    end

    it 'creates a model report with condition groups' do
      post :save_as_model_report, format: :json, params: {
        report: {
          title: 'Condition Group Report',
          is_adhoc: false,
          category_id: 0,
          data_set_id: data_set.id,
        },
        viz_setting: {
          viz_type: 'data_table',
          fields: {
            table_fields: [
              {
                custom_label: nil, format: {},
                path_hash: { model_id: rm.id, field_name: 'name' },
              },
            ],
          },
          filters: [],
          amql: {
            filters: [
              FactoryBot.build(:condition_group),
            ],
          },
        },
      }

      r = QueryReport.last

      expect(response.status).to eq 200
      expect(r.data_set_id).to eq data_set.id
      expect(r.viz_setting.amql[:filters].length).to eq 1
    end

    context 'for adhoc report' do
      let(:dashboard) { FactoryBot.create :dashboard }

      it 'creates an adhoc model report' do
        post :save_as_model_report, format: :json, params: {
          report: {
            title: 'I have a bad feeling about this',
            is_adhoc: true,
            dashboard_id: dashboard.id,
            data_set_id: data_set.id,
          },
          viz_setting: {
            viz_type: 'data_table',
            fields: {
              table_fields: [
                {
                  custom_label: nil, format: {},
                  path_hash: { model_id: rm.id, field_name: 'name' },
                },
              ],
            },
            filters: [],
          },
        }

        r = QueryReport.last
        dashboard.reload

        expect(response.status).to eq 200
        expect(r.data_set_id).to eq data_set.id
        expect(r.cache_setting).not_to be_nil
        expect(r.viz_setting.viz_type).to eq 'data_table'
        expect(dashboard.dashboard_widgets.size).to eq 1

        job_id = JSON.parse(response.body)['job_id']
        post :save_report_result, format: :json, params: { job_id: job_id }
        report_result = JSON.parse(response.body)['data']
        expect(report_result['widget']).not_to be_nil
      end
    end

    context 'user is analyst' do
      let(:dashboard) { FactoryBot.create :dashboard }

      before { sign_in analyst }

      it 'can crud model report if dataset is shared when' do
        admin.share(analyst, :read, data_set)

        post :save_as_model_report, format: :json, params: {
          report: {
            title: 'I have a bad feeling about this',
            is_adhoc: true,
            dashboard_id: dashboard.id,
            data_set_id: data_set.id,
          },
          viz_setting: {
            viz_type: 'data_table',
            fields: {
              table_fields: [
                {
                  custom_label: nil, format: {},
                  path_hash: { model_id: rm.id, field_name: 'name' },
                },
              ],
            },
            filters: [],
          },
        }

        r = QueryReport.last
        expect(response.status).to eq 200
        expect(r.data_set_id).to eq data_set.id

        job_id = JSON.parse(response.body)['job_id']
        post :save_report_result, format: :json, params: { job_id: job_id }

        expect(analyst.can?(:crud, r)).to be(true)
      end

      it 'cannot create model report if dataset is not shared' do
        post :save_as_model_report, format: :json, params: {
          report: {
            title: 'I have a bad feeling about this',
            is_adhoc: true,
            dashboard_id: dashboard.id,
            data_set_id: data_set.id,
          },
          viz_setting: {
            viz_type: 'data_table',
            fields: {
              table_fields: [
                {
                  custom_label: nil, format: {},
                  path_hash: { model_id: rm.id, field_name: 'name' },
                },
              ],
            },
            filters: [],
          },
        }

        expect(response.status).to eq 403
      end
    end
    context 'Hard restriction' do
      include_context 'billing/hard_restriction'
      it 'restrict create new report' do
        post :save_as_model_report, format: :json, params: {
          report: {
            title: 'I have a bad feeling about this',
            is_adhoc: true,
            dashboard_id: dashboard.id,
            data_set_id: data_set.id,
          },
          viz_setting: {
            viz_type: 'data_table',
            fields: {
              table_fields: [
                {
                  custom_label: nil, format: {},
                  path_hash: { model_id: rm.id, field_name: 'name' },
                },
              ],
            },
            filters: [],
          },
        }

        expect(response.status).to eq 422
        error_msgs = JSON.parse(response.body)
        expect(error_msgs['errors'][0]).to eq('Your current usage has exceeded your current plan and you cannot create or edit objects. Please upgrade your plan.')
      end
    end
  end

  describe '#submit_fetch_field_suggestions' do
    include_context 'simple_query_model'
    let(:data_set) do
      ds = FactoryBot.create :data_set, root_model_id: nil
      ds.data_models << query_data_model
      ds.save!
      ds.reload
      ds
    end
    let(:admin) { get_test_admin }
    let(:current_user) { admin }
    let(:job) { FactoryBot.create :job, tenant: get_test_tenant, user: admin }
    let(:field_name) { 'name' }
    let!(:params) do
      {
        data_set_id: data_set.id,
        data_model_id: query_data_model.id,
        field_name: field_name,
        permission_rules: {},
      }
    end
    let(:fetch_field_suggestions_params) do
      {
        data_set: data_set,
        data_model: query_data_model,
        field_name: field_name,
        permission_rules: DataModeling::Values::PermissionRules::Object.new,
        query_processing_timezone: 'Etc/UTC',
      }
    end

    before do
      sign_in current_user
      request.content_type = 'application/json'
    end

    it 'returns the distinct values of the specified field' do
      post :submit_fetch_field_suggestions, params: params, format: :json
      assert_success_response!
      res = JSON.parse(response.body)
      job = Job.find(res['async_job_id'])
      assert_success_job!(job)

      res = job.fetch_cache_data.rsk
      expect(res[:options]).to match_array([{ value: 'alice' }, { value: 'bob' }])
      expect(res[:options_count]).to eq 2
    end

    context 'analyst' do
      let(:current_user) { get_test_analyst }

      it 'raises permission denied' do
        post :submit_fetch_field_suggestions, params: params, format: :json
        assert_response_status!(403)
      end

      context 'with dynamic_filter_id submitted' do
        let(:dashboard) { FactoryBot.create :dashboard, version: 3 }
        let(:field_path) { DataModeling::Values::FieldPath.new(field_name: 'name', model_id: query_data_model.id) }
        let(:filter_source) { FactoryBot.create :dm_field_filter_source, data_set: data_set, field_path: field_path }
        let(:filter_definition) { FactoryBot.create :dynamic_filter_definition, filter_source: filter_source }
        let!(:dynamic_filter) do
          FactoryBot.create :dynamic_filter, definition: filter_definition, filter_holdable: dashboard, order: 2
        end
        let(:params) do
          {
            dynamic_filter_id: dynamic_filter.id,
          }
        end

        it 'raises permission denied' do
          post :submit_fetch_field_suggestions, params: params, format: :json
          assert_response_status!(403)
        end

        context 'data source is shared' do
          before do
            admin.share(current_user, :read, data_set.data_source)
          end

          it 'returns the field suggestions for the dynamic filter' do
            post :submit_fetch_field_suggestions, params: params, format: :json
            job = assert_success_async_response!(['async_job_id'])

            res = job.fetch_cache_data.rsk
            expect(res[:options]).to match_array([{ value: 'alice' }, { value: 'bob' }])
            expect(res[:options_count]).to eq 2
          end
        end

        context 'data set is shared' do
          before do
            admin.share(current_user, :read, data_set)
          end

          it 'returns the field suggestions for the dynamic filter' do
            post :submit_fetch_field_suggestions, params: params, format: :json
            job = assert_success_async_response!(['async_job_id'])

            res = job.fetch_cache_data.rsk
            expect(res[:options]).to match_array([{ value: 'alice' }, { value: 'bob' }])
            expect(res[:options_count]).to eq 2
          end
        end

        context 'dashboard is explicitly shared' do
          before do
            admin.share(current_user, :read, dashboard)
          end

          it 'returns the field suggestions for the dynamic filter' do
            post :submit_fetch_field_suggestions, params: params, format: :json
            job = assert_success_async_response!(['async_job_id'])

            res = job.fetch_cache_data.rsk
            expect(res[:options]).to match_array([{ value: 'alice' }, { value: 'bob' }])
            expect(res[:options_count]).to eq 2
          end
        end
      end
    end

    context 'with aml binding' do
      include_context 'aml_studio_basic'

      let(:dataset_binding) do
        {
          name: 'test',
          _type: 'Dataset',
          value: {
            name: 'test',
            label: 'Test data set',
            description: 'ahihi',
            owner: '<EMAIL>',
            distinct_rows: true,
            metric: {},
            __fqn__: 'test',
            data_source_name: 'pg',
            models: [
              {
                name: 'simple',
                label: 'simple',
                description: '',
                owner: '<EMAIL>',
                data_source_name: 'pg',
                models: [],
                dimension: {
                  name: {
                    name: 'name',
                    hidden: false,
                    definition: {
                      __type__: 'Heredoc',
                      name: 'sql',
                      content: '{{ #SOURCE.name }}',
                    },
                    label: 'name',
                    type: 'text',
                    __childIdx__: 4,
                    __doc__: '',
                  },
                },
                __fqn__: 'simple',
                type: 'query',
                query: {
                  __type__: 'Heredoc',
                  name: 'sql',
                  content: "select 'alice' as name",
                },
                __doc__: '',
                __type__: 'QueryModel',
              },
            ],
            relationships: [],
            __doc__: '',
            __type__: 'Dataset',
          },
        }
      end

      before do
        FeatureToggle.toggle_global(AmlStudio::Project::FT_UTILIZE_FE_PROCESSING, true)
      end

      it 'works' do
        # expect not to call aml server
        expect_any_instance_of(Aml::Client).not_to receive(:get_bindings_by_fqns_from_wd)
        expect_any_instance_of(Aml::Client).not_to receive(:get_bindings_by_fqns)

        params[:binding_json] = dataset_binding
        params[:project_id] = project.id
        params[:data_set_id] = 'test'
        params[:data_model_id] = 'simple'

        within_aml_studio_aml_env do
          request.headers['X-Holistics-Aml-Env'] = 'aml_studio'

          post :submit_fetch_field_suggestions, params: params, format: :json

          job = assert_success_async_response!('async_job_id')

          res = job.fetch_cache_data.rsk
          expect(res[:options]).to match_array([{ value: 'alice' }])
          expect(res[:options_count]).to eq 1
        end
      end
    end

    context 'parent-child filters' do
      context 'with timezone' do
        include_context 'timezone_dynamic_dashboard'

        let(:child_filter_source) do
          FactoryBot.create :dm_field_filter_source, data_set_id: query_model_data_set.id,
                                                     field_path: { model_id: query_data_model.id, field_name: 'name' }
        end
        let(:child_filter_condition) { { operator: 'is', values: [] } }
        let(:child_filter_definition) do
          FactoryBot.create :dynamic_filter_definition, default_condition: child_filter_condition,
                                                        filter_source: child_filter_source, filter_type: DynamicFilters::Constants::FilterTypes::NUMBER
        end
        let(:child_filter) do
          FactoryBot.create :dynamic_filter, dynamic_filter_holdable: timezone_dashboard, order: 2,
                                             dynamic_filter_definition: child_filter_definition
        end
        let(:parent_child_mapping) do
          field_path = DataModeling::Values::FieldPath.new(field_name: 'time', model_id: query_data_model.id)
          FactoryBot.create :dynamic_filter_mapping, viz_conditionable: child_filter, dynamic_filter: time_filter,
                                                     field_path: field_path
        end
        let(:params_parent_child) do
          {
            data_set_id: query_model_data_set.id,
            data_model_id: query_data_model.id,
            field_name: 'name',
            dynamic_filter_id: child_filter.id,
            permission_rules: {},
            viz_conditions: [
              viz_condition,
            ],
          }
        end

        it 'filters correct timezone' do
          post :submit_fetch_field_suggestions, params: params_parent_child, format: :json
          job = assert_success_async_response!(['async_job_id'])

          res = job.fetch_cache_data.rsk
          expect(res[:options]).to match_array([{ value: 'Mina' }, { value: 'Ne' }])
          expect(res[:options_count]).to eq 2
        end
      end
    end

    context 'cached' do
      before do
        with_fake_async_context(job) do
          DataModeling::Services::FetchFieldSuggestions.coerce_from(fetch_field_suggestions_params).call
        end
      end

      it 'returns result directly' do
        post :submit_fetch_field_suggestions, params: params, format: :json
        assert_success_response!

        res = JSON.parse(response.body)['data'].rsk
        expect(res[:options]).to match_array([{ value: 'alice' }, { value: 'bob' }])
        expect(res[:options_count]).to eq 2
      end

      context 'with bust_cache param' do
        it 'returns result via job' do
          post :submit_fetch_field_suggestions, params: params.merge(bust_cache: true), format: :json
          assert_success_response!
          res = JSON.parse(response.body)
          job = Job.find(res['async_job_id'])
          assert_success_job!(job)

          res = job.fetch_cache_data.rsk
          expect(res[:options]).to match_array([{ value: 'alice' }, { value: 'bob' }])
          expect(res[:options_count]).to eq 2
        end
      end

      context 'fetch suggestions for another field, not the cached one' do
        let(:query_model_sql) do
          <<~SQL
            with t(name, age, address) as (
              values('a', 1, 'first'),
                ('b', 2, 'first'),
                ('c', 3, 'second'),
                ('d', 4, 'third')
            )
            select * from t
          SQL
        end

        it 'returns result via job' do
          post :submit_fetch_field_suggestions, params: params.merge(field_name: 'address'), format: :json
          assert_success_response!
          res = JSON.parse(response.body)
          job = Job.find(res['async_job_id'])
          assert_success_job!(job)

          res = job.fetch_cache_data.rsk
          expect(res[:options]).to match_array([{ value: 'first' }, { value: 'second' }, { value: 'third' }])
          expect(res[:options_count]).to eq 3
        end
      end
    end

    context 'with search query' do
      before do
        params[:q] = 'b'
      end

      it 'returns matching values' do
        # expect(Job).to receive(:update_running_step).exactly(5).times.and_call_original

        post :submit_fetch_field_suggestions, params: params, format: :json
        assert_success_response!
        res = JSON.parse(response.body)
        job = Job.find(res['async_job_id'])
        assert_success_job!(job)
        expect(job.running_info[:step]).to eq 'processing_result'
        expect(job.running_info[:dbtype]).to eq 'postgresql'

        res = job.fetch_cache_data.rsk
        expect(res[:options]).to match_array([{ value: 'bob' }])
        expect(res[:options_count]).to eq 1
      end

      context 'cached' do
        before do
          with_fake_async_context(job) do
            DataModeling::Services::FetchFieldSuggestions.coerce_from(fetch_field_suggestions_params).call
          end
        end

        it 'returns result directly' do
          expect(Job).to receive(:update_running_step).exactly(3).times.and_call_original
          expect(Job).to receive(:update_running_info).once.and_call_original

          post :submit_fetch_field_suggestions, params: params, format: :json
          assert_success_response!

          res = JSON.parse(response.body)['data'].rsk
          expect(res[:options]).to match_array([{ value: 'bob' }])
          expect(res[:options_count]).to eq 1
        end

        context 'with different search query' do
          it 'returns result directly' do
            post :submit_fetch_field_suggestions, params: params.merge(q: 'a'), format: :json
            assert_success_response!

            res = JSON.parse(response.body)['data'].rsk
            expect(res[:options]).to match_array([{ value: 'alice' }])
            expect(res[:options_count]).to eq 1
          end
        end
      end

      context 'non-text field' do
        let(:field_name) { 'age' }

        it 'returns empty result' do
          post :submit_fetch_field_suggestions, params: params, format: :json
          assert_success_response!

          res = JSON.parse(response.body)['data'].rsk
          expect(res[:options]).to match_array([])
          expect(res[:options_count]).to eq 0
        end
      end

      context 'text field but all values are numbers' do
        let(:query_model_sql) do
          <<~SQL
            with t(name, age) as (
              values('1', 1),
                ('2', 2),
                ('25', 3)
            )
            select * from t
          SQL
        end

        it 'searchs correctly' do
          post :submit_fetch_field_suggestions, params: params.merge(q: '2'), format: :json
          assert_success_response!
          res = JSON.parse(response.body)
          job = Job.find(res['async_job_id'])
          assert_success_job!(job)

          res = job.fetch_cache_data.rsk
          expect(res[:options]).to match_array([{ value: '2' }, { value: '25' }])
          expect(res[:options_count]).to eq 2
        end
      end

      context 'many records' do
        let(:query_model_sql) do
          <<~SQL
            with t(name, age) as (
              values
                #{(1..275).map { |i| "('#{i <= 250 ? 'banana' : 'ahihi'}#{i}', #{i})" }.join(",\n")}
            )
            select * from t
          SQL
        end

        it 'returns 200 records only' do
          post :submit_fetch_field_suggestions, params: params, format: :json
          assert_success_response!
          res = JSON.parse(response.body)
          job = Job.find(res['async_job_id'])
          assert_success_job!(job)

          res = job.fetch_cache_data.rsk
          expect(res[:options].size).to eq(200)
          expect(res[:options_count]).to eq 250
        end
      end
    end

    context 'with empty data' do
      let(:query_model_sql) do
        <<~SQL
          with t(name,age) as (
            values('alice', 1),
              ('bob', 2)
          )
          select * from t
          where 1 = 2
        SQL
      end

      it 'returns empty result' do
        post :submit_fetch_field_suggestions, params: params, format: :json
        assert_success_response!
        res = JSON.parse(response.body)
        job = Job.find(res['async_job_id'])
        assert_success_job!(job)

        res = job.fetch_cache_data.rsk
        expect(res[:options].size).to eq(0)
        expect(res[:options_count]).to eq(0)
      end

      context 'with search query' do
        before do
          params[:q] = 'b'
        end

        it 'returns empty result' do
          post :submit_fetch_field_suggestions, params: params, format: :json
          assert_success_response!
          res = JSON.parse(response.body)
          job = Job.find(res['async_job_id'])
          assert_success_job!(job)

          res = job.fetch_cache_data.rsk
          expect(res[:options].size).to eq(0)
          expect(res[:options_count]).to eq(0)
        end
      end
    end

    context 'with_viz_conditions' do
      # check 'simple_query_model' context
      let(:query_model_sql) do
        <<~SQL
          with t(name,age,birth) as (
            values('alice', 1, '2020-12-13'::timestamp),
              ('bob', 2, '2120-12-14'::timestamp)
          )
          select * from t
        SQL
      end

      it 'returns matching values' do
        params[:viz_conditions] = [
          {
            condition: { operator: 'is', values: ['HXT'] },
            path_hash: { field_name: 'name', model_id: query_data_model.id },
            label: 'Name',
          },
        ]

        post :submit_fetch_field_suggestions, params: params, format: :json
        assert_success_response!
        res = JSON.parse(response.body)
        job = Job.find(res['async_job_id'])
        assert_success_job!(job)

        res = job.fetch_cache_data.rsk
        expect(res[:options]).to match_array([])
        expect(res[:options_count]).to eq 0
      end

      # https://holistics.slack.com/archives/C03495C37NX/p1670816130063099
      it 'double coerce_from should not make modifier empty' do
        params[:viz_conditions] = [
          {
            condition: { operator: 'last', values: ['70'], modifier: 'year' },
            path_hash: { field_name: 'birth', model_id: query_data_model.id },
            label: 'Birthday',
          },
        ]

        post :submit_fetch_field_suggestions, params: params, format: :json
        assert_success_response!

        res = JSON.parse(response.body)
        job = Job.find(res['async_job_id'])
        assert_success_job!(job)

        res = job.fetch_cache_data.rsk
        expect(res[:options]).to match_array([{ value: 'alice' }])
      end
    end

    context 'embed_link users' do
      let(:dashboard) { FactoryBot.create :dashboard, version: 3 }
      let(:permission_rules) { {} }
      let(:embed_payload) do
        {
          permissions: permission_rules,
        }
      end
      let(:embed_link) do
        el = FactoryBot.create :embed_link, source: dashboard, filter_ownerships: [], tenant: admin.tenant, version: 3
        el.set_public_user
        el.share_source
        el
      end
      let(:embed_token) do
        sk = embed_link.secret_key
        jwt_encode(sk, embed_payload, Time.now.to_i + 1000)
      end
      let(:headers) do
        {
          PublicLinks::AuthenticationHelper::EMBED_ID_HEADER => embed_link.hash_code,
          PublicLinks::AuthenticationHelper::EMBED_TOKEN_HEADER => embed_token,
        }
      end

      before do
        ts = FactoryBot.create :tenant_subscription, status: 'active'
        ts.update_embed_workers! 5
        sign_out admin
        request.headers.merge!(headers)
      end

      shared_examples 'fetch_field_suggestion with embed_user' do
        it 'raises permission denied' do
          post :submit_fetch_field_suggestions, params: params, format: :json
          assert_response_status!(403)
        end

        context 'with dynamic_filter_id submitted' do
          include_context 'simple_query_model'
          let(:field_path) { DataModeling::Values::FieldPath.new(field_name: 'name', model_id: query_data_model.id) }
          let(:filter_source) { FactoryBot.create :dm_field_filter_source, data_set: data_set, field_path: field_path }
          let(:filter_definition) { FactoryBot.create :dynamic_filter_definition, filter_source: filter_source }
          let!(:dynamic_filter) do
            FactoryBot.create :dynamic_filter, definition: filter_definition, filter_holdable: dashboard, order: 2
          end
          let(:params) do
            {
              dynamic_filter_id: dynamic_filter.id,
            }
          end

          it 'returns the field suggestions for the dynamic filter' do
            post :submit_fetch_field_suggestions, params: params, format: :json
            assert_success_response!
            res = JSON.parse(response.body)
            job = Job.find(res['async_job_id'])
            assert_success_job!(job)

            res = job.fetch_cache_data.rsk
            expect(res[:options]).to match_array([{ value: 'alice' }, { value: 'bob' }])
            expect(res[:options_count]).to eq 2
          end

          context 'with permission rules' do
            let(:field_permission_rule) do
              {
                path: "#{data_set.uname}.#{query_data_model.name}.name",
                operator: 'is',
                values: ['alice'],
              }
            end
            let(:permission_rules) do
              {
                row_based: [field_permission_rule],
              }
            end

            it 'returns restricted records' do
              post :submit_fetch_field_suggestions, params: params, format: :json
              assert_success_response!
              res = JSON.parse(response.body)
              job = Job.find(res['async_job_id'])
              assert_success_job!(job)

              res = job.fetch_cache_data.rsk
              expect(res[:options]).to match_array([{ value: 'alice' }])
              expect(res[:options_count]).to eq 1
            end

            context 'same name models in different datasets' do
              let(:ds2) do
                ds = get_test_ds.dup
                ds.name += '2'
                ds.save!
                ds
              end
              let(:query_data_model2) do
                DataModels::CreateQueryModelService.async(tenant_id: admin.tenant_id, user_id: admin.id).execute(
                  admin.id, ds2.id, 'new_sql_model', query_model_sql, 0,
                )
                DataModel.last
              end
              let(:data_set2) do
                ds = FactoryBot.create :data_set, root_model_id: nil
                ds.data_models << query_data_model2
                ds.save!
                ds.reload
                ds
              end
              let(:field_path2) do
                DataModeling::Values::FieldPath.new(field_name: 'name', model_id: query_data_model2.id)
              end
              let(:filter_source2) do
                FactoryBot.create :dm_field_filter_source, data_set: data_set2, field_path: field_path2
              end
              let(:filter_definition2) { FactoryBot.create :dynamic_filter_definition, filter_source: filter_source2 }
              let!(:dynamic_filter2) do
                FactoryBot.create :dynamic_filter, definition: filter_definition2, filter_holdable: dashboard, order: 3
              end
              let(:field_permission_rule2) do
                {
                  path: "#{data_set2.uname}.#{query_data_model2.name}.name",
                  operator: 'is',
                  values: ['bob'],
                }
              end
              let(:permission_rules) do
                {
                  row_based: [field_permission_rule, field_permission_rule2],
                }
              end

              describe 'applying correct condition on correct model' do
                it 'applies correct condition for dynamic_filter' do
                  post :submit_fetch_field_suggestions, params: params, format: :json
                  assert_success_response!
                  res = JSON.parse(response.body)
                  job = Job.find(res['async_job_id'])
                  assert_success_job!(job)

                  res = job.fetch_cache_data.rsk
                  expect(res[:options]).to match_array([{ value: 'alice' }])
                  expect(res[:options_count]).to eq 1
                end

                it 'applies correct condition for dynamic_filter2' do
                  post :submit_fetch_field_suggestions, params: params.merge(dynamic_filter_id: dynamic_filter2.id),
                    format: :json
                  assert_success_response!
                  res = JSON.parse(response.body)
                  job = Job.find(res['async_job_id'])
                  assert_success_job!(job)

                  res = job.fetch_cache_data.rsk
                  expect(res[:options]).to match_array([{ value: 'bob' }])
                  expect(res[:options_count]).to eq 1
                end
              end
            end
          end
        end
      end

      context 'use v3_configs' do
        before do
          FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, false)
        end

        it_behaves_like 'fetch_field_suggestion with embed_user'
      end

      context 'use portal configs' do
        before do
          FeatureToggle.toggle_global(EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD, true)
        end

        it_behaves_like 'fetch_field_suggestion with embed_user'
      end
    end

    context 'with huge data model', skip_on_circleci: true do
      before do
        1000.times do |i|
          query_data_model.external_fields << DataModeling::Explores::Dimension.new(
            name: "custom_field_#{i}",
            label: "Custom #{i}",
            sql: '{{ #THIS.name }}',
            type: 'text',
            description: '',
            explore: query_data_model,
            model: query_data_model,
          )
        end
        query_data_model.save!
      end

      it 'is fast' do
        require 'stackprof'

        time = Benchmark.realtime do
          StackProf.run(raw: true, out: '/tmp/submit_fetch_field_suggestions.dump') do
            post :submit_fetch_field_suggestions, params: params.merge(bust_cache: true), format: :json
          end
          assert_success_response!
        end
        puts "Took #{time * 1000}ms".yellow
      end
    end

    context 'when in AML Studio', aml_env: ::AmlStudio::WorkingEnvironment::Env::AmlStudio do
      include_context 'aml_studio_dataset'

      let(:aml_model_name) { 'dm_products' }
      let(:aml_model_file_path) { 'models/dynamic_model/dm_products.model.aml' }
      let(:aml_dataset_name) { 'dynamic_modeling_dataset' }
      let(:aml_dataset_file_path) { 'datasets/dynamic_modeling.dataset.aml' }

      let(:params) do
        {
          project_id: project.id,
          data_set_id: nil,
          data_model_id: aml_model_name,
          field_name: 'param_text',
          path: nil,
          permission_rules: {},
        }
      end
      let(:expected_suggestion) { [{ 'value' => 'A' }, { 'value' => 'BC' }, { 'value' => 'DEF' }] }

      before do
        request.headers[HolisticsSetting::HEADER_AML_ENV] = AmlStudio::WorkingEnvironment::Env::AmlStudio.serialize
      end

      describe 'input validation' do
        it 'raises error when project id is invalid' do
          params[:project_id] = 1_234_654_321
          params[:path] = 'any'

          post :submit_fetch_field_suggestions, params: params, format: :json

          assert_response_status!(404)
          expect(response.body).to match(/Couldn't find AmlStudio::Project with 'id'=1234654321/i)
        end
      end

      describe 'suggestions for param field when previewing DataSet' do
        before do
          params[:data_set_id] = aml_dataset_name
        end

        it 'returns suggestions successfully' do
          post :submit_fetch_field_suggestions, params: params, format: :json
          assert_success_response!

          res = JSON.parse(response.body)

          expect(res['data']['options']).to match_array(expected_suggestion)
        end

        it 'raises error when dataset is nil' do
          params[:data_set_id] = 'invalid_dataset_name'

          post :submit_fetch_field_suggestions, params: params, format: :json

          assert_response_status!(422)
          expect(response.body).to match(/Dataset `invalid_dataset_name` not found/i)
        end

        it 'raises error when model is invalid' do
          params[:data_model_id] = 'invalid_model'

          post :submit_fetch_field_suggestions, params: params, format: :json

          assert_response_status!(422)
          expect(response.body).to match(/Data Model 'invalid_model' not found/i)
        end

        it 'raises error when the current user has no access to the data source' do
          sign_in get_test_analyst

          post :submit_fetch_field_suggestions, params: params, format: :json

          assert_response_status!(403)
          expect(response.body).to match(/You do not have permission/i)
        end
      end

      describe 'suggestions for param field when previewing Model' do
        it 'returns suggestions successfully' do
          post :submit_fetch_field_suggestions, params: params, format: :json
          assert_success_response!

          res = JSON.parse(response.body)

          expect(res['data']['options']).to match_array(expected_suggestion)
        end

        it 'raises error when model is invalid' do
          params[:data_model_id] = 'invalid_model'
          post :submit_fetch_field_suggestions, params: params, format: :json

          assert_response_status!(422)
          expect(response.body).to match(/Model `invalid_model` not found/i)
        end

        it 'raises error when the current user has no access to the data source' do
          sign_in get_test_analyst

          post :submit_fetch_field_suggestions, params: params, format: :json

          assert_response_status!(403)
          expect(response.body).to match(/You do not have permission/i)
        end
      end
    end
  end
end

describe VizDataController do
  context 'dashboards v4' do
    include_context 'dashboards_v4'

    let(:current_user) { admin }
    let(:explorable_filter_block) do
      DashboardsV4::Materialized::FilterBlock.find_by(id: "#{dashboard_v4.id}:filter_1")
    end
    let(:params) do
      {
        data_set_id: data_set.id,
        data_model_id: products_model.id,
        field_name: explorable_filter_block.filter_source.field_path.field_name,
      }
    end

    before do
      sign_in current_user
    end

    context 'shareable_link user' do
      let(:shareable_link) do
        sl = FactoryBot.create :shareable_link, resource: dashboard_v4, owner_id: admin.id
        sl.set_public_user
        sl.share_resource
        dashboard_v4.shareable_links << sl
        sl
      end
      let(:dynamic_filter_preset) do
        FactoryBot.create(
          :dynamic_filter_preset,
          dynamic_filter_presettable: shareable_link,
          owner_id: admin.id,
          dynamic_filter_id: "#{dashboard_v4.id}:#{explorable_filter_block.uname}",
          preset_condition: { operator: 'is', values: ['hehehe'], modifier: nil },
        )
      end

      it 'can fetch suggestions' do
        post :submit_fetch_field_suggestions, params: params, format: :json
        job = assert_success_async_response!(['async_job_id'])

        res = job.fetch_cache_data.rsk
        expect(res[:options]).to match_array([{ value: 'bread' }, { value: 'egg' }, { value: 'milk' }])
        expect(res[:options_count]).to eq 3
      end
    end

    context 'analyst' do
      let(:current_user) { get_test_analyst }

      it 'raises permission denied' do
        post :submit_fetch_field_suggestions, params: params, format: :json
        assert_response_status!(403)
      end

      context 'with dynamic_filter_id' do
        let(:params) do
          {
            dynamic_filter_id: explorable_filter_block.id,
          }
        end

        it 'raises permission denied' do
          post :submit_fetch_field_suggestions, params: params, format: :json
          assert_response_status!(403)
        end

        context 'data source is shared' do
          before do
            admin.share(current_user, :read, data_set.data_source)
          end

          it 'can fetch suggestions' do
            post :submit_fetch_field_suggestions, params: params, format: :json
            job = assert_success_async_response!(['async_job_id'])

            res = job.fetch_cache_data.rsk
            expect(res[:options]).to match_array([{ value: 'bread' }, { value: 'egg' }, { value: 'milk' }])
            expect(res[:options_count]).to eq 3
          end
        end

        context 'dashboard is explicitly shared' do
          before do
            admin.share(current_user, :read, dashboard_v4)
          end

          it 'raises permission denied' do
            # same as VizBlock permission
            post :submit_fetch_field_suggestions, params: params, format: :json
            assert_response_status!(403)
          end
        end
      end
    end
  end
end
