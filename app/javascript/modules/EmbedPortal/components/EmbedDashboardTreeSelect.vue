<script setup lang="ts">
import { computed } from 'vue';
import { HSelect, type SelectOption } from '@holistics/design-system';
import { useVModel } from '@vueuse/core';
import { useEmbedPortalStore } from '@/modules/EmbedPortal/store/embedPortalStore';
import type { EmbedDashboard } from '@/modules/EmbedPortal/types';

const props = defineProps<{
  modelValue?: EmbedDashboard;
}>();

const modelValue = useVModel(props);
const embedPortalStore = useEmbedPortalStore();

function selectDashboard (dashboard: EmbedDashboard) {
  modelValue.value = dashboard;
}

const buildWorkspaceOptions = (dashboards: EmbedDashboard[]) => {
  if (dashboards.length === 0) {
    return [{
      value: null,
      label: 'No dashboard',
      disabled: true,
    } as SelectOption];
  }

  return dashboards.map(dashboard => ({
    value: dashboard.id,
    label: dashboard.title,
    icon: 'canvas',
    action: () => selectDashboard(dashboard),
  } as SelectOption));
};

const options = computed(() => {
  if (!embedPortalStore.embedPortal) return [];

  const { actionBasedPermission, embedObjects } = embedPortalStore.embedPortal;

  const result: SelectOption[] = [];

  if (actionBasedPermission.orgWorkspace.canView) {
    const orgDashboards = buildWorkspaceOptions(embedObjects.orgDashboards || []);

    result.push({
      label: 'Shared workspace',
      value: 'org-workspace',
      children: orgDashboards,
      initialExpanded: true,
    });
  }

  if (actionBasedPermission.personalWorkspace.canView) {
    const personalDashboards = buildWorkspaceOptions(embedObjects.personalDashboards || []);

    result.push({
      label: 'Personal workspace',
      value: 'personal-workspace',
      children: personalDashboards,
      initialExpanded: true,
    });
  }

  return result;
});
</script>

<template>
  <HSelect
    data-ci="embed-portal-dashboard-tree-select"
    :model-value="modelValue?.id"
    :options="options"
    filterable
    placeholder="Select dashboard..."
  />
</template>
