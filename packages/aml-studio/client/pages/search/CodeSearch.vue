<template>
  <div class="flex h-full flex-col">
    <!-- Search and Replace Section -->
    <div class="flex-none p-2 pt-3">
      <span class="item mb-1 block font-medium">Code Search</span>
      <div class="flex flex-col gap-2">
        <!-- Search section -->
        <SearchHeader
          ref="searchHeaderRef"
          :search-text="searchOptions.searchText"
          :show-replace="showReplace"
          :all-options="allOptions"
          :search-options="{
            matchCase: searchOptions.matchCase,
            matchWholeWord: searchOptions.matchWholeWord
          }"
          @update:search-text="handleUpdateQuery"
          @update:search-options="handleSearchOptionsUpdate"
          @select-option="handleSelectDropdownOption"
          @clear-filter="clearFilter"
          @toggle-replace="toggleReplace"
          @clear-search="clearSearch"
          @key-down="handleKeyDown"
        />

        <!-- Replace section -->
        <ReplaceSection
          v-model="replaceValue"
          :show="showReplace"
          :has-matches="!!searchResults.length"
          :need-refresh-implicit-repo="needRefreshImplicitRepo"
          @replace="performReplaceAll"
        />
      </div>
    </div>

    <div
      v-h-loading="isReplacing"
      class="flex-1 overflow-hidden"
    >
      <!-- Search Results Section -->
      <SearchResults
        :results="searchResults"
        :display-nodes="displayNodes"
        :selected-item-id="selectedItemId"
        :item-height="textItemHeight"
        :search-texts="searchQueries"
        :show-replace="showReplace"
        :replace-value="replaceValue"
        :is-searching="isSearching"
        :total-matches="totalMatches"
        :need-refresh-implicit-repo="needRefreshImplicitRepo"
        @toggle-file="toggleFile"
        @open-file="clickOpenFile"
        @open-match="clickOpenFileAtMatch"
        @replace-in-file="performReplaceInFile"
        @dismiss-result="removeSearchResult"
        @dismiss-file="removeFileSearchResult"
        @refresh-project="refreshProject"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref, watch, computed, nextTick,
  onMounted, onUnmounted, Ref, toRefs,
} from 'vue';
import { useStore } from 'vuex';
import {
  debounce, concat, snakeCase, toArray,
} from 'lodash';
import { DropdownOption } from '@holistics/design-system';
import { onFileChangeHook, onSearchRefreshHook, onSearchFileOpenHook } from '@aml-studio/client/store/eventHooks';
import { useExploreSearchMode } from '@aml-studio/client/pages/composables/useExploreSearchMode';
import { useImplicitGitCheck } from '@aml-studio/client/pages/composables/useImplicitGitCheck';
import SearchHeader from '@aml-studio/client/pages/search/components/SearchHeader/SearchHeader.vue';
import ReplaceSection from '@aml-studio/client/pages/search/components/ReplaceSection/ReplaceSection.vue';
import SearchResults from '@aml-studio/client/pages/search/components/SearchResults/SearchResults.vue';
import {
  EventSchema, trackUnstructEvent, SnowplowEvent,
} from '@aml-studio/h/services';
import type {
  Node, SearchResult, Option, TextNode,
  SearchOperator,
} from '@aml-studio/client/pages/search/types';
import {
  NodeType, OptionType,
} from '@aml-studio/client/pages/search/types';
import {
  FileType,
  PropertyType,
  FilterType,
} from '@aml-studio/client/pages/search/constants';
import {
  resultToAmlNodes,
  expand, collapse,
  searchTextMatchesWithFiltersInAmlFiles,
  fillMissingKeyword,
  parseQuery, serializeFilter, buildSearchOperatorsFromFilters,
  buildFilterOption, buildHistoryOption, buildHeaderOption, buildKeywordString,
  getOptionType, extractKeywordStringFromDropdownOption,
} from '@aml-studio/client/pages/search/services';
import { useProvideSearchState, useSearchState } from './composables/useSearchState';
import { useReplace } from './composables/useReplace';
import { useFileOperations } from './composables/useFileOperations';

const props = defineProps<{
  onFileOpen?:(filePath: string) => Promise<void>
}>();

const { isSearchMode } = useExploreSearchMode();

const textItemRef = ref<HTMLDivElement | null>(null);
const textItemHeight = computed(() => textItemRef.value?.clientHeight || 24);

const store = useStore();

// Provide search state at the top level
useProvideSearchState();

const {
  searchState, updateSearchData, setIsSearching, setSearchResults,
  updateReplaceText, updateFilters,
} = useSearchState();

// Individual search texts
const searchQueries = ref<string[]>([]);

const { hasUpdatedImplicitRepo } = useImplicitGitCheck();
const {
  showReplace,
  replaceValue,
  isReplacing,
  toggleReplace,
  handleReplaceAll,
  handleReplaceInFile,
} = useReplace();

const { searchResults, isSearching } = toRefs(searchState);
const currentFilters = computed(() => searchState.filters);

const totalMatches = computed(() => searchResults.value.reduce((sum, result) => sum + result.matches.length, 0));
const amlFlatNodes = computed(() => resultToAmlNodes(searchResults.value));
const displayNodes = ref<Node[]>([]);

const searchText = computed(() => searchState.searchData.searchText);
const searchOptions = computed(() => searchState.searchData);

const selectedItemId = ref<string | null>(null);

const needRefreshImplicitRepo = ref(false);

const performSearch = debounce(async (text: string) => {
  if (!text) {
    setSearchResults([]);
    setIsSearching(false);
    return;
  }

  try {
    const filters = parseQuery(text);
    updateFilters(filters);

    const options = {
      matchCase: searchOptions.value.matchCase,
      matchWholeWord: searchOptions.value.matchWholeWord,
    };

    const operators: SearchOperator[] = buildSearchOperatorsFromFilters(filters, options);
    searchQueries.value = operators.map(o => o.searchText);

    const results: SearchResult[] = await searchTextMatchesWithFiltersInAmlFiles(store.state)({
      operators,
      options: {
        matchCase: searchOptions.value.matchCase,
        matchWholeWord: searchOptions.value.matchWholeWord,
      },
    });

    setSearchResults(toArray(results));

    // Event tracking

    // Create an array of search options that are selected
    // e.g. ['match_case', 'match_whole_word'] or [] if nothing is selected
    const selectedSearchOptions = ['matchCase', 'matchWholeWord']
      .filter(option => searchOptions.value[option as keyof typeof searchOptions.value])
      .map(option => snakeCase(option));

    // ToDo: update this when we introduce multiple filters
    const selectedFilters = currentFilters.value.map(filter => `[${serializeFilter(filter)}]`).join(',');

    trackUnstructEvent({
      schema: EventSchema.AmlStudioCodeSearch,
      data: {
        search_text: text,
        search_options: selectedSearchOptions,
        filters: selectedFilters,
      },
    } as SnowplowEvent);
  } catch (error) {
    setSearchResults([]);
  } finally {
    setIsSearching(false);
  }
}, 300);

const handleSelectOption = (option: Option) => {
  if (option.type === OptionType.Filter) {
    const newSearchText = fillMissingKeyword({
      rawText: searchText.value,
      keyword: buildKeywordString(option),
      addSpaceAtEnd: true,
    });

    updateSearchData({
      ...searchOptions.value,
      searchText: newSearchText,
    });
    setIsSearching(true);
    performSearch(newSearchText);
  } else if (option.type === OptionType.History) {
    updateSearchData({
      ...searchOptions.value,
      searchText: option.value,
    });
    setIsSearching(true);
    performSearch(option.value);
  }
};

const filterOptions: Option[] = [
  buildFilterOption({
    key: FilterType.Type,
    value: FileType.Model,
    onClickFn: handleSelectOption,
  }),
  buildFilterOption({
    key: FilterType.Type,
    value: FileType.Dataset,
    onClickFn: handleSelectOption,
  }),
  buildFilterOption({
    key: FilterType.Type,
    value: FileType.Dashboard,
    onClickFn: handleSelectOption,
  }),
  buildFilterOption({
    key: FilterType.Type,
    value: PropertyType.Dimension,
    onClickFn: handleSelectOption,
  }),
  buildFilterOption({
    key: FilterType.Type,
    value: PropertyType.Measure,
    onClickFn: handleSelectOption,
  }),
  buildFilterOption({
    key: FilterType.Type,
    value: PropertyType.Metric,
    onClickFn: handleSelectOption,
  }),
  buildFilterOption({
    key: FilterType.Property,
    value: PropertyType.DataSourceName,
    onClickFn: handleSelectOption,
  }),
  buildFilterOption({
    key: FilterType.Property,
    value: PropertyType.Owner,
    onClickFn: handleSelectOption,
  }),
];

const headerOption: Option = buildHeaderOption({
  key: 'search-options',
  value: 'Search Options',
});

const headerHistory: Option = buildHeaderOption({
  key: 'search-history',
  value: 'Recent Searches',
});

const historyOptions: Ref<Option[]> = computed(
  () => searchState
    .searchTextHistory
    .map(text => buildHistoryOption({
      key: text,
      value: text,
    })),
);

const allOptions: Ref<Option[]> = computed(() => concat(
  headerOption,
  filterOptions,
  headerHistory,
  historyOptions.value,
));

// Handle search data update and carry out search

const clearFilter = () => {
  updateFilters([]);
};

const clearSearch = () => {
  updateSearchData({
    ...searchOptions.value,
    searchText: '',
  });
  setSearchResults([]);
  setIsSearching(false);
};

const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Backspace' && !searchText.value && currentFilters.value.length > 0) {
    clearFilter();
    clearSearch();
  }
};

const {
  getFileName,
  openFile,
  openFileAtMatch,
} = useFileOperations();

const refreshSearch = async () => {
  setIsSearching(true);
  await performSearch(searchText.value);
};

const refreshProject = async () => {
  await store.dispatch('aml/files/refreshFiles');
  await refreshSearch();
  needRefreshImplicitRepo.value = false;
};

const performReplaceAll = async () => {
  const affectedPaths = searchResults.value.map(result => result.filePath);

  // Check implicit git status and get user confirmation if needed
  needRefreshImplicitRepo.value = await hasUpdatedImplicitRepo(affectedPaths);

  // If a refresh is needed, do nothing
  if (needRefreshImplicitRepo.value) {
    return;
  }

  // Event tracking
  trackUnstructEvent({
    schema: EventSchema.AmlStudioCodeReplace,
    data: {
      search_text: searchText.value,
      replace_text: replaceValue.value,
      replace_type: 'replace_all',
    },
  } as SnowplowEvent);

  // Proceed with replace using latest search results
  if (searchResults.value.length > 0) {
    await handleReplaceAll(searchResults.value);
  }
};

const performReplaceInFile = async (item: Node) => {
  // Check implicit git status and get user confirmation if needed
  needRefreshImplicitRepo.value = await hasUpdatedImplicitRepo([item.path]);

  // If a refresh is needed, do nothing
  if (needRefreshImplicitRepo.value) {
    return;
  }

  // Proceed with replace using latest search results for the specific file
  const fileResults = searchResults.value.filter(result => result.filePath === item.path);

  // Event tracking
  trackUnstructEvent({
    schema: EventSchema.AmlStudioCodeReplace,
    data: {
      search_text: searchText.value,
      replace_text: replaceValue.value,
      replace_type: 'replace_in_file',
    },
  } as SnowplowEvent);

  // Perform replace
  if (fileResults.length > 0) {
    await handleReplaceInFile(item, fileResults);
  }
};

const handleUpdateQuery = (query: string) => {
  updateSearchData({
    ...searchOptions.value,
    searchText: query,
  });

  setIsSearching(true);
  performSearch(query);
};

const handleSelectDropdownOption = (option: DropdownOption | undefined) => {
  if (!option) {
    return;
  }

  switch (getOptionType(option)) {
    case OptionType.Header:
      return;
    case OptionType.Filter:
      updateSearchData({
        ...searchOptions.value,
        // @ts-ignore
        searchText: fillMissingKeyword({
          rawText: searchText.value,
          keyword: extractKeywordStringFromDropdownOption(option),
          addSpaceAtEnd: true,
        }),
      });
      break;
    case OptionType.History:
      updateSearchData({
        ...searchOptions.value,
        // @ts-ignore
        searchText: option.value,
      });
      break;
    default:
  }

  setIsSearching(true);
  performSearch(searchOptions.value.searchText);
};

const handleSearchOptionsUpdate = (options: { matchCase: boolean; matchWholeWord: boolean }) => {
  updateSearchData({
    ...searchOptions.value,
    matchCase: options.matchCase,
    matchWholeWord: options.matchWholeWord,
  });

  setIsSearching(true);
  performSearch(searchText.value);
};

function toggleFile (nodePath: string, isExpanded: boolean) {
  displayNodes.value = isExpanded
    ? collapse({
      currentDisplayNodes: displayNodes.value,
      collapsibleNodePath: nodePath,
    })
    : expand({
      originalNodes: amlFlatNodes.value,
      currentDisplayNodes: displayNodes.value,
      expandableNodePath: nodePath,
    });
}

// Remove a single search result and clean up orphaned file nodes
const removeSearchResult = (item: TextNode) => {
  // Update the search results
  const updatedResults = searchResults.value.map(result => {
    if (result.filePath === item.path) {
      // Filter out the removed match from this file's matches based on lineNumber, start, and end
      const updatedMatches = result.matches.filter(match => !(match.lineNumber === item.lineNumber
          && match.start === item.start
          && match.end === item.end));

      return {
        ...result,
        matches: updatedMatches,
        hasMatch: updatedMatches.length > 0,
      };
    }
    return result;
  }).filter(result => result.hasMatch);

  setSearchResults(updatedResults);
};

// Remove a specific file search result
const removeFileSearchResult = (path: string) => {
  const updatedResults = searchResults.value.filter(result => result.filePath !== path);
  setSearchResults(updatedResults);
};

// Handler for file content changes
const handleFileContentChanged = () => {
  if (searchText.value) {
    setIsSearching(true);
    performSearch(searchText.value);
  }
};

// To focus on search input when search mode is activated
const searchHeaderRef = ref<InstanceType<typeof SearchHeader> | null>(null);

const focusSearchInput = async () => {
  await nextTick();
  searchHeaderRef.value?.focusSearchInput();
};

watch(amlFlatNodes, (newNodes) => {
  displayNodes.value = newNodes;
}, { immediate: true });

// Watch replace value changes
watch(replaceValue, (newValue) => {
  updateReplaceText(newValue);
});

const handleFileOpen = ({ filePath }: { filePath: string }) => {
  const fileNode: Node = {
    id: filePath,
    path: filePath,
    level: 0,
    isExpanded: false,
    type: NodeType.File,
    label: getFileName(filePath),
    children: [],
  };
  openFile(fileNode);
};

const clickOpenFile = async (item: Node) => {
  selectedItemId.value = item.id;
  openFile(item);

  // Handle router push on file open
  if (props.onFileOpen) {
    await props.onFileOpen(item.path);
  }
};

const clickOpenFileAtMatch = async (item: TextNode) => {
  selectedItemId.value = item.id;
  await openFileAtMatch(item);

  // Handle router push on file open at match
  if (props.onFileOpen) {
    await props.onFileOpen(item.path);
  }
};

// Revalidate search when component is mounted if there's a persisted search
onMounted(async () => {
  if (searchState.searchData.searchText) {
    setIsSearching(true);
    await performSearch(searchState.searchData.searchText);
  }

  if (isSearchMode.value) {
    focusSearchInput();
  }

  onFileChangeHook.on(handleFileContentChanged);
  onSearchRefreshHook.on(refreshSearch);
  onSearchFileOpenHook.on(handleFileOpen);
});

// When this component is unmounted, remove the event listener
onUnmounted(() => {
  onFileChangeHook.off(handleFileContentChanged);
  onSearchRefreshHook.off(refreshSearch);
  onSearchFileOpenHook.off(handleFileOpen);
});
</script>
