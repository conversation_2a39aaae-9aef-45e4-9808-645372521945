# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe Api::V2::AmlStudio::ProjectsController, :api, aml_env: AmlStudio::WorkingEnvironment::Env::AmlStudio,
                                                       type: :controller do
  include_context 'aml_studio_dashboard'

  let(:user) { get_test_admin }
  let(:tenant) { get_test_tenant }

  before do
    request.headers['Accept'] = 'application/json'
    request.headers['Content-Type'] = 'application/json'

    set_request_aml_env(:aml_studio)
    user.update(allow_authentication_token: true)
    set_token_header(user)
    ThreadContext.set(:current_user, user)

    deploy_flow.call
  end

  def response_result
    JSON.parse(response.body)
  end

  describe '#dbt cloud credentials' do
    context 'get' do
      it 'can get credentials' do
        project.update!(dbt_token: 'abcdefg', dbt_account_id: 1, dbt_cloud_url: 'a.com')
        get :dbt_cloud_credentials, params: { project_id: project.id }
        assert_success_response!

        expect(response_result).to eq({ 'token' => 'abc***', 'account_id' => 1, 'dbt_cloud_url' => 'a.com' })
      end

      it 'raise not found error' do
        get :dbt_cloud_credentials, params: { project_id: 99 }

        expect(response.status).to eq(404)
      end

      describe 'permission issue' do
        let(:project2) do
          AmlStudio::Project.create!(name: 'Test2', tenant_id: get_test_tenant2.id)
        end

        it 'raise error' do
          get :dbt_cloud_credentials, params: { project_id: project2.id }

          expect(response.status).to eq(403)
        end
      end
    end

    context 'remove' do
      it 'can remove credentials' do
        project.update!(dbt_token: 'a', dbt_account_id: 1)

        delete :remove_dbt_cloud_credentials, params: { project_id: project.id }
        assert_success_response!

        tenant.reload
        project.reload
        expect(project.dbt_token).to eq(nil)
        expect(project.dbt_account_id).to eq(nil)
        expect(tenant.dbt_token).to eq(nil)
        expect(tenant.dbt_account_id).to eq(nil)
      end

      it 'raise not found error' do
        delete :remove_dbt_cloud_credentials, params: { project_id: 99 }

        expect(response.status).to eq(404)
      end

      describe 'permission issue' do
        let(:project2) do
          AmlStudio::Project.create!(name: 'Test2', tenant_id: get_test_tenant2.id)
        end

        it 'raise error' do
          delete :remove_dbt_cloud_credentials, params: { project_id: project2.id }

          expect(response.status).to eq(403)
        end
      end
    end

    context 'update' do
      it 'can update credentials' do
        allow_any_instance_of(Dbt::ValidateCredentials).to receive(:call).and_return({ status: 'OK' })
        post :update_dbt_cloud_credentials, params: { project_id: project.id, token: 'a', account_id: 1 }
        assert_success_response!

        tenant.reload
        project.reload
        expect(project.dbt_token).to eq('a')
        expect(project.dbt_account_id).to eq(1)
        expect(tenant.dbt_token).to eq('a')
        expect(tenant.dbt_account_id).to eq(1)
      end

      it 'cannot update credentials due to wrong credentials' do
        post :update_dbt_cloud_credentials, params: { project_id: project.id, token: 'a', account_id: 1 }

        expect(response.status).to eq(400)

        tenant.reload
        project.reload
        expect(project.dbt_token).to eq(nil)
        expect(project.dbt_account_id).to eq(nil)
        expect(tenant.dbt_token).to eq(nil)
        expect(tenant.dbt_account_id).to eq(nil)
      end

      it 'raise not found error' do
        post :update_dbt_cloud_credentials, params: { project_id: 99, token: 'a', account_id: 1 }

        expect(response.status).to eq(404)
      end

      describe 'permission issue' do
        let(:project2) do
          AmlStudio::Project.create!(name: 'Test2', tenant_id: get_test_tenant2.id)
        end

        it 'raise error' do
          post :update_dbt_cloud_credentials, params: { project_id: project2.id, token: 'a', account_id: 1 }

          expect(response.status).to eq(403)
        end
      end

      describe 'invalid param' do
        let(:params) { { project_id: project.id, token: 'a', account_id: 2 } }

        before do
          tenant.dbt_token = 'a'
          tenant.dbt_account_id = 1
          tenant.save!
          project.update!(dbt_token: 'a', dbt_account_id: 1)
        end

        shared_examples 'invalid operation error' do
          it 'raise error' do
            post :update_dbt_cloud_credentials, params: params

            expect(response.status).to eq(400)
            project.reload
            tenant.reload
            expect(project.dbt_token).to eq('a')
            expect(project.dbt_account_id).to eq(1)
            expect(tenant.dbt_token).to eq('a')
            expect(tenant.dbt_account_id).to eq(1)
          end
        end

        context 'token is empty' do
          it_behaves_like 'invalid operation error' do
            let(:params) { { project_id: project.id, token: '', account_id: 2 } }
          end
        end

        context 'token param is missing' do
          it_behaves_like 'invalid operation error' do
            let(:params) { { project_id: project.id, account_id: 2 } }
          end
        end

        context 'account_id param is missing' do
          it_behaves_like 'invalid operation error' do
            let(:params) { { project_id: project.id, token: 'as' } }
          end
        end
      end
    end
  end

  describe '#object_reporting_url' do
    it 'returns dashboard reporting url' do
      get :object_reporting_url, params: { project_id: project.id, uname: 'test_dashboard', object_type: 'Dashboard' }

      dashboard = Dashboard.find_by(uname: 'test_dashboard', project_id: project.id)

      assert_success_response!
      url = response_result['url']
      expect(url).to eq("/dashboards/#{dashboard.id}-dashboard-4-0")
    end

    context 'dataset' do
      include_context 'aml_studio_dataset'
      include_context 'data_set'

      let(:aml_dataset) do
        deploy_result # make sure deployment is already done
        DataSet.find_by!(uname: 'test')
      end

      it 'returns dataset reporting url' do
        get :object_reporting_url, params: { project_id: project.id, uname: 'test', object_type: 'Dataset' }
        assert_success_response!
        url = response_result['url']
        expect(url).to eq("/datasets/#{aml_dataset.id}-test-data-set")
      end
    end

    it 'returns error not found' do
      get :object_reporting_url, params: { project_id: project.id, uname: 'error', object_type: 'Dashboard' }
      expect(response.status).to eq(404)
      expect(response_result['message']).to eq("Cannot find Dashboard with 'project_id'=#{project.id}, 'uname'=error")
    end

    it 'returns error when invalid type', :skip_schema_conform do
      get :object_reporting_url, params: { project_id: project.id, uname: 'error', object_type: 'DataModel' }
      expect(response.status).to eq(400)
      expect(response_result['message']).to eq("Invalid object type 'DataModel'")
    end

    context 'analyst' do
      let(:user) { users(:analyst) }

      it 'returns error permission denied' do
        get :object_reporting_url, params: { project_id: project.id, uname: 'dataset_products', object_type: 'Dataset' }
        expect(response.status).to eq(403)
      end
    end
  end

  context 'enables explicit git' do
    it 'enables explicit git ft' do
      post :enable_explicit_git, params: { project_id: project.id }

      assert_success_response!
      ft = FeatureToggle.find_by(key: AmlStudio::Project::FT_EXPLICIT_GIT)
      expect(ft.active_for(user.tenant_id)).to be true
    end

    it 'return 422' do
      allow(FeatureToggle).to receive(:toggle_tenant).and_return(false)

      post :enable_explicit_git, params: { project_id: project.id }

      expect(response.status).to eq(422)
    end

    context 'violate permission' do
      it 'returns error permission denied' do
        user = get_test_user

        request.headers['Accept'] = 'application/json'
        request.headers['Content-Type'] = 'application/json'

        set_request_aml_env(:aml_studio)
        user.update(allow_authentication_token: true)
        set_token_header(user)

        post :enable_explicit_git, params: { project_id: project.id }

        expect(response.status).to eq(403)
      end
    end
  end

  describe '#submit_publish' do
    include_context 'aml_studio_remote_deploy'

    it 'deploy latest commit' do
      commit_history = prepare
      SourceControl::Client.any_instance.stub(:branch_target_oid).and_return(commit_history.last)

      post :submit_publish
      assert_success_async_response!(['job', 'id'])
      job = assert_async_response!(['job', 'id'])
      result = job.fetch_cache_data
      expect(result['data']).to have_key 'preparing_production_job'
      expect(result['target_commit']).to eq(commit_history.last)
      expect(DataSet.find_by(uname: 'test_ds')).to be_present
      expect(DataSet.find_by(uname: 'test_ds_2')).to be_present
    end

    xit 'deploy with commit' do
      FeatureToggle.toggle_global(AmlStudio::Project::FT_DELETE_ORPHANED_PRODUCTION_DATASET, true)
      commit_history = prepare
      SourceControl::Client.any_instance.stub(:branch_target_oid).and_return(commit_history.last)

      post :submit_publish, params: { commit_oid: commit_history.last }
      assert_success_async_response!(['job', 'id'])
      expect(DataSet.find_by(uname: 'test_ds')).to be_present
      expect(DataSet.find_by(uname: 'test_ds_2')).to be_present

      post :submit_publish, params: { commit_oid: commit_history.first }
      assert_success_async_response!(['job', 'id'])
      expect(DataSet.find_by(uname: 'test_ds')).to be_present
      expect(DataSet.find_by(uname: 'test_ds_2')).not_to be_present
    end

    describe 'with object mapping' do
      it 'raise dataset mapping error' do
        FeatureToggle.toggle_global(AmlStudio::Project::FT_DELETE_ORPHANED_PRODUCTION_DATASET, false)

        commit_history = prepare
        SourceControl::Client.any_instance.stub(:branch_target_oid).and_return(commit_history.last)

        post :submit_publish
        assert_success_async_response!(['job', 'id'])

        commit = implicit_commit.call('test', { 'test.dataset.aml' => '', 'test3.dataset.aml' => test_dataset3 })
        commit_history.push(commit)
        SourceControl::Client.any_instance.stub(:branch_target_oid).and_return(commit_history.last)

        post :submit_publish
        assert_success_async_response!(['job', 'id'])
        job = assert_async_response!(['job', 'id'])
        result = job.fetch_cache_data

        expect(result['status']).to eq 'error'
        expect(result['error_type']).to eq 'dataset_mapping'
        expect(result['target_commit']).to eq commit_history.last
      end

      it 'work' do
        FeatureToggle.toggle_global(AmlStudio::Project::FT_DELETE_ORPHANED_PRODUCTION_DATASET, false)

        commit_history = prepare
        SourceControl::Client.any_instance.stub(:branch_target_oid).and_return(commit_history.last)

        post :submit_publish
        assert_success_async_response!(['job', 'id'])

        commit = implicit_commit.call('test', { 'test.dataset.aml' => '', 'test3.dataset.aml' => test_dataset3 })
        commit_history.push(commit)

        SourceControl::Client.any_instance.stub(:branch_target_oid).and_return(commit_history.last)
        test_dataset = DataSet.find_by(uname: 'test_ds')
        object_mapping = { datasets: { test_dataset.id => 'test_ds_3' } }
        post :submit_publish, params: { object_mapping: object_mapping }
        assert_success_async_response!(['job', 'id'])

        job = assert_async_response!(['job', 'id'])
        result = job.fetch_cache_data

        expect(result['status']).to eq 'success'
        expect(DataSet.find_by(uname: 'test_ds_3')).to be_present
      end
    end

    xit 'invalid commit' do
      prepare
      SourceControl::Client.any_instance.stub(:contains).and_return(false)
      post :submit_publish, params: { commit_oid: 'abc' }
      job = assert_async_response!(['job', 'id'])
      result = job.fetch_cache_data
      expect(result['status']).to eq 'error'
      expect(result['error_type']).to eq 'invalid_commit'
      expect(result['error_details']).to eq 'commit_oid abc is not available on production branch master'
    end

    it '403' do
      FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
      set_token_header(get_test_explorer)
      post :submit_publish

      assert_response_status!(403)
    end

    it '404' do
      allow(AmlStudio::Project).to receive(:find_by!) do
        raise Holistics::NotFound, 'Cannot find project'
      end
      post :submit_publish

      assert_response_status!(404)
    end

    it '422' do
      FeatureToggle.toggle_global('aml_studio:enable', false)
      post :submit_publish
      assert_response_status!(422)
    end
  end

  describe '#submit_validate' do
    include_context 'aml_studio_remote_deploy'

    it 'work' do
      commit_history = prepare
      post :submit_validate, params: { branch_name: 'master', commit_oid: commit_history.last }
      assert_success_async_response!(['job', 'id'])
    end

    it '404' do
      allow(AmlStudio::Project).to receive(:find_by!) do
        raise Holistics::NotFound, 'Cannot find project'
      end

      commit_history = prepare
      post :submit_validate, params: { branch_name: 'master', commit_oid: commit_history.last }
      assert_response_status!(404)
    end

    it '422' do
      FeatureToggle.toggle_global('aml_studio:enable', false)
      commit_history = prepare
      post :submit_validate, params: { branch_name: 'master', commit_oid: commit_history.last }
      assert_response_status!(422)
    end

    it '403' do
      FeatureToggle.toggle_global(User::FT_EXPLORER_USER, true)
      set_token_header(get_test_explorer)
      commit_history = prepare
      post :submit_validate, params: { branch_name: 'master', commit_oid: commit_history.last }

      assert_response_status!(403)
    end
  end

  describe '#update_settings' do
    context 'invalid permission' do
      let(:user) { get_test_analyst }

      it 'raise permission denied for analyst' do
        put :update_settings, params: { id: project.id, settings: { edit_in_production_enabled: true } }
        expect(response.status).to eq(403)
      end
    end

    it 'only update allowed props' do
      put :update_settings, params: {
        id: project.id,
        settings: {
          edit_in_production_enabled: true,
          provider: 'GitHub',
          is_external_integrated: true,
          enabled_looker_migrator: true,
          enabled_pr_workflow: true,
        },
      }

      assert_success_response!
      project.reload
      expect(project.settings.to_h.compact).to eq({ edit_in_production_enabled: true, is_external_integrated: false,
                                                    enabled_looker_migrator: true, enabled_pr_workflow: false, })
    end
  end

  describe '#pr workflow settings' do
    context 'can not perform pr workflow settings' do
      let(:user) { get_test_analyst }

      it 'raise permission denied when enable pr workflow' do
        post :enable_pr_workflow, params: {
          id: project.id,
          token: 'abc',
        }
        expect(response).to have_http_status(:forbidden)
      end

      it 'raise permission denied when disable pr workflow' do
        post :disable_pr_workflow, params: {
          id: project.id,
        }
        expect(response).to have_http_status(:forbidden)
      end

      it 'raise permission denied when update token pr workflow' do
        post :update_token_pr_workflow, params: {
          id: project.id,
          token: 'abc',
        }
        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'pr workflow feature toggle is disabled' do
      let(:user) { get_test_admin }

      before do
        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, false)
      end

      after do
        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, true)
      end

      it 'raise 422 when enable pr workflow' do
        post :enable_pr_workflow, params: {
          id: project.id,
          token: 'abc',
        }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response_result).to eq({
                                        'type' => 'InvalidOperationError',
                                        'message' => 'PR Workflow Feature is not enabled for this tenant',
                                      })
      end

      it 'raise 422 when disable pr workflow' do
        post :disable_pr_workflow, params: {
          id: project.id,
        }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response_result).to eq({
                                        'type' => 'InvalidOperationError',
                                        'message' => 'PR Workflow Feature is not enabled for this tenant',
                                      })
      end

      it 'raise 422 when update token pr workflow' do
        post :update_token_pr_workflow, params: {
          id: project.id,
          token: 'abc',
        }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response_result).to eq({
                                        'type' => 'InvalidOperationError',
                                        'message' => 'PR Workflow Feature is not enabled for this tenant',
                                      })
      end
    end

    context 'enable pr workflow settings' do
      let(:user) { get_test_admin }

      before do
        # timezone here because if we run in different timezones, the test will fail
        Time.zone = 'UTC'

        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, true)
        FeatureToggle.toggle_global(AmlStudio::Project::FT_EXTERNAL_GIT, true)

        project.settings = project.settings.merge(
          is_external_integrated: true,
          provider: 'GitHub',
        )
        project.remote_url_origin = '**************:test/test.git'
        project.save!
      end

      after do
        Time.zone = nil
      end

      it 'enables pr workflow settings, update token and disable without deleting the token' do
        AmlStudio::GitFlows::GitClient::GithubClient.any_instance
                                                    .stub(:validate_read_pr_permission!)
                                                    .and_return(true)
        AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:create_webhook).and_return(123_456)
        AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:token_expiration_date).and_return(Time.zone.parse('2100-12-31T00:00:00Z'))
        AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:webhook_exists?).and_return(false)

        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, false)

        post :enable_pr_workflow, params: {
          id: project.id,
          token: 'abc',
        }
        expect(response_result).to eq({
                                        'type' => 'InvalidOperationError',
                                        'message' => 'PR Workflow Feature is not enabled for this tenant',
                                      })

        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, true)

        post :enable_pr_workflow, params: {
          id: project.id,
          token: 'abc',
        }

        assert_success_async_response!(['job', 'id'])
        job = assert_async_response!(['job', 'id'])
        result = job.fetch_cache_data
        expect(result['message']).to eq('Enable PR workflow successfully')
        project.reload
        expect(SourceControl::Backend.passphrase_encryptor.decrypt(project.external_git_integration.token)).to eq('abc')

        # Check activity log for enable_pr_workflow
        expect(ActivityLog.exists?(key: 'aml_studio_project.enable_pr_workflow', owner: user, trackable: project,
                                   tenant_id: user.tenant_id,)).to eq(true)

        @controller = described_class.new

        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, false)
        post :update_token_pr_workflow, params: {
          id: project.id,
          token: 'xyz',
        }
        expect(response_result).to eq({
                                        'type' => 'InvalidOperationError',
                                        'message' => 'PR Workflow Feature is not enabled for this tenant',
                                      })

        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, true)

        post :update_token_pr_workflow, params: {
          id: project.id,
          token: 'xyz',
        }

        assert_success_async_response!(['job', 'id'])
        job = assert_async_response!(['job', 'id'])
        result = job.fetch_cache_data
        expect(result['message']).to eq('Token validated')
        expect(project.external_git_integration.webhook_id).to eq(123_456)
        project.reload
        expect(SourceControl::Backend.passphrase_encryptor.decrypt(project.external_git_integration.token)).to eq('xyz')

        # Check activity log for update_token_pr_workflow
        expect(ActivityLog.exists?(key: 'aml_studio_project.update_token_pr_workflow', owner: user, trackable: project,
                                   tenant_id: user.tenant_id,)).to eq(true)

        # wont delete webhook if disable without deleting token
        expect_any_instance_of(AmlStudio::GitFlows::GitClient::GithubClient).not_to receive(:delete_webhook)
        @controller = described_class.new

        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, false)
        post :disable_pr_workflow, params: {
          id: project.id,
          is_delete_token: false,
        }
        expect(response_result).to eq({
                                        'type' => 'InvalidOperationError',
                                        'message' => 'PR Workflow Feature is not enabled for this tenant',
                                      })

        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, true)

        post :disable_pr_workflow, params: {
          id: project.id,
          is_delete_token: false,
        }

        assert_success_response!
        project.reload
        expect(project.enabled_pr_workflow?).to be(false)
        expect(project.external_git_integration.webhook_id).to eq(123_456)
        expect(project.external_git_integration.token).not_to be_nil

        # Check activity log for disable_pr_workflow
        expect(ActivityLog.exists?(key: 'aml_studio_project.disable_pr_workflow', owner: user, trackable: project,
                                   tenant_id: user.tenant_id,)).to eq(true)

        expect(response_result).to eq({ 'message' => 'Disable PR Workflow successfully' })
      end

      it 'enables pr workflow settings and disable with deleting the token' do
        AmlStudio::GitFlows::GitClient::GithubClient.any_instance
                                                    .stub(:validate_read_pr_permission!)
                                                    .and_return(true)
        AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:create_webhook).and_return(654_321)
        AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:token_expiration_date).and_return(Time.zone.parse('2100-12-31T00:00:00Z'))
        AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:delete_webhook).and_return(true)

        post :enable_pr_workflow, params: {
          id: project.id,
          token: 'abc',
        }

        assert_success_async_response!(['job', 'id'])
        job = assert_async_response!(['job', 'id'])
        result = job.fetch_cache_data
        expect(result['message']).to eq('Enable PR workflow successfully')

        # Check activity log for enable_pr_workflow
        expect(ActivityLog.exists?(key: 'aml_studio_project.enable_pr_workflow', owner: user, trackable: project,
                                   tenant_id: user.tenant_id,)).to eq(true)

        @controller = described_class.new

        project.reload
        expect(project.enabled_pr_workflow?).to be(true)
        expect(project.external_git_integration.token).not_to be_nil
        expect(project.external_git_integration.token_expiration_date).to eq(Time.zone.parse('2100-12-31T00:00:00Z'))

        # expect delete webhook in this case, becasue there is no more use for this repository
        expect_any_instance_of(AmlStudio::GitFlows::GitClient::GithubClient).to receive(:delete_webhook)
        @controller = described_class.new

        post :disable_pr_workflow, params: {
          id: project.id,
          is_delete_token: true,
        }

        expect(response_result).to eq({ 'message' => 'Delete token successfully' })
        project.reload
        expect(project.enabled_pr_workflow?).to be(false)
        expect(project.external_git_integration.token).to be_nil
        expect(project.external_git_integration.webhook_id).to be_nil
        expect(project.external_git_integration.token_expiration_date).to eq(nil)

        # Check activity log for disable_pr_workflow
        expect(ActivityLog.exists?(key: 'aml_studio_project.disable_pr_workflow', owner: user, trackable: project,
                                   tenant_id: user.tenant_id,)).to eq(true)
      end

      it 'successfully disables pr workflow without deleting token' do
        # Mock: Set up project state as if PR workflow is already enabled
        external_git_integration = project.create_external_git_integration!(
          repo_url: project.remote_url_origin,
          provider: 'GitHub',
          token: SourceControl::Backend.passphrase_encryptor.encrypt('existing_token'),
          webhook_id: 123_456,
          token_expiration_date: Time.zone.parse('2100-12-31T00:00:00Z'),
        )
        project.update!(settings: project.settings.merge(enabled_pr_workflow: true))

        # Ensure webhook is not deleted
        expect_any_instance_of(AmlStudio::GitFlows::GitClient::GithubClient).not_to receive(:delete_webhook)

        # Test: Call disable PR workflow API
        post :disable_pr_workflow, params: {
          id: project.id,
          is_delete_token: false,
        }

        # Assertions
        assert_success_response!
        expect(response).to have_http_status(:ok)
        expect(response_result).to eq({ 'message' => 'Disable PR Workflow successfully' })

        # Verify project state
        project.reload
        expect(project.enabled_pr_workflow?).to be(false)
        expect(project.external_git_integration.webhook_id).to eq(123_456)
        expect(project.external_git_integration.token).not_to be_nil
      end

      it 'successfully updates token for pr workflow' do
        # Mock: Set up project state as if PR workflow is already enabled
        external_git_integration = project.create_external_git_integration!(
          repo_url: project.remote_url_origin,
          provider: 'GitHub',
          token: SourceControl::Backend.passphrase_encryptor.encrypt('original_token'),
          webhook_id: 789_123,
          token_expiration_date: Time.zone.parse('2100-12-31T00:00:00Z'),
        )
        project.update!(settings: project.settings.merge(enabled_pr_workflow: true))

        # Mock GitHub client validation
        AmlStudio::GitFlows::GitClient::GithubClient.any_instance
                                                    .stub(:validate_read_pr_permission!)
                                                    .and_return(true)
        AmlStudio::GitFlows::GitClient::GithubClient.any_instance
                                                    .stub(:token_expiration_date)
                                                    .and_return(Time.zone.parse('2100-12-31T00:00:00Z'))
        AmlStudio::GitFlows::GitClient::GithubClient.any_instance
                                                    .stub(:create_webhook)
                                                    .and_return(789_123)

        # Test: Call update token PR workflow API
        post :update_token_pr_workflow, params: {
          id: project.id,
          token: 'updated_token',
        }

        # Assertions
        assert_success_async_response!(['job', 'id'])
        expect(response).to have_http_status(:ok)

        job = assert_async_response!(['job', 'id'])
        result = job.fetch_cache_data
        expect(result['message']).to eq('Token validated')

        # Verify project state
        project.reload
        expect(SourceControl::Backend.passphrase_encryptor.decrypt(project.external_git_integration.token)).to eq('updated_token')
        expect(project.external_git_integration.webhook_id).to eq(789_123)
      end
    end
  end

  describe 'get latest events for each types' do
    context do
      it 'return 422 when pr workflow feature toggle is disabled' do
        get :fetch_latest_event,
            params: { id: project.id, branch_name: 'master', event_types: ['pull_request', 'deploy'] }
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'return 200 when pr workflow feature toggle is enabled' do
        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, true)
        get :fetch_latest_event,
            params: { id: project.id, branch_name: 'master', event_types: ['pull_request', 'deploy'] }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'return 403 when user is biz user' do
      let(:user) { get_test_user }

      it 'return 403' do
        get :fetch_latest_event,
            params: { id: project.id, branch_name: 'master', event_types: ['pull_request', 'deploy'] }
        expect(response).to have_http_status(:forbidden)
      end
    end
  end

  describe 'interpret_aml_slot' do
    let(:binding) do
      binding_raw = <<~JSON
        {
          "binding": {
            "condition": {
              "fqn": "H::git::is_production",
              "__doc__": "",
              "__type__": "Slot<Boolean>"
            },
            "then": "hdev",
            "else": "hdemo",
            "__doc__": "",
            "__type__": "Slot<IfExpression>"
          }
        }
      JSON

      JSON.parse(binding_raw)
    end

    it 'work' do
      post :interpret_aml_slot, params: { binding: binding, id: project.id }
      assert_success_response!

      expect(response_result).to eq({ 'binding' => { '__type__' => 'Slot<Resolved>', 'value' => 'hdev' } })
    end
  end

  describe '#parse_lookml' do
    it 'work' do
      lookml = <<~LOOKML
        view: user_campaign_facts_config {
          extends: [user_campaign_facts_core]
          extension: required
        }
      LOOKML

      VCR.use_cassette('parse_lookml') do
        post :parse_lookml, params: { lookml: lookml, id: project.id }
        expected = '{"success":true,"data":{"views":[{"extends__all":[["user_campaign_facts_core"]],"extension":"required","name":"user_campaign_facts_config"}]}}'
        assert_success_response!
        expect(response_result).to eq(JSON.parse(expected))
      end
    end
  end
end
