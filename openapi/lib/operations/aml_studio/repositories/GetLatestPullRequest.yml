operationId: AmlStudioRepositories_GetLatestPullRequest
summary: Get latest pull request information for given branch name on Git provider
tags:
  - Development
parameters:
  - name: project_id
    in: query
    description: AML Project Id
    schema:
      type: integer
responses:
  "200":
    description: "Pull request information"
    content:
      application/json:
        schema:
          type: object
          properties:
            status:
              type: string
              example: "OK"
            data:
              type: object
              nullable: true
              description: "Pull request information, null if no open PR was found"
  "422":
    $ref: ../../../components/responses/errors/InvalidOperationError.yml
