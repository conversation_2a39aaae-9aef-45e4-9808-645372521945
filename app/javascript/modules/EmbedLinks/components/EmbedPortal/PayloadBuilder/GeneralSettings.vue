<template>
  <div class="flex flex-col">
    <div class="border-b border-b-gray-300 bg-gray-100 px-3 py-2 font-medium">
      General settings
    </div>
    <div class="p-3">
      <div class="flex flex-col gap-3">
        <div class="text-2xs font-normal text-gray-600">
          Configure dashboard access and visibility settings. <a
            :href="docUrl"
            target="_blank"
          >Learn more</a>
        </div>

        <div class="mt-1.5 grid grid-cols-10 items-start">
          <div class="col-span-7 flex flex-col gap-1">
            <div class="text-xs font-medium">
              Allow users to export raw data
            </div>
            <div class="text-2xs font-normal text-gray-600">
              When enabled, users can export raw data in CSV or Excel format
            </div>
          </div>
          <div class="col-span-3 flex items-center justify-end">
            <HSwitch
              v-model="enableExportData"
              size="lg"
            />
          </div>
        </div>

        <div class="mt-1.5 grid grid-cols-5 items-start">
          <div class="col-span-3 flex flex-col gap-1">
            <div class="text-xs font-medium">
              Default timezone
            </div>
            <div class="text-2xs font-normal text-gray-600">
              Control timezone for all dashboards
            </div>
          </div>
          <div class="col-span-2">
            <DashboardTimezoneSelect
              v-model="defaultTimezone"
            />
          </div>
        </div>

        <div class="mt-1.5 grid grid-cols-2 items-start">
          <div class="col-span-1 flex flex-col gap-1">
            <div class="text-xs font-medium">
              Allow viewers to change the dashboard timezone
            </div>
          </div>
          <div class="col-span-1 flex items-center justify-end">
            <HSwitch
              v-model="allowToChangeDashboardTimezone"
              size="lg"
            />
          </div>
        </div>

        <div class="mt-1.5 grid grid-cols-5 items-start">
          <div class="col-span-3 flex flex-col gap-1">
            <div class="text-xs font-medium">
              Token expiry duration
            </div>
            <div class="mr-2 text-2xs font-normal text-gray-600">
              Set how long a generated preview token will be valid
            </div>
          </div>
          <div class="col-span-2">
            <HSelect
              v-model="expiryDuration"
              data-ci="embed-portal-expiry-duration-select"
              :options="expiryDurationOptions"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { EmbedPortalPayload } from '@/modules/EmbedPortal/types';
import { HSelect, HSwitch, SelectOption } from '@holistics/design-system';
import { computed } from 'vue';
import { generateDocsLink } from '@aml-studio/h/utils';
import DashboardTimezoneSelect from './DashboardTimezoneSelect.vue';

const docUrl = generateDocsLink('/docs/embedded-analytics');

const settings = defineModel<EmbedPortalPayload['settings']>('settings', {
  required: true,
});

const expiryDuration = defineModel<number | null>('expiryDuration', {
  required: true,
});

const expiryDurationOptions : SelectOption[] = [
  { label: '15 minutes', value: 15 * 60 },
  { label: '30 minutes', value: 30 * 60 },
  { label: '1 hour', value: 60 * 60 },
  {
    label: 'No expiration',
    icon: 'warning',
    class: '!text-orange-500',
    value: null,
    tooltip: 'Security Warning: The token won\'t expire. Use with extreme caution in trusted environments to keep your information secure',
  },
];

const defaultTimezone = computed({
  get: () => settings.value?.default_timezone || null,
  set: (value: string | null) => {
    settings.value = {
      ...settings.value,
      default_timezone: value,
    };
  },
});

const allowToChangeDashboardTimezone = computed({
  get: () => settings.value?.allow_to_change_dashboard_timezone,
  set: (value: boolean) => {
    settings.value = {
      ...settings.value,
      allow_to_change_dashboard_timezone: value,
    };
  },
});

const enableExportData = computed({
  get: () => settings.value?.allow_to_export_raw_data,
  set: (value: boolean) => {
    settings.value = {
      ...settings.value,
      allow_to_export_raw_data: value,
    };
  },
});
</script>
