<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useField, useForm } from 'vee-validate';
import { HButton, HModal, HRadio } from '@holistics/design-system';
import { unifyDataset } from '@holistics/aml-std';
import { AmlDataset } from '@aml-studio/client';
import DataSet from '@/modules/DataSets/models/DataSet';
import { saveVisualisationToDashboard } from '@/modules/DashboardAsCode/services/saveVisualisationToDashboard';
import { useToasts } from '@/core/composables/useToasts';
import { errorMessageFromAjax } from '@/core/services/ajax';
import { vizSettingToDashboardDefinition } from '@/modules/DashboardAsCode/utils/vizSettingToDashboardDefinition';
import type VizSetting from '@/modules/Viz/models/VizSetting';
import { useEmbedPortalStore } from '../store/embedPortalStore';
import { buildRouteForNode } from '../helpers/buildEmbedPortalTreeNodes';
import type { EmbedDashboard } from '../types';
import EmbedDashboardTreeSelect from './EmbedDashboardTreeSelect.vue';

interface Props {
  vizSetting: VizSetting
  dataset: DataSet | AmlDataset
  generatedTitle?: string
}

const props = defineProps<Props>();
const emit = defineEmits<{
  resolve:[]
  dismiss: []
}>();
const shown = defineModel<boolean>('shown', { required: true });
const saveState = ref<'unsave'| 'saved' | 'saving'>('unsave');

const embedPortalStore = useEmbedPortalStore();
const { toast } = useToasts();

enum ALL_SAVE_OPTIONS {
  existingDashboard,
  newOrgDashboard,
  newPersonalDashboard,
}
const saveOption = ref<ALL_SAVE_OPTIONS>(ALL_SAVE_OPTIONS.existingDashboard);

const canCreateOrgDashboard = computed(() => embedPortalStore.embedPortal?.actionBasedPermission.orgWorkspace.canEdit);
const canCreatePersonalDashboard = computed(() => embedPortalStore.embedPortal?.actionBasedPermission.personalWorkspace.canEdit);

const { validate } = useForm();
const { value: title, errorMessage: titleErrorMsg } = useField<string>('title', 'required', { initialValue: props.generatedTitle || '' });
const description = ref<string>('');
const { value: selectedDashboard, errorMessage: dashboardErrorMsg } = useField<EmbedDashboard>(
  'dashboard',
  computed(() => (saveOption.value === ALL_SAVE_OPTIONS.existingDashboard ? 'required' : undefined)),
);

async function saveToNewDashboard ({ isPersonal }: { isPersonal: boolean }) {
  const dashboardDefinition = vizSettingToDashboardDefinition({
    vizSetting: props.vizSetting,
    datasetId: props.dataset.id,
    dashboardTitle: `Dashboard ${title.value}`,
    blockTitle: title.value,
    blockDescription: description.value,
  });

  selectedDashboard.value = await embedPortalStore.createEmbedDashboard({ definition: dashboardDefinition, datasets: [unifyDataset(props.dataset)], isPersonal });
}

async function saveVisualization () {
  if (saveState.value !== 'unsave') return;

  const { valid } = await validate();
  if (!valid) return;

  saveState.value = 'saving';
  try {
    switch (saveOption.value) {
      case ALL_SAVE_OPTIONS.newOrgDashboard:
        await saveToNewDashboard({ isPersonal: false });
        break;
      case ALL_SAVE_OPTIONS.newPersonalDashboard:
        await saveToNewDashboard({ isPersonal: true });
        break;
      default:
        await saveVisualisationToDashboard({
          vizSetting: props.vizSetting, dataset: props.dataset as any, title: title.value, description: description.value, dashboard_id: selectedDashboard.value.id as number,
        });
        break;
    }

    saveState.value = 'saved';
  } catch (error: any) {
    toast.danger(errorMessageFromAjax(error));
    saveState.value = 'unsave';
  }
}

function close () {
  emit('dismiss');
}

const router = useRouter();
function goToDashboard () {
  if (!selectedDashboard.value) return;

  router.push(buildRouteForNode({ type: 'Dashboard', id: selectedDashboard.value.id }, embedPortalStore.secretToken));
  close();
}
</script>

<template>
  <HModal
    v-model:shown="shown"
    title="Save Visualization"
    :dismiss-button="saveState === 'saved' ? 'Close' : 'Cancel'"
    @dismiss="close"
  >
    <div v-if="saveState !== 'saved'">
      <div class="h-form-label required-label">
        Title
      </div>
      <input
        v-model="title"
        class="h-input h-form-input"
        data-ci="save-embed-visualization-title"
        placeholder="Title"
        type="text"
      >
      <div class="h-form-invalid-feedback">
        {{ titleErrorMsg }}
      </div>

      <div class="mt-4">
        <div class="h-form-label required-label">
          Save to
        </div>
        <HRadio
          v-model="saveOption"
          class="mt-1"
          :opt-value="ALL_SAVE_OPTIONS.existingDashboard"
          data-ci="save-embed-visualization-existing-dashboard"
        >
          <span>Existing dashboard</span>
        </HRadio>
        <EmbedDashboardTreeSelect
          v-model="selectedDashboard"
          class="ml-4 mt-1"
          :disabled="saveOption !== ALL_SAVE_OPTIONS.existingDashboard"
        />
        <div
          v-if="dashboardErrorMsg"
          class="h-form-invalid-feedback ml-4 mt-1"
        >
          {{ dashboardErrorMsg }}
        </div>
        <div class="mt-3">
          <HRadio
            v-if="canCreateOrgDashboard"
            v-model="saveOption"
            :opt-value="ALL_SAVE_OPTIONS.newOrgDashboard"
            data-ci="save-embed-visualization-new-org-dashboard"
          >
            New dashboard in shared workspace
          </HRadio>
        </div>
        <div class="mt-3">
          <HRadio
            v-if="canCreatePersonalDashboard"
            v-model="saveOption"
            :opt-value="ALL_SAVE_OPTIONS.newPersonalDashboard"
            data-ci="save-embed-visualization-new-personal-dashboard"
          >
            New dashboard in personal workspace
          </HRadio>
        </div>
      </div>
    </div>
    <div v-else>
      <span>Saved visualization to dashboard </span>
      <a
        class="cursor-pointer"
        data-ci="save-embed-visualization-navigate-to-dashboard"
        @click.prevent="goToDashboard"
      >{{ selectedDashboard?.title }}</a>
      <span> successfully.</span>
    </div>

    <template #resolve-button>
      <HButton
        v-if="saveState !== 'saved'"
        class="ml-1"
        type="primary-highlight"
        label="Confirm"
        :disabled="saveState === 'saving'"
        :icon="saveState === 'saving' ? 'loading' : undefined"
        :icon-spin="saveState === 'saving'"
        @click="saveVisualization"
      />
      <div />
    </template>
  </HModal>
</template>

<style scoped lang="postcss">
.required-label::after {
  content: '*';
  color: red;
}
</style>
