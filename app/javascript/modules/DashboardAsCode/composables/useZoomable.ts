import {
  computed, ref, nextTick, type Ref,
  onMounted,
  watch,
} from 'vue';
import {
  createInjectionState,
  useEventListener,
  useThrottleFn,
  useElementSize,
} from '@vueuse/core';
import { type DashboardLayout } from '@holistics/aml-std';
import { hideAllPoppers } from '@holistics/floating-vue';
import { getScrollBarWidth, isEditableElement } from '@holistics/utils/ui';
import { hideAllDatePickers } from '@/core/components/ui/DatePickers/utils/hideAllDatePickers';
import { isOSX } from '@holistics/utils';
import { check as checkFeatureToggle } from '@/core/services/featureToggle';
import { DashboardAsCode } from '@/modules/DashboardAsCode/types';
import { findScrollParent } from '../utils/findScrollParent';
import type { UpdateDashboardFn } from './useDashboardUiEditor';
import { IS_PPTR, IS_PUBLIC_LINK } from '../constants/views';
import { createZoomUpdateEvent, getUpdatePaths } from '../utils/zooms';

const FALLBACK_GRID_SIZE = 10;

export type ZoomMode = 'fit_to_width' | 'fit_to_page' | 'custom';

export interface ZoomableOptions {
  parentLayout: Ref<DashboardLayout | undefined>
  currentLayout: Ref<DashboardLayout | undefined>
  maxZoomLevel?: number
  minZoomLevel?: number
  updateDashboardFn?: UpdateDashboardFn
  saveToSessionStorage?: boolean
}

function getElement (wrapper?: HTMLElement | { $el: HTMLElement }) {
  if (!wrapper) {
    return undefined;
  }
  if (wrapper instanceof HTMLElement) {
    return wrapper;
  }
  return wrapper.$el;
}

function getContainerSize (scrollParent: HTMLElement, el: HTMLElement, axis: 'width' | 'height') {
  const scrollStyles = getComputedStyle(scrollParent);
  const elStyles = getComputedStyle(el);

  const paddingKeys: (keyof CSSStyleDeclaration)[] = axis === 'width'
    ? ['paddingLeft', 'paddingRight']
    : ['paddingTop', 'paddingBottom'];

  const scrollPadding = paddingKeys
    .map(key => {
      const value = scrollStyles[key];
      return value === null ? 0 : parseFloat(value as string);
    })
    .reduce((a, b) => a + b, 0);

  const elPadding = paddingKeys
    .map(key => {
      const value = elStyles[key];
      return value === null ? 0 : parseFloat(value as string);
    })
    .reduce((a, b) => a + b, 0);

  const size = axis === 'width' ? scrollParent.clientWidth : scrollParent.clientHeight;
  return size - scrollPadding - elPadding;
}

// calculate the real width without margin
function getContainerWidth (scrollParent: HTMLElement, el: HTMLElement) {
  return getContainerSize(scrollParent, el, 'width');
}

function getContainerHeight (scrollParent: HTMLElement, el: HTMLElement) {
  return getContainerSize(scrollParent, el, 'height');
}

const ZOOM_STEP = 0.10;
const WHEEL_ZOOM_STEP = 0.0010;

const [useProvideZoomable, _useZoomable] = createInjectionState((dashboard: Ref<DashboardAsCode>, options: ZoomableOptions) => {
  const { currentLayout, parentLayout } = options;
  const minZoomLevel = options.minZoomLevel || 0.1;
  const maxZoomLevel = options.maxZoomLevel || 3;

  if (minZoomLevel <= 0) {
    throw new Error('minZoomLevel must be positive');
  }
  if (maxZoomLevel <= minZoomLevel) {
    throw new Error('maxZoomLevel must be greater than minZoomLevel');
  }

  const updatePaths = computed(() => getUpdatePaths(currentLayout.value, parentLayout.value));

  const defaultZoomConfig = computed({
    get () {
      if (currentLayout.value?.type === 'CanvasLayout') {
        return currentLayout.value.default_zoom || 1;
      }
      return 1;
    },
    set (value: number | 'fit_to_width' | 'fit_to_page') {
      if (!options.updateDashboardFn) {
        return;
      }
      if (!updatePaths.value) return;
      const event = createZoomUpdateEvent(value, updatePaths.value);

      if (!event) return;
      options.updateDashboardFn(event);
    },
  });

  const zoomContainer = ref<HTMLElement | { $el: HTMLElement }>();
  const zoomTarget = ref<HTMLElement | { $el: HTMLElement }>();
  const scrollParent = computed(() => findScrollParent(getElement(zoomContainer.value)));
  const scrollParentSize = useElementSize(scrollParent);

  const zoomKey = computed(() => `dashboard-${dashboard.value.uname}-${currentLayout.value?.type}-${currentLayout.value?.uname}`);

  const initialZoomLevel = computed(() => {
    if (!currentLayout.value) return 1;
    if (currentLayout.value?.type !== 'CanvasLayout') return 1;
    return typeof currentLayout.value.default_zoom === 'number' ? currentLayout.value.default_zoom : 1;
  });

  const initialZoomMode = computed(() => {
    if (!currentLayout.value) return 'custom';
    if (currentLayout.value?.type !== 'CanvasLayout') return 'custom';
    return typeof currentLayout.value.default_zoom === 'string' ? currentLayout.value.default_zoom : 'custom';
  });

  function initializeZoomLevel () {
    if (IS_PPTR) return ref(1);
    if (options.saveToSessionStorage) {
      const zoomLevelValue = Number(window?.sessionStorage?.getItem(`${zoomKey.value}-zoom-level`)) || initialZoomLevel.value;
      return ref(Math.max(Math.min(Math.floor(100 * zoomLevelValue) / 100, maxZoomLevel), minZoomLevel));
    }
    return ref(initialZoomLevel.value);
  }

  function initializeZoomMode () {
    if (IS_PPTR) return ref('custom' as ZoomMode);
    if (options.saveToSessionStorage) {
      const zoomModeValue = window?.sessionStorage?.getItem(`${zoomKey.value}-zoom-mode`) || initialZoomMode.value;
      return ref(zoomModeValue);
    }
    return ref(initialZoomMode.value);
  }

  const zoomLevel = initializeZoomLevel();
  const zoomMode = initializeZoomMode();

  const smartMode = ref(false);
  const snapToGrid = ref(true);
  const gridSize = computed({
    get () {
      if (currentLayout.value?.type === 'CanvasLayout') {
        return currentLayout.value.grid_size || FALLBACK_GRID_SIZE;
      }
      return 0;
    },
    set (value: number) {
      if (!options.updateDashboardFn) {
        return;
      }
      if (currentLayout.value?.type === 'CanvasLayout') {
        options.updateDashboardFn({
          event: 'UpdateCanvasSize',
          uname: currentLayout.value.uname,
          size: {
            grid_size: value,
          },
        });
      }
    },
  });

  const transform = computed(() => {
    return `matrix(${zoomLevel.value}, 0, 0, ${zoomLevel.value}, 0, 0)`;
  });

  const hideAllPopovers = useThrottleFn(() => {
    hideAllPoppers();
    hideAllDatePickers();
  }, 1000);

  function setCustomZoomLevel (level: number) {
    if (zoomMode.value !== 'custom') {
      zoomMode.value = 'custom';
    }
    zoomLevel.value = level;
  }

  function zoomIn () {
    hideAllPopovers();
    setCustomZoomLevel(Math.min(maxZoomLevel, Math.round(zoomLevel.value / ZOOM_STEP + 1) * ZOOM_STEP));
  }
  function zoomOut () {
    hideAllPopovers();
    setCustomZoomLevel(Math.max(minZoomLevel, Math.round(zoomLevel.value / ZOOM_STEP - 1) * ZOOM_STEP));
  }
  function resetZoom () {
    hideAllPopovers();
    setCustomZoomLevel(1);
  }

  function fitToContainerWidth () {
    zoomMode.value = 'fit_to_width';

    const target = getElement(zoomTarget.value);
    const container = getElement(zoomContainer.value);
    if (!target || !container) {
      return;
    }
    const containerWidth = getContainerWidth(scrollParent.value, container) - getScrollBarWidth();
    const targetWidth = target.clientWidth;

    const newZoomLevel = Math.max(Math.min(Math.floor((100 * containerWidth) / targetWidth) / 100, maxZoomLevel), minZoomLevel);
    zoomLevel.value = newZoomLevel || 1; // fallback to 1
  }

  function fitToPage () {
    zoomMode.value = 'fit_to_page';

    const target = getElement(zoomTarget.value);
    const container = getElement(zoomContainer.value);
    if (!target || !container) return;

    const containerWidth = getContainerWidth(scrollParent.value, container) - getScrollBarWidth();
    const containerHeight = getContainerHeight(scrollParent.value, container) - getScrollBarWidth();

    const targetWidth = target.clientWidth;
    const targetHeight = target.clientHeight;

    const zoomWidth = containerWidth / targetWidth;
    const zoomHeight = containerHeight / targetHeight;

    const newZoomLevel = Math.max(Math.min(Math.floor(100 * Math.min(zoomWidth, zoomHeight)) / 100, maxZoomLevel), minZoomLevel);
    zoomLevel.value = newZoomLevel || 1; // fallback to 1
  }

  function makeCurrentZoomDefault () {
    if (zoomMode.value === 'custom') {
      defaultZoomConfig.value = Math.max(Math.min(Math.floor(100 * zoomLevel.value) / 100, maxZoomLevel), minZoomLevel);
    } else if (['fit_to_width', 'fit_to_page'].includes(zoomMode.value) && defaultZoomConfig.value !== zoomMode.value) {
      defaultZoomConfig.value = zoomMode.value as 'fit_to_width' | 'fit_to_page';
    }
  }

  function repositionTheContainer (targetX: number, targetY: number, currentZoom: number, newZoom: number) {
    const zoomEl = getElement(zoomTarget.value);

    if (!zoomEl) {
      // don't handle scroll, just set zoom level
      return;
    }

    const zoomRect = zoomEl.getBoundingClientRect();
    const scrollRect = scrollParent.value.getBoundingClientRect();

    if (zoomRect.width < scrollRect.width && zoomRect.height < scrollRect.height) {
      // no scroll
      return;
    }

    const zoomElWidth = zoomEl.clientWidth;

    const currentX = zoomElWidth / 2 - ((zoomRect.x + zoomRect.width / 2) - targetX) / currentZoom;
    const currentY = (targetY - zoomRect.y) / currentZoom;
    const scrollTop = scrollParent.value.scrollTop + currentY * (newZoom - currentZoom);
    const scrollLeft = scrollParent.value.scrollLeft + currentX * (newZoom - currentZoom);

    nextTick(() => {
      scrollParent.value.scroll({
        top: scrollTop,
        left: scrollLeft,
        behavior: 'instant',
      });
    });
  }

  useEventListener('wheel', (e) => {
    // Note: In trackpad mode, it auto include ctrl key
    if (!e.metaKey && !e.ctrlKey) {
      return;
    }
    e.preventDefault();

    const currentZoom = zoomLevel.value;
    const newZoom = Math.min(Math.max(zoomLevel.value + e.deltaY * -WHEEL_ZOOM_STEP, minZoomLevel), maxZoomLevel);
    if (currentZoom === newZoom) {
      return;
    }

    hideAllPopovers();

    setCustomZoomLevel(newZoom);

    repositionTheContainer(e.clientX, e.clientY, currentZoom, newZoom);
  }, {
    passive: false,
  });

  // pinch zoom
  const pinchZoomStartZoom = ref(1);
  const pinchZoomStartDistance = ref(0);
  const pinchZoomStartCenter = ref({ x: 0, y: 0 });
  function calculateTouchesDistance (touch1: Touch, touch2: Touch) {
    return Math.hypot(touch1.pageX - touch2.pageX, touch1.pageY - touch2.pageY);
  }
  useEventListener('touchstart', (e: TouchEvent) => {
    const { touches } = e;
    if (touches.length === 2) {
      e.preventDefault();
      pinchZoomStartZoom.value = zoomLevel.value;
      pinchZoomStartDistance.value = calculateTouchesDistance(touches[0], touches[1]);
      pinchZoomStartCenter.value = {
        x: (touches[0].pageX + touches[1].pageX) / 2,
        y: (touches[0].pageY + touches[1].pageY) / 2,
      };
    }
  }, { passive: false });

  useEventListener('touchmove', (e: TouchEvent) => {
    const { touches } = e;
    if (touches.length === 2) {
      e.preventDefault();
      const currentZoom = zoomLevel.value;
      const distance = calculateTouchesDistance(touches[0], touches[1]);
      let newZoom = currentZoom;
      if (distance > pinchZoomStartDistance.value) {
        // zoom in
        newZoom = Math.min(pinchZoomStartZoom.value * (distance / pinchZoomStartDistance.value), maxZoomLevel);
      } else {
        // zoom out
        newZoom = Math.max(pinchZoomStartZoom.value * (distance / pinchZoomStartDistance.value), minZoomLevel);
      }

      setCustomZoomLevel(newZoom);

      repositionTheContainer(pinchZoomStartCenter.value.x, pinchZoomStartCenter.value.y, currentZoom, newZoom);
    }
  }, { passive: false });

  useEventListener(['touchend', 'touchcancel'], (e: TouchEvent) => {
    pinchZoomStartZoom.value = zoomLevel.value;
    pinchZoomStartDistance.value = 0;
    pinchZoomStartCenter.value = { x: 0, y: 0 };

    if (e.touches.length > 1 || e.changedTouches.length > 1) {
      e.preventDefault();
    }
  }, { passive: false });

  useEventListener('keydown', (e) => {
    if (e.shiftKey && ['Digit1', 'Digit2'].includes(e.code) && !isEditableElement(e.target as HTMLElement)) {
      e.preventDefault();
      switch (e.code) {
        case 'Digit1':
          fitToContainerWidth();
          break;
        case 'Digit2':
          fitToPage();
          break;
        default:
          break;
      }
      return;
    }

    //  Note: prevent zoom default behavior browser on mac and window
    //  Don't use keyCode because keyCode in firefox and chrome different
    //  Chrome (187 = +, 189 = -), Firefox (173 = -, 61 = + )
    if (isOSX() && !e.metaKey) return;
    if (!isOSX() && !e.ctrlKey) return;

    const isZoomKey = ['-', '=', '0'].includes(e.key);
    if (!isZoomKey) return;
    e.preventDefault();

    switch (e.key) {
      case '-':
        zoomOut();
        break;
      case '=':
        zoomIn();
        break;
      case '0':
        resetZoom();
        break;
      default:
        break;
    }
  }, {
    passive: false,
  });

  const hasNoDefaultZoom = computed(() => {
    if (!currentLayout.value) return true;
    if (currentLayout.value?.type !== 'CanvasLayout') return true;
    return !currentLayout.value?.default_zoom;
  });

  onMounted(() => {
    nextTick(() => {
      if (checkFeatureToggle('dashboard_v4:fit_to_width_by_default') || (IS_PUBLIC_LINK && hasNoDefaultZoom.value)) {
        fitToContainerWidth();
      }
    });
  });

  const handleResize = useThrottleFn(() => {
    switch (zoomMode.value) {
      case 'fit_to_page':
        fitToPage();
        break;
      case 'fit_to_width':
        fitToContainerWidth();
        break;
      default:
        break;
    }
  }, 50);

  watch(() => [scrollParentSize.width.value, scrollParentSize.height.value], () => {
    handleResize();
  });

  // Set zoom to session storage
  watch(() => zoomLevel.value, () => {
    if (options.saveToSessionStorage) {
      try {
        window?.sessionStorage?.setItem(`${zoomKey.value}-zoom-level`, `${zoomLevel.value}`);
      } catch (e) {
        // ignore
      }
    }
  });

  watch(() => zoomMode.value, () => {
    if (options.saveToSessionStorage) {
      try {
        window?.sessionStorage?.setItem(`${zoomKey.value}-zoom-mode`, zoomMode.value);
      } catch (e) {
        // ignore
      }
    }
  });

  watch(() => [initialZoomLevel.value, initialZoomMode.value], () => {
    const initLevel = initializeZoomLevel();
    const initMode = initializeZoomMode();

    if (initLevel.value === zoomLevel.value && initMode.value === zoomMode.value) return;
    zoomLevel.value = initLevel.value;
    zoomMode.value = initMode.value;
  });

  return {
    zoomContainer,
    zoomTarget,
    minZoomLevel,
    maxZoomLevel,
    zoomLevel,
    transform,
    smartMode,
    snapToGrid,
    gridSize,
    zoomIn,
    zoomOut,
    resetZoom,
    fitToContainerWidth,
    fitToPage,
    zoomMode,
    defaultZoomConfig,
    makeCurrentZoomDefault,
  };
});

export { useProvideZoomable };

export function useZoomable () {
  const zoomStore = _useZoomable();
  if (zoomStore == null) { throw new Error('Please call `useProvideZoomable` on the appropriate parent component'); }
  return zoomStore;
}
