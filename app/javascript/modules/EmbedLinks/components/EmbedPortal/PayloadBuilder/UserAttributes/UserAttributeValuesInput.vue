<template>
  <div class="mt-4">
    <div class="flex items-center">
      <div class="flex w-[144px] px-2">
        <HIcon :name="dataTypeIcon" />
        <span class="ml-1 font-medium">{{ attributeName }}</span>
      </div>
      <div class="flex-grow px-2">
        <HPopover
          placement="bottom-start"
          :arrow="false"
          content-class="w-[300px]"
          @interact-outside="e => { if (isFocusingInput) e.preventDefault() }"
        >
          <div class="cursor-pointer">
            <div
              v-if="inputType === 'all'"
              class="flex h-[32px] items-center rounded border border-gray-300 bg-white px-2"
            >
              is any value ("__ALL__")
            </div>
            <div
              v-else-if="isEmptyAttributeValues"
              class="flex h-[32px] items-center rounded border border-gray-300 bg-white px-2 text-gray-600"
            >
              No values
            </div>
            <VizFilterForm
              v-else
              :model-value="computeCondition({
                attributeType,
                values: customValues,
              })"
              :field-type="attributeType"
              fixed-operator
              inline
              disabled
            />
          </div>
          <template #content>
            <div>
              <HRadio
                :model-value="inputType"
                opt-value="all"
                @update:model-value="selectAllValues"
              >
                is any value ("__ALL__")
              </HRadio>
            </div>
            <div class="mt-2">
              <HRadio
                :model-value="inputType"
                opt-value="custom"
                @update:model-value="selectCustomValues"
              >
                custom values
              </HRadio>
              <VizFilterForm
                class="ml-4 mt-1"
                :model-value="computeCondition({
                  attributeType,
                  values: customValues,
                })"
                :field-type="attributeType"
                fixed-operator
                inline
                @update:model-value="({ values }) => updateCustomValues(values)"
                @focus-input="focusInput"
              />
              <div class="ml-4 mt-1 text-gray-600">
                If no value is set, users won't see any data associated with this attribute
              </div>
            </div>
          </template>
        </HPopover>
      </div>
      <HTooltip :content="isRequired ? 'This attribute is required for Permission Rules' : ''">
        <HButton
          type="tertiary-default"
          size="sm"
          unified
          icon="delete"
          :disabled="isRequired"
          @click="emit('delete')"
        />
      </HTooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CORE_TYPES_MAP } from '@/modules/DataModels/constants';
import {
  HButton, HIcon, HPopover, HRadio, HTooltip, IconName,
} from '@holistics/design-system';
import { cloneDeep, get, isArray } from 'lodash';
import { computed, nextTick, ref } from 'vue';
import { AttributeValuesType, CustomAttributesValuesType } from '@/modules/EmbedPortal/types';
import Condition from '@/modules/Viz/submodules/VizFilters/models/Condition';
import VizFilterForm from '@/modules/Viz/submodules/VizFilters/components/VizFilterForm.vue';

const props = defineProps<{
  attributeName: string,
  attributeType: string,
  isRequired: boolean,
  values: AttributeValuesType,
}>();

const emit = defineEmits<{
  updateValue: [values: AttributeValuesType],
  delete: [],
}>();

const initCustomValues = (values: AttributeValuesType): CustomAttributesValuesType => {
  if (values === '__ALL__') return [];

  if (isArray(values)) return values;

  return [values];
};

const inputType = ref<'all' | 'custom'>(props.values === '__ALL__' ? 'all' : 'custom');
const customValues = ref<(string | number | boolean)[]>(cloneDeep(initCustomValues(props.values)));
const isFocusingInput = ref(false);

const dataTypeIcon = computed<IconName>(() => {
  return get(CORE_TYPES_MAP, [props.attributeType, 'icon'], 'unknown') as IconName;
});

const computeCondition = (attribute: { attributeType: string, values: any }) => {
  let computedOperator = 'is';
  const { values } = attribute;
  let computedValues = (values === '__ALL__' || !isArray(values)) ? [] : values;
  if (['date', 'datetime'].includes(attribute.attributeType)) {
    computedOperator = 'matches';
    computedValues = values.slice(0, 1);
  }
  return new Condition({
    operator: computedOperator,
    values: computedValues,
  });
};

const isEmptyAttributeValues = computed(() => inputType.value === 'custom' && customValues.value.length === 0);

const updateCustomValues = (newValues: CustomAttributesValuesType) => {
  customValues.value = newValues;
  emit('updateValue', newValues);

  nextTick(() => { isFocusingInput.value = false; });
};

const focusInput = () => {
  if (props.attributeType === 'date') {
    isFocusingInput.value = true;
  }
};

const selectAllValues = () => {
  inputType.value = 'all';
  emit('updateValue', '__ALL__');
};

const selectCustomValues = () => {
  inputType.value = 'custom';
  emit('updateValue', customValues.value);
};

</script>
