# typed: true

module AmlStudio
  module PrWorkflow
    module Events
      class EventHandler < T::Struct
        # TODO: handle when there is a push event in production branch
        VALID_EVENTS = ['pull_request', 'push', 'ping'].freeze

        const :event_type, String
        const :payload, T::Hash[String, T.untyped]
        const :external_git_integration, ExternalGitIntegration
        const :git_client_class, T.class_of(AmlStudio::GitFlows::GitClient::GithubClient)

        sig { returns(T.untyped) }
        def call
          case event_type
          when 'pull_request'
            handle_pull_request
          when 'push'
            # TODO: Implement push event handling
          when 'ping'
            ['pong']
          end
        end

        private

        sig { returns(T::Array[String]) }
        def handle_pull_request
          parsed_data = git_client_class.parse_pull_request_event(payload)

          messages = []
          external_git_integration.projects.each do |project|
            next unless project.enabled_pr_workflow?

            HandlePullRequestEvent.new(
              project: project,
              parsed_data: parsed_data,
            ).call
            success_message = "project with id #{project.id}: success"
            messages << success_message
          rescue StandardError => e
            messages << "project with id #{project.id}: #{e.message}"
          end
          messages
        end
      end
    end
  end
end
