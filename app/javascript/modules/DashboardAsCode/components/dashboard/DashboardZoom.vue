<script setup lang="ts">
import { computed, h } from 'vue';
import {
  HButton, HDropdown, HIcon, type DropdownOption,
  type IconName,
} from '@holistics/design-system';
import { isOSX } from '@holistics/utils';
import { IS_MOBILE } from '../../constants/views';
import { useZoomable } from '../../composables/useZoomable';
import DropdownOptionWithHint from '../utils/DropdownOptionWithHint.vue';

const props = defineProps<{
  editMode?: boolean
}>();

const {
  zoomContainer, zoomTarget, zoomLevel,
  zoomIn, zoomOut, resetZoom, fitToContainerWidth,
  maxZoomLevel, minZoomLevel, fitToPage, zoomMode,
  defaultZoomConfig, makeCurrentZoomDefault,
} = useZoomable();

const COMMAND_KEY = isOSX() ? '⌘' : 'Ctrl';
const SHIFT_KEY = isOSX() ? '⇧' : 'Shift';

function zoomText (zoom?: number | 'fit_to_width' | 'fit_to_page') {
  if (!zoom) return '100%';
  if (zoom === 'fit_to_width') return 'Fit to width';
  if (zoom === 'fit_to_page') return 'Fit to page';
  return `${Math.round(zoom * 100)}%`;
}

const zoomLevelText = computed(() => zoomText(zoomLevel.value));

const onZoomInput = (event: Event) => {
  if (!event?.target) return;

  const target = event.target as HTMLInputElement;
  if (!target.value) return;

  const value = target.value.replace('%', '').trim();
  // Only accept positive number and floats number
  if (/^\d*\.?\d*$/.test(value)) {
    zoomLevel.value = Math.max(minZoomLevel, Math.min(maxZoomLevel, Number(value) / 100));
  }
};

function createZoomOption ({
  key, label, icon, hint, onClick, checked, description,
}: {
  key: string; label: string; icon?: IconName; hint?: string; onClick: () => void; checked?: boolean; description?: string; }): DropdownOption {
  return {
    type: 'render',
    key,
    render () {
      return h(DropdownOptionWithHint, {
        class: `w-56 ci-${key}`,
        label,
        icon,
        hint,
        checked,
        onClick,
        description,
      });
    },
  };
}

const zoomOptions = computed(() => {
  const defaultZoomOptions: DropdownOption[] = [
    { key: 'zoom-input', slot: 'zoom-input' },
    createZoomOption({
      key: 'zoom-in',
      label: 'Zoom in',
      icon: 'search-plus-light',
      hint: `${COMMAND_KEY} +`,
      onClick: () => { zoomIn(); },
    }),
    createZoomOption({
      key: 'zoom-out',
      label: 'Zoom out',
      icon: 'search-minus-light',
      hint: `${COMMAND_KEY} -`,
      onClick: () => { zoomOut(); },
    }),
    createZoomOption({
      key: 'zoom-to-100',
      label: 'Zoom to 100%',
      icon: 'search-plus-light',
      hint: `${COMMAND_KEY} 0`,
      onClick: () => { resetZoom(); },
    }),
    createZoomOption({
      key: 'zoom-to-fit',
      label: 'Fit to width',
      icon: 'arrow-back-forth',
      hint: `${SHIFT_KEY} 1`,
      onClick: () => { fitToContainerWidth(); },
      checked: zoomMode.value === 'fit_to_width',
    }),
    createZoomOption({
      key: 'zoom-to-fit-page',
      label: 'Fit to page',
      icon: 'expand-square',
      checked: zoomMode.value === 'fit_to_page',
      hint: `${SHIFT_KEY} 2`,
      onClick: () => { fitToPage(); },
    }),
  ];
  return props.editMode ? [...defaultZoomOptions,
    { type: 'divider' },
    createZoomOption({
      key: 'set-default-zoom',
      label: 'Make current zoom default',
      description: defaultZoomConfig.value ? `Default zoom: ${zoomText(defaultZoomConfig.value)}` : '',
      onClick: () => { makeCurrentZoomDefault(); },
    })] : defaultZoomOptions;
});

function onZoomInputFocus (e: FocusEvent) {
  // Select all text value when click on zoom input
  return e.target && (e.target as HTMLInputElement).select();
}
</script>

<template>
  <div
    v-if="zoomContainer && zoomTarget"
    class="flex items-center space-x-1"
  >
    <template v-if="IS_MOBILE">
      <HButton
        type="secondary-default"
        icon="search-minus-light"
        unified
        @click="zoomOut"
      />
      <HButton
        type="secondary-default"
        icon="search-plus-light"
        unified
        @click="zoomIn"
      />
    </template>
    <HDropdown
      v-else
      :options="zoomOptions"
    >
      <div
        class="flex w-[70px] cursor-pointer items-center rounded border bg-white px-2 py-1.5 hover:border-blue-400"
        data-ci="ci-zoom-dropdown"
      >
        <span class="tabular-nums">
          {{ zoomLevelText }}
        </span>
        <h-icon
          name="angle-down"
          class="ml-auto"
        />
      </div>
      <template #zoom-input>
        <input
          :value="zoomLevelText"
          class="mb-1 w-full cursor-text items-center rounded border border-gray-300 p-2 tabular-nums hover:border-blue-400 focus:border-blue-400 focus:outline-none"
          @focus="onZoomInputFocus"
          @blur="onZoomInput"
          @keyup.enter="onZoomInput"
        >
      </template>
    </HDropdown>
  </div>
</template>
