import type VizSetting from '@/modules/Viz/models/VizSetting';
import { vizSettingToDashboardDefinition } from './vizSettingToDashboardDefinition';

describe('vizSettingToDashboardDefinition', () => {
  it('should create a dashboard definition with correct structure', () => {
    const vizSetting = {
      id: null,
      amql: {
        filters: [],
        adhoc_fields: [],
        conditions: [],
      },
      fields: {
        table_fields: [
          {
            type: 'number',
            uuid: '!fields[0]',
            format: {
              type: 'number',
            },
            path_hash: {
              model_id: 'public_viz_settings',
              field_name: 'id',
            },
          },
          {
            type: 'text',
            uuid: '!fields[1]',
            format: {
              type: 'string',
            },
            path_hash: {
              model_id: 'public_viz_settings',
              field_name: 'source_type',
            },
          },
          {
            type: 'number',
            uuid: '!fields[2]',
            format: {
              type: 'number',
              format: {
                pattern: 'inherited',
              },
            },
            path_hash: {
              model_id: 'public_viz_settings',
              field_name: 'viz_type',
            },
          },
        ],
      },
      format: {},
      filters: [],
      settings: {
        misc: {
          row_limit: 5000,
          row_height: 'Single line',
          pagination_size: 25,
          show_row_number: true,
        },
        others: {
          include_empty_children_rows: false,
        },
        aggregation: {
          show_total: false,
          show_average: false,
        },
        aggregate_awareness: {
          enabled: true,
          debug_comments: true,
        },
        conditional_formatting: [],
      },
    } as unknown as VizSetting;

    const result = vizSettingToDashboardDefinition({
      vizSetting,
      blockTitle: 'test viz block',
      dashboardTitle: 'new dashboard from viz block',
      blockDescription: 'test block description',
      datasetId: 1,
    });

    expect(result).toMatchSnapshot();
  });
});
