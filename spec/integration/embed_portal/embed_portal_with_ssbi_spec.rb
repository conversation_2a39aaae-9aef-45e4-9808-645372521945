# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'embed_portal_with_ssbi', :js do
  include_context 'embed_portal_with_ssbi'

  def dashboard_node_selector(dashboard_id)
    "[data-hui-key=\"Dashboard-#{dashboard_id}\"]"
  end

  it 'can create dashboard in org workspace' do
    visit embed_url

    safe_click('[data-ci="button-create-org-workspace-dashboard"]')
    wait_expect(true) { page.has_css?('[data-hui-key="new-org-dashboard"') }
    sleep 1

    set_text('[data-ci="edit-dashboard-title"] .ci-title-input', 'New test org dashboard')
    safe_click('[data-ci="save-canvas-dashboard"]')
    wait_for_all_ajax_requests

    new_dashboard = Dashboard.find_by(title: 'New test org dashboard')

    expect(new_dashboard.external_user_item.is_personal).to eq(false)
    # should remove the temporary creation node
    wait_expect(true) { page.has_no_css?('[data-hui-key="new-org-dashboard"') }
    # navigate to new node and highlight it
    wait_expect(true) { page.has_css?("#{dashboard_node_selector(new_dashboard.id)} .bg-blue-50") }
  end

  it 'can create dashboard in team workspace and edit' do
    visit embed_url

    safe_click('[data-ci="button-create-personal-workspace-dashboard"]')
    wait_expect(true) { page.has_css?('[data-hui-key="new-personal-dashboard"') }
    sleep 1

    set_text('[data-ci="edit-dashboard-title"] .ci-title-input', 'New test personal dashboard')
    safe_click('[data-ci="save-canvas-dashboard"]')
    wait_for_all_ajax_requests

    new_dashboard = Dashboard.find_by(title: 'New test personal dashboard')
    expect(new_dashboard.external_user_item.is_personal).to eq(true)
    # should remove the temporary creation node
    wait_expect(true) { page.has_no_css?('[data-hui-key="new-personal-dashboard"') }
    # navigate to new node and highlight it
    wait_expect(true) { page.has_css?("#{dashboard_node_selector(new_dashboard.id)} .bg-blue-50") }

    # edit dashboard
    safe_click('[data-ci="ci-edit-canvas-dashboard"]')

    safe_click('[data-ci="edit-dashboard-title"]')
    set_text('[data-ci="edit-dashboard-title"] .ci-title-input', 'Edit dashboard title')
    page.send_keys(:enter)
    safe_click('[data-ci="save-canvas-dashboard"]')
    wait_for_all_ajax_requests

    wait_expect('Edit dashboard title') { page.find(dashboard_node_selector(new_dashboard.id)).text }
    new_dashboard.reload
    expect(new_dashboard.title).to eq('Edit dashboard title')
  end

  it 'can delete dashboard' do
    org_dashboard
    visit embed_url

    dashboard_node_css = dashboard_node_selector(org_dashboard.id)
    wait_for_element_load(dashboard_node_css)
    page.find(dashboard_node_css).hover
    safe_click('[data-ci="more-embed-portal-node-action"]')
    safe_click('[data-ci="delete-embed-portal-node"]')
    safe_click('.ci-confirm-delete')
    expect(page).to have_css('[data-ci="embed-portal-loading-node"]')

    wait_for_all_ajax_requests
    expect(Dashboard.where(id: org_dashboard.id).exists?).to be(false)
  end

  context 'save visualization' do
    before do
      FeatureToggle.toggle_global(EmbedLink::FT_DATA_EXPLORE, true)
    end

    def select_field(model_name:, field_name:)
      wait_for_element_load('.ci-empty-field')
      select_h_select_option('.viz-section:nth-child(1) .ci-empty-field', value: "#{model_name}$!#{field_name}")
    end

    it 'can save visualization' do
      visit embed_url

      dataset_node_selector = "[data-hui-key=\"Dataset-#{dataset.id}\"]"
      safe_click(dataset_node_selector)
      sleep 0.5

      select_field(model_name: 'users', field_name: 'id')
      safe_click('[data-ci="save-embed-dataset-visualization"]')

      block_title = 'Save embed dataset visualization'
      page.find('[data-ci="save-embed-visualization-title"]').set(block_title)
      safe_click('[data-ci="save-embed-visualization-new-org-dashboard"]')
      safe_click('.hui-btn-primary-highlight')

      # save successfully and navigate to dashboard
      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="save-embed-visualization-navigate-to-dashboard"]')
      safe_click('[data-ci="save-embed-visualization-navigate-to-dashboard"]')

      sleep 0.5
      wait_for_all_ajax_requests
      expect(page.find('#block-v1 .dac-viz-block-label').text).to eq(block_title)

      # explore the new viz block and then save the new explore result
      page.find_by_id('block-v1').hover
      safe_click('[data-ci="explore-block-btn"]')
      select_field(model_name: 'users', field_name: 'full_name')
      safe_click('.ci-save-explore-results-btn')

      new_block_title = 'Save embed dataset visualization to existing dashboard'
      page.find('[data-ci="save-embed-visualization-title"]').set(block_title)
      safe_click('[data-ci="save-embed-visualization-existing-dashboard"]')

      select_h_select_option('[data-ci="embed-portal-dashboard-tree-select"]', label: "Dashboard #{block_title}")
      safe_click('.hui-btn-primary-highlight')

      wait_for_all_ajax_requests
      expect(page).to have_css('[data-ci="save-embed-visualization-navigate-to-dashboard"]')
    end
  end
end
