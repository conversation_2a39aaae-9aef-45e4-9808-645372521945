<template>
  <HModalProvider>
    <div
      class="h-dashboard-container h-full"
      :class="DAC_THEME_SCOPE_CSS_CLASS"
    >
      <router-view v-slot="{ Component }">
        <component :is="Component" />
      </router-view>
    </div>
  </HModalProvider>
</template>

<script setup lang="ts">
import { HModalProvider } from '@holistics/design-system';
import { DAC_THEME_SCOPE_CSS_CLASS } from '@holistics/aml-std';
import { FetchProductionDatasetService } from '@/modules/DataSets/services/fetchDatasetService';
import { User } from '@/core/plugins/user';
import { curry } from 'lodash';
import { createReportFromDataSet } from '@/modules/DataSets/utils';
import saveReportModal from '@/modules/DataModels/services/modals/saveReportModal.modal';
import { type SaveVisualizationFunc, useProvideDashboardConfigs } from '../composables/useDashboardConfigs';
import { FetchProductionDashboardService } from '../services/FetchProductionDashboardService';

const user = new User();

const saveVisualizationFunc: SaveVisualizationFunc = ({
  dataSet, vizSetting, generatedTitle, title, description,
}) => {
  const curriedCreate = curry(createReportFromDataSet);

  return saveReportModal(
    {
      title,
      generatedTitle,
      description: description || '',
    },
    curriedCreate({ vizSetting, dataSetId: dataSet.id, dataset: dataSet }),
    null,
    { mode: 'dataset' },
  );
};

useProvideDashboardConfigs({
  fetchDatasetService: new FetchProductionDatasetService(),
  fetchDashboardService: new FetchProductionDashboardService(),
  saveVisualizationFunc: user.isPublicUser ? undefined : saveVisualizationFunc,
  extraDetails: {
    projectId: undefined,
    inDevMode: false,
  },
});
</script>
