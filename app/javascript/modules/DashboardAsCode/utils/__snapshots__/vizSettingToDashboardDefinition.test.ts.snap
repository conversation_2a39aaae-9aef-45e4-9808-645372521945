// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`vizSettingToDashboardDefinition should create a dashboard definition with correct structure 1`] = `
{
  "blocks": [
    {
      "description": "test block description",
      "label": "test viz block",
      "settings": {
        "hide_controls": false,
        "hide_label": false,
      },
      "theme": undefined,
      "type": "VizBlock",
      "uname": "v1",
      "viz": {
        "dataset_id": 1,
        "theme": {},
        "viz_setting": {
          "adhoc_fields": [],
          "amql": {
            "adhoc_fields": [],
            "conditions": [],
            "filters": [],
          },
          "fields": {
            "table_fields": [
              {
                "format": {
                  "type": "number",
                },
                "path_hash": {
                  "field_name": "id",
                  "model_id": "public_viz_settings",
                },
                "type": "number",
                "uuid": "!fields[0]",
              },
              {
                "format": {
                  "type": "string",
                },
                "path_hash": {
                  "field_name": "source_type",
                  "model_id": "public_viz_settings",
                },
                "type": "text",
                "uuid": "!fields[1]",
              },
              {
                "format": {
                  "format": {
                    "pattern": "inherited",
                  },
                  "type": "number",
                },
                "path_hash": {
                  "field_name": "viz_type",
                  "model_id": "public_viz_settings",
                },
                "type": "number",
                "uuid": "!fields[2]",
              },
            ],
          },
          "filters": [],
          "format": {},
          "settings": {
            "aggregate_awareness": {
              "debug_comments": true,
              "enabled": true,
            },
            "aggregation": {
              "show_average": false,
              "show_total": false,
            },
            "conditional_formatting": [],
            "misc": {
              "pagination_size": 25,
              "row_height": "Single line",
              "row_limit": 5000,
              "show_row_number": true,
            },
            "others": {
              "include_empty_children_rows": false,
            },
          },
          "viz_type": undefined,
        },
      },
    },
  ],
  "description": "",
  "interactions": [],
  "settings": {
    "allow_timezone_change": false,
    "autorun": true,
  },
  "theme": {
    "_id": "H.themes.classic",
    "background": {
      "bg_color": "#FFFFFF",
      "bg_image": "none",
      "bg_repeat": "no-repeat",
      "bg_size": "cover",
    },
    "block": {
      "background": {
        "bg_color": "#FFFFFF",
        "bg_image": "none",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
      },
      "border": {
        "border_color": "#E8E8E8",
        "border_radius": {
          "bottom_left": "8px",
          "bottom_right": "8px",
          "top_left": "8px",
          "top_right": "8px",
        },
        "border_style": "solid",
        "border_width": {
          "bottom": "1px",
          "left": "1px",
          "right": "1px",
          "top": "1px",
        },
      },
      "label": {
        "font_color": "#53586A",
        "font_family": "Inter",
        "font_size": "14px",
        "font_style": "normal",
        "font_weight": "medium",
      },
      "opacity": 1,
      "padding": {
        "bottom": "12px",
        "left": "12px",
        "right": "12px",
        "top": "12px",
      },
      "shadow": "none",
      "text": {
        "font_color": "#53586A",
        "font_family": "Inter",
        "font_size": "12px",
        "font_style": "normal",
        "font_weight": "normal",
      },
    },
    "canvas": {
      "background": {
        "bg_color": "#F7F8F9",
        "bg_image": "none",
        "bg_repeat": "no-repeat",
        "bg_size": "cover",
      },
      "border": {
        "border_color": "#E8E8E8",
        "border_radius": {
          "bottom_left": "4px",
          "bottom_right": "4px",
          "top_left": "4px",
          "top_right": "4px",
        },
        "border_style": "solid",
        "border_width": {
          "bottom": "1px",
          "left": "1px",
          "right": "1px",
          "top": "1px",
        },
      },
      "opacity": 1,
      "shadow": "none",
    },
    "title": "Classic",
  },
  "title": "new dashboard from viz block",
  "uname": "new_dashboard_from_viz_block",
  "views": [
    {
      "blocks": {
        "v1": {
          "layer": 0,
          "position": {
            "h": 300,
            "w": 400,
            "x": 20,
            "y": 20,
          },
        },
      },
      "grid_size": 20,
      "height": 840,
      "label": "View 1",
      "type": "CanvasLayout",
      "uname": "view_1",
      "width": 1200,
    },
  ],
}
`;
