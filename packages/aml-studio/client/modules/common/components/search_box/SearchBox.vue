<template>
  <HDropdown
    v-model:open="open"
    :options="dropdownOptions"
    trigger="click"
    placement="bottom-start"
    @select="onSelect"
  >
    <div class="border-1 flex items-center gap-2 rounded-md border border-gray-200 bg-white px-2 py-1 has-[:focus]:border-blue-300">
      <slot name="prepend-section" />

      <div class="relative flex flex-1 flex-row items-center">
        <HighlightableInput
          :model-value="inputValue"
          :highlight-regex="keywordsRegex"
          :highlight-class="'!bg-gray-200 !px-1 !py-1 !rounded-md'"
          :placeholder="placeholder"
          class="search-and-replace-input size-full min-w-[120px] overflow-hidden break-all border-0 border-none outline-none"
          @update:model-value="onInput"
          @keydown.stop="onKeyDown"
        />
      </div>
      <slot name="footer-section" />
    </div>
  </HDropdown>
</template>

<script setup lang="ts">
import {
  ref, watch, computed, type Ref,
} from 'vue';
import {
  isEmpty, concat, debounce, isUndefined,
} from 'lodash';
import {
  type DropdownOption, HDropdown,
} from '@holistics/design-system';
import {
  isAlphabetic, endsWithSpaceCharacter, buildRegexForMatchingKeywords, getLastWord,
} from '@aml-studio/client/utils/lang';
import { HighlightableInput } from '@aml-studio/h/components';
import { useSearchHistoryBrowsingState } from './useSearchHistoryBrowsingState';
import { Option } from './types';

interface Props {
  searchQuery: string;
  options: Option[];
  buildDropdownOptionFn: (options: Option) => DropdownOption;
  dropdownWidth: number;
  hoveredOptionClass?: string;
  highlightClass?: string
  isInitiallyOpenDropdown?: boolean;
  placeholder?: string;
  debounceTime?: number;
}

const props = withDefaults(defineProps<Props>(), {
  hoveredOptionClass: 'bg-gray-100',
  highlightClass: 'hui-text-highlight',
  isInitiallyOpenDropdown: true,
  debounceTime: 300,
  placeholder: 'Search...',
});

const open = ref(props.isInitiallyOpenDropdown ?? false);
const focusedOptionKey = ref<string | null>(null);
const inputValue = ref(props.searchQuery);

const emit = defineEmits<{
  'update:query': [string]
  'selectOption': [DropdownOption | undefined]
  'keyDown': [KeyboardEvent]
}>();

const onInput = debounce((text: string) => {
  emit('update:query', text);
}, props.debounceTime ?? 300);

const onSelect = (option: DropdownOption) => {
  if (option.type !== 'divider') {
    emit('selectOption', option);
  }
};

const currentTextSegment = computed(() => getLastWord(inputValue.value));

const highlightableOptions = ref(props.options.filter(o => o.highlightable));
const optionKeywordMap: Ref<{ option: Option, keyword: string }[]> = computed(() => highlightableOptions.value.map(o => ({
  option: o,
  keyword: o.keyword ?? '',
})));

const keywords = computed(() => optionKeywordMap.value.map(o => o.keyword));
const keywordsRegex: Ref<RegExp | undefined> = computed(() => {
  if (isEmpty(optionKeywordMap)) {
    return undefined;
  }

  return buildRegexForMatchingKeywords(keywords.value);
});

const matchedOptions: Ref<{ option: Option, keyword: string }[]> = computed(() => {
  if (isEmpty(currentTextSegment.value) || isEmpty(optionKeywordMap.value)) {
    return [];
  }

  return optionKeywordMap.value.filter(x => !isUndefined(x.keyword) && x.keyword.startsWith(currentTextSegment.value));
});

const hasMatchingKeyword = computed(() => !isEmpty(currentTextSegment.value) && matchedOptions.value.length > 0);

const shownOptions = computed(() => (
  hasMatchingKeyword.value
    ? matchedOptions.value.map(o => o.option)
    : props.options));

const dropdownOptions: Ref<DropdownOption[]> = computed(() => {
  return shownOptions.value
    .map(option => props.buildDropdownOptionFn(option))
    .map(option => {
      // Dropdown style when navigating using keyboard
      const klass = focusedOptionKey.value === option.key
        ? concat(option.class, props.hoveredOptionClass)
        : option.class;

      // Dropdown width
      const style = concat(option.style, `width: ${props.dropdownWidth}px;`);

      return {
        ...option,
        class: klass,
        style,
      } as DropdownOption;
    });
});

// Keyboard navigation on dropdown items

const {
  currentBrowsingIndex,
  increaseBrowsingIndex,
  decreaseBrowsingIndex,
  resetBrowsingIndex,
} = useSearchHistoryBrowsingState(dropdownOptions);

const onKeyDown = (e: KeyboardEvent) => {
  emit('keyDown', e);

  if (!open.value) {
    return;
  }

  if (e.key === 'ArrowDown') {
    increaseBrowsingIndex();
    // @ts-ignore
    focusedOptionKey.value = dropdownOptions.value[currentBrowsingIndex.value]?.key;
  } else if (e.key === 'ArrowUp') {
    decreaseBrowsingIndex();
    // @ts-ignore
    focusedOptionKey.value = dropdownOptions.value[currentBrowsingIndex.value]?.key;
  } else if (e.key === 'Enter') {
    if (focusedOptionKey.value === null) {
      return;
    }

    const option = dropdownOptions.value.find(opt => 'key' in opt && opt.key === focusedOptionKey.value);
    if (option) {
      onSelect(option);
    }
  } else if (e.key === 'Escape') {
    open.value = false;
  } else if (isAlphabetic(e.key)) {
    open.value = false;
  }
};

watch(open, (newValue) => {
  if (!newValue) {
    resetBrowsingIndex();
  }
});

watch(inputValue, (newValue) => {
  if (keywords.value.some(keyword => newValue.trim().endsWith(keyword))) {
    open.value = false;
    return;
  }

  open.value = isEmpty(newValue)
    || endsWithSpaceCharacter(newValue)
    || hasMatchingKeyword.value;
});

watch(() => props.searchQuery, (newQuery) => {
  inputValue.value = newQuery;
});

const inputRef = ref<HTMLInputElement | null>(null);

const focus = () => {
  inputRef.value?.focus();
};

defineExpose({
  focus,
});
</script>

<style lang="postcss" scoped>
.search-and-replace-input:empty::before {
    @apply content-['Search...'] text-gray-400;
  }

  .search-and-replace-input:focus::before {
    @apply hidden;
  }
</style>
