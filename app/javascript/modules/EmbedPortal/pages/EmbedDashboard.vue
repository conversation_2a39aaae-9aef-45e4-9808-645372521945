<template>
  <div
    class="embed-portal-dashboard relative size-full"
  >
    <HButton
      type="secondary-default"
      size="sm"
      class="absolute left-2 top-2 z-[1]"
      icon="sidebar-light"
      unified
      @click="emit('toggleExpand')"
    />
    <CanvasDashboardEmbed
      :key="dashboardId"
      data-ci="embed-portal-dashboard"
      :hide-embed-warning="true"
    />
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import CanvasDashboardEmbed from '@/modules/DashboardAsCode/pages/DashboardEmbed.vue';
import { computed, defineAsyncComponent, defineEmits } from 'vue';
import { HButton, useModal } from '@holistics/design-system';
import { useEmbedPortalStore } from '@/modules/EmbedPortal/store/embedPortalStore';
import { type SaveVisualizationFunc, useProvideDashboardConfigs } from '@/modules/DashboardAsCode/composables/useDashboardConfigs';

const route = useRoute();
const dashboardId = computed(() => route.params.dashboard_id as string);

const embedPortalStore = useEmbedPortalStore();
const { open } = useModal();

const saveVisualizationFunc: SaveVisualizationFunc = async ({ dataSet, generatedTitle, vizSetting }) => {
  const modal = defineAsyncComponent(() => import('../components/EmbedSaveVisualizationModal.vue'));

  const { state } = await open(modal, { vizSetting, generatedTitle, dataset: dataSet });

  return { data: null, status: state };
};

useProvideDashboardConfigs({
  saveVisualizationFunc: embedPortalStore.hasEditPermission ? saveVisualizationFunc : undefined,
});

const emit = defineEmits<{(e: 'toggleExpand'): void;
}>();
</script>
<style lang="postcss" scoped>
.embed-portal-dashboard:deep(.dac-metadata) {
  @apply pl-10;
}
</style>
