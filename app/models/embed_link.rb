# typed: false

class EmbedLink < ApplicationRecord
  include TenantScope
  include FilterValuable
  include AddManyThrough

  INITIAL_NUM_EMBED_WORKERS = 2

  EMBED_TOKEN_PARAM = '_token'.freeze
  EMBED_TOKEN_LIST_VALUE_TYPE = 'EmbedToken'.freeze
  DEFAULT_CLIENT_ORG_ID = '_DEFAULT_ORG_'.freeze
  FT_ACCEPT_ANY_IDENTIFIER_VAR_VALUE = 'embed_link:accept_any_identifier_var_value'.freeze
  FT_DATA_EXPLORE = 'embed_link:data_explore'.freeze
  FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD = 'embed_link:use_portal_configs_for_dashboard_source'.freeze
  FT_USE_SINGLE_EMBED_KEY = 'embed_link:use_single_embed_key'.freeze
  FT_NEW_EMBED_ANALYTICS_UI = 'embed_link:new_embed_analytics_ui'.freeze
  FT_EMBED_PREVIEW_WORKER = 'embed_link:embed_preview_worker'.freeze
  FT_EMBED_DASHBOARD_USER_ATTRIBUTES = 'embed_link:embed_dashboard_user_attributes'.freeze

  belongs_to :user, class_name: 'User', foreign_key: 'owner_id'
  belongs_to :owner, class_name: 'User', foreign_key: 'owner_id'
  belongs_to :public_user, class_name: 'User', foreign_key: 'public_user_id', dependent: :destroy
  belongs_to :source, polymorphic: true

  has_many :embed_link_identifier_variables, class_name: 'EmbedLinkIdentifierVariable'
  has_many :identifier_filter_ownerships, through: :embed_link_identifier_variables, source: :filter_ownership

  after_initialize :set_hash_code
  before_save :set_secret_key
  before_save :validate_params

  # if user is creating the first embed_link, set the default number of reserved embed workers
  before_save :set_initial_embed_workers_for_tenant, if: :table_empty?

  def validate_params
    self.identifier_filter_ownerships.each do |filter_ownership|
      unless T.must(filter_ownership.shared_filter).settings[:type].in?(%w[dropdown input])
        raise Holistics::ValidationError, 'Filter type must be either dropdown or text input'
      end
    end
  end

  def add_filter_ownership_validation(filter_ownership)
    unless filter_ownership.shared_filter.settings[:type].in?(%w[dropdown input])
      raise Holistics::ValidationError, 'Filter type must be either dropdown or text input'
    end
  end

  def is_v3?
    version == 3
  end

  def v3_or_higher?
    [3, 4].include?(version)
  end

  def set_hash_code
    self.hash_code ||= SecureRandom.hex(12)
  end

  # Create random secret key
  def set_secret_key
    self.secret_key ||= SecureRandom.hex(64)
  end

  def set_public_user
    if self.public_user_id.nil?
      embed_user = User.create(
        name: 'public user',
        email: "embed_#{SecureRandom.hex(6)}@holistics.io",
        role: 'public',
        password: SecureRandom.hex(20),
        tenant: self.tenant,
      )
      self.public_user_id = embed_user.id
      self.save
    end
  end

  def update_public_user
    T.must(self.public_user).tenant = T.must(self.tenant)
    T.must(self.public_user).save
  end

  def share_source
    T.must(self.user).share(self.public_user, :read, self.source) if self.user
    if self.source_type == 'QueryReport'
      T.must(self.user).share(self.public_user, :bust_cache, self.source) if self.user
    end
  end

  def url
    "/embed/#{self.hash_code}?_token=<TOKEN>"
  end

  # Override params with FilterValues (super method), THEN override with identifier variables param
  def overridden_params(params, user)
    ParamTransformers::Embed::DecodeIdentifierVariables.new(embed_link: self).call(super)
                                                       .to_unsafe_h
                                                       .with_indifferent_access
  end

  def is_legacy?
    self.embed_link_identifier_variables.any? { |variable| variable.legacy_customer_filter == true }
  end

  def can_embed_object?(embed_object)
    return embed_object.project_id == source_id if source.is_a?(::AmlStudio::Project)

    source_id == embed_object.id
  end

  private

  def table_empty?
    !EmbedLink.exists?
  end

  def set_initial_embed_workers_for_tenant
    TenantSubscription.where(tenant_id: self.tenant_id).find_each do |ts|
      ts.update_embed_workers! INITIAL_NUM_EMBED_WORKERS
    end
  end
end
