operationId: AmlStudioProjects_FetchLatestEvent
tags:
  - Development
summary: Poll for project events
description: |
  Fetch events for an AML Studio project that occurred after the specified timestamp.
  This endpoint supports efficient polling of events by returning only new events since the last request.
parameters:
  - name: id
    in: path
    description: ID of the AML Studio project
    required: true
    schema:
      type: integer
  - name: branch_name
    in: query
    description: Git branch name to poll events for
    required: true
    schema:
      type: string
  - name: event_types
    in: query
    description: Filter events with given event types
    required: true
    allowEmptyValue: false
    schema:
      type: array
      items:
        type: string
        enum:
          - pull_request
          - deploy
responses:
  "200":
    description: Successfully retrieved events
    content:
      application/json:
        schema:
          type: object
          properties:
            events:
              type: array
              description: List of events that occurred since the last poll
              items:
                $ref: ../../../components/schemas/aml_studio/ProjectEvent.yml
  "403":
    $ref: ../../../components/responses/errors/PermissionDeniedError.yml
  "422":
    $ref: ../../../components/responses/errors/InvalidOperationError.yml
