# frozen_string_literal: true

# typed: false
require 'rails_helper'

describe 'View underlying data feature', js: true do
  include_context 'test_tenant'
  include_context 'data_set'

  before do
    FeatureToggle.toggle_global('viz_result:show_context_menu_on_data_table', true)
    FeatureToggle.toggle_global(Dashboard::FT_V4_CREATION, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_DASHBOARD_V4, true)
    FeatureToggle.toggle_global('drill_features:view_underlying_data', true)
    FeatureToggle.toggle_global('ag-grid:data-table', true)
    FeatureToggle.toggle_global('ag-grid:pivot-table', true)
    FeatureToggle.toggle_global('table:infinite_scroll', true)
    FeatureToggle.toggle_global('metric_kpi:new_renderer_for_v4', true)
    FeatureToggle.toggle_global('table:freeze_columns', true)
    FeatureToggle.toggle_global('table:hide_fields', true)
    FeatureToggle.toggle_global('table:auto_size', true)
    FeatureToggle.toggle_global('table:remove_columns', true)
    FeatureToggle.toggle_global('table:rename_columns', true)
    FeatureToggle.toggle_global('table:aggregate_columns', true)
    FeatureToggle.toggle_global('table:add_edit_calculations', true)
    FeatureToggle.toggle_global('table:add_new_columns', true)
    FeatureToggle.toggle_global('pop:enabled', true)
    FeatureToggle.toggle_global('pop:custom_period', true)
    FeatureToggle.toggle_global('interactive_control:pop', true)
    ThreadContext.set(:current_user, admin)

    connector = Connectors.from_ds(get_test_ds)

    # connector.exec_sql('truncate data_modeling.products')
    connector.exec_sql(
      <<~SQL
        INSERT INTO data_modeling.products(name, merchant_id, category_id, "Price", "Status", "Created At")
        ( VALUES
          ('bread', 1, 1, 2.25, 'available', '2019-09-10T00:00:00Z'),
          ('milk', 1, 1, 3, 'available', '2019-09-10T00:00:00Z'),
          ('egg', 1, 1, 5, 'available', '2019-09-10T00:00:00Z'),
          ('bread', 1, 1, 2.25, 'expired', '2019-09-10T00:00:00Z'),
          ('bread', 1, 1, 2.25, 'available', '2019-09-09T00:00:00Z'),
          ('milk', 1, 1, 3, 'available', '2019-09-09T00:00:00Z'),
          ('egg', 1, 1, 5, 'available', '2019-09-09T00:00:00Z'),
          ('bread', 1, 1, 2.25, 'expired', '2019-09-09T00:00:00Z')
        )
      SQL
    )
  end

  include_context 'aml_studio_deployed' do
    let(:isolated_repo) { true }
    let(:project_fixture_folder_path) do
      'spec/fixtures/aml_repos/drills'
    end
  end

  let(:dashboard) do
    deploy_result # make sure deployment is already done
    Dashboard.find_by!(uname: 'canvas_dashboard')
  end
  let(:dataset) do
    deploy_result # make sure deployment is already done
    DataSet.find_by!(uname: 'ecommerce')
  end
  let(:dashboard_url) { "/studio/projects/#{dashboard.project_id}/explore/dashboards/canvas_dashboard.page.aml" }

  def view_underlying_data(url, block_selector, current_user, &block)
    safe_login(current_user, url)
    wait_for_all_ajax_requests
    wait_for_element_load(block_selector.to_s)
    begin
      page.find('div[data-hui-comp="popper"] button', exact_text: 'Got it', wait: false).click
    rescue
      # do nothing
    end
    scroll_to_js(block_selector.to_s)
    viz = page.find(block_selector.to_s)
    data_point = yield(viz)
    data_point.right_click
    page.find('div', exact_text: 'View underlying data').click

    wait_for_element_load('div.view-underlying-data')
    page.find('div.view-underlying-data')
  end

  it 'works on table' do
    modal = view_underlying_data(dashboard_path(dashboard), '#block-table', admin) do |viz|
      find_cell_element(table_element: viz.find('[data-ci="ci-ag-grid-data-table"]'), row_id: '1', col_index: 4)
    end

    expect(modal.has_css?('div', exact_text: 'Underlying data of "Table"'))
    expect(modal.has_css?('div', text: 'where Name is'))
    expect(modal.find('div.underlying-data-viz .left-section').text).to match(/Sum of Price\n is \n (.|0-9)+\nwhere\nName\nis \"milk\"\nand\nStatus\nis \"available\"/)
    wait_for_element_load('div.underlying-data-viz .viz-data-table')
    expect(modal.has_css?('div.underlying-data-viz .viz-data-table'))
  end

  it 'works on pivot table' do
    modal = view_underlying_data(dashboard_path(dashboard), '#block-pivot_table', admin) do |viz|
      find_cell_element(table_element: viz.find('[data-ci="ci-ag-grid-pivot-table"]'), row_id: '0', col_index: 3)
    end

    expect(modal.has_css?('div', exact_text: 'Underlying data of "pivot"'))
    expect(modal.find('div.underlying-data-viz .left-section').text).to match(/Sum of Price\n is \n (.|0-9)+\nwhere\nName\nis "(bread|egg)"/)
    wait_for_element_load('div.underlying-data-viz .viz-data-table')
    expect(modal.has_css?('div.underlying-data-viz .viz-data-table'))
  end

  it 'works on line chart' do
    modal = view_underlying_data(dashboard_path(dashboard), '#block-combination_chart', admin) do |viz|
      viz.all('.highcharts-series .highcharts-point')[0]
    end

    expect(modal.has_css?('div', exact_text: 'Underlying data of "Sum of Price by Month Created at and Name"'))
    expect(modal.find('div.underlying-data-viz .left-section').text).to match(/Sum of Price\n is \n 10.25\nwhere\nCreated at\nmatches "(Jul|Aug) 2019"\nand\nStatus\nis \"available\"/)
    wait_for_element_load('div.underlying-data-viz .viz-data-table')
    expect(modal.has_css?('div.underlying-data-viz .viz-data-table'))
  end

  it 'works on pie chart' do
    modal = view_underlying_data(dashboard_url, '#block-pie_chart', admin) do |viz|
      cell = nil
      retry_until_success(retries: 3) do
        cell = viz.all('.highcharts-series .highcharts-point')[0]
        cell.right_click
      end

      cell
    end

    expect(modal.has_css?('div', exact_text: 'Underlying data of "Sum of Price by Month Created at and Name"'))
    expect(modal.find('div.underlying-data-viz .left-section').text).to match(/Count of Id\n is \n (.|0-9)+\nwhere\nName\nis \"(bread|egg)\"/)
    wait_for_element_load('div.underlying-data-viz .viz-data-table')
    expect(modal.has_css?('div.underlying-data-viz .viz-data-table'))
  end

  it 'works on metric kpi' do
    modal = view_underlying_data(dashboard_url, '#block-pie_chart', admin) do |viz|
      data_point = nil
      retry_until_success(retries: 3) do
        data_point = page.find('.h-kpi-metric-kpi').right_click
      end

      data_point
    end

    expect(modal.has_css?('div', exact_text: 'Underlying data of "metric kpi"'))
    expect(modal.find('div.underlying-data-viz .left-section').text).to match(/Sum of Price\n is \n (.|0-9)+/)
    wait_for_element_load('div.underlying-data-viz .viz-data-table')
    expect(modal.has_css?('div.underlying-data-viz .viz-data-table'))
  end

  context 'in production' do
    it 'works as URL' do
      modal = view_underlying_data(dashboard_path(dashboard), '#block-combination_chart', admin) do |viz|
        viz.all('.highcharts-series .highcharts-point')[0]
      end
      wait_for_all_ajax_requests
      wait_expect(false) do
        page.has_css?('a[data-ci="explore-from-here"][disabled="true"]')
      end
      expect(page.current_url).to match(/.+\/dashboards\/v4\/1-canvas-dashboard-4-0\?_vud=combination_chart&_eshash=.+/)
      modal.find('.h-icon[data-icon="cancel"]').click
      wait_expect(false) do
        page.has_css?('div.view-underlying-data')
      end
      page.go_back
      wait_for_element_load('div.view-underlying-data')
      new_modal = page.find('div.view-underlying-data')
      expect(modal.has_css?('div', exact_text: 'Underlying data of "Sum of Price by Month Created at and Name"'))
      expect(modal.find('div.underlying-data-viz .left-section').text).to match(/Sum of Price\n is \n 10.25\nwhere\nCreated at\nmatches "(Jul|Aug) 2019"\nand\nStatus\nis \"available\"/)
      wait_for_element_load('div.underlying-data-viz .viz-data-table')
      expect(modal.has_css?('div.underlying-data-viz .viz-data-table'))
    end

    it 'table experience should work' do
      def headers
        page.find('div.underlying-data-viz .viz-data-table').all('.ag-header-cell').map(&:text)
      end
      modal = view_underlying_data(dashboard_path(dashboard), '#block-combination_chart', admin) do |viz|
        viz.all('.highcharts-series .highcharts-point')[0]
      end
      wait_for_all_ajax_requests

      wait_for_element_load('div.underlying-data-viz .viz-data-table')
      table = page.find('div.underlying-data-viz .viz-data-table')
      expect(headers).to match(["", "Sum of Price", "Id", "Name", "Merchant Id", "Category Id", "Price", "Status", "Created at", ""])
      table.find('.ag-header-cell', exact_text: 'Merchant Id').right_click
      page.first('.h-table-header-dropdown .hui-popper-content div', exact_text: 'Remove column').click
      wait_for_all_holistics_loadings
      wait_for_all_ajax_requests
      table.find('.ag-header-cell', exact_text: 'Id').right_click
      page.first('.h-table-header-dropdown .hui-popper-content div', exact_text: 'Remove column').click
      wait_for_all_holistics_loadings
      wait_for_all_ajax_requests
      expect(headers).to match(["", "Sum of Price", "Name", "Category Id", "Price", "Status", "Created at", ""])

      table.find('.ag-header-cell', exact_text: 'Name').right_click
      page.first('.h-table-header-dropdown .hui-popper-content div', exact_text: 'Insert column right').click
      safe_click('[data-value="data_modeling_products$!id"]')
      wait_for_all_ajax_requests
      wait_for_all_holistics_loadings
      expect(headers).to match(["", "Sum of Price", "Name", "Category Id", "Price", "Status", "Created at", "", "Id"])

      table.find('.ag-header-cell', exact_text: 'Sum of Price').right_click
      page.first('.h-table-header-dropdown .hui-popper-content div', exact_text: 'Freeze up to this column').click
      wait_for_all_ajax_requests
      wait_for_all_holistics_loadings
      cell = find_cell_element(table_element: table.find('[data-ci="ci-ag-grid-data-table"]'), row_id: '0', col_index: 2)
      expect(cell[:class].include?('ag-cell-last-left-pinned')).to be true
    end

    it 'works in conversion funnel' do
      modal = view_underlying_data(dashboard_path(dashboard), '#block-conversion_funnel', admin) do |viz|
        point = viz.all('.highcharts-series .highcharts-point')[0]
        point.right_click

        expect(page.has_css?('div', exact_text: 'View underlying data')).to eq(true)

        scroll_to_js('.new-conversion-funnel .conversion-funnel-table')

        viz.all('tbody tr')[1].all('td')[1]
      end

      expect(modal.has_css?('div', exact_text: 'Underlying data of "Conversion Funnel"'))
      expect(modal.find('div.underlying-data-viz .left-section').text).to match(/Sum of Price\n is \n (.|0-9)+\nwhere\nStatus\nis \"(available|expired)\"/)
    end

    it 'should disable on period comparison' do
      modal = view_underlying_data(dashboard_path(dashboard), '#block-bar_chart', admin) do |viz|
        point = viz.all('.highcharts-series')[0].all('.highcharts-point')[0]
        point.right_click

        expect(page.has_css?('div.\!cursor-not-allowed', text: 'View underlying data')).to eq(true)

        point = viz.all('.highcharts-series')[1].all('.highcharts-point')[1]
        point.right_click
      end

      expect(modal.has_css?('div', exact_text: 'Underlying data of "Count of Status by Month Created at"'))
      expect(modal.find('div.underlying-data-viz .left-section').text).to match(/Count of Status\n is \n (.|0-9)+/)
      wait_for_element_load('div.underlying-data-viz .viz-data-table')
      expect(modal.has_css?('div.underlying-data-viz .viz-data-table'))
    end

    it 'works in gauge chart' do
      modal = view_underlying_data(dashboard_path(dashboard), '#block-gauge_chart', admin) do |viz|
        viz.all('.highcharts-series')[0].all('.highcharts-point')[0]
      end

      expect(modal.has_css?('div', exact_text: 'Underlying data of "Gauge Chart"'))
      expect(modal.find('div.underlying-data-viz .left-section').text).to match(/Count of Id\n is \n (.|0-9)+/)
    end
  end
end