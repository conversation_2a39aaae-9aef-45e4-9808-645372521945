import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import { useRoute } from 'vue-router';
import type { DashboardDefinition, DashboardUpdateEvent, DatasetWithModels } from '@holistics/aml-std';
import { isEqual } from 'lodash';
import { createEmbedUserDashboard, deleteCanvasDashboards, updateCanvasDashboard } from '@/modules/DashboardAsCode/services/dashboards.ajax';
import { EmbedDashboard, EmbedPortalConfig } from '../types';
import { fetchEmbedPortal as _fetchEmbedPortal } from '../services/embedPortal';

export const useEmbedPortalStore = defineStore('embedPortal', () => {
  const route = useRoute();
  const { hashcode } = route.params as { hashcode: string };
  const secretToken = route.query._token as string;

  const embedPortal = ref<EmbedPortalConfig>();
  const isFetchingEmbedPortal = ref(false);
  const fetchEmbedPortalError = ref();

  const fetchEmbedPortal = async () => {
    isFetchingEmbedPortal.value = true;
    const { data, success, error } = await _fetchEmbedPortal(hashcode, secretToken);
    isFetchingEmbedPortal.value = false;
    if (success) {
      embedPortal.value = data?.embedPortal;
    } else {
      fetchEmbedPortalError.value = error.data?.message;
    }
    return embedPortal.value;
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async function createEmbedDashboard ({ definition, datasets, isPersonal }: { definition: DashboardDefinition, datasets: DatasetWithModels[], isPersonal: boolean }) {
    const { id } = await createEmbedUserDashboard({ definition, datasets, isPersonal });

    const workspace = isPersonal ? embedPortal.value?.embedObjects.personalDashboards : embedPortal.value?.embedObjects.orgDashboards;

    const embedDashboard: EmbedDashboard = {
      id,
      type: 'Dashboard',
      title: definition.title,
      uname: definition.uname,
    };

    workspace?.push(embedDashboard);

    return embedDashboard;
  }

  function findEmbedDashboardInWorkspace (id: number) {
    const { embedObjects } = embedPortal.value as EmbedPortalConfig;

    const orgDashboardIndex = (embedObjects.orgDashboards || []).findIndex(d => d.id === id);
    if (orgDashboardIndex !== -1) {
      return { workspace: embedObjects.orgDashboards, index: orgDashboardIndex };
    }

    const personalDashboardIndex = (embedObjects.personalDashboards || []).findIndex(d => d.id === id);
    if (personalDashboardIndex !== -1) {
      return { workspace: embedObjects.personalDashboards, index: personalDashboardIndex };
    }

    return { workspace: undefined, index: -1 };
  }

  async function updateEmbedDashboard ({
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    id, events, repoVersion, definition, datasets,
  } : { id: number, events: DashboardUpdateEvent[], repoVersion?: string, definition: DashboardDefinition, datasets: DatasetWithModels[] }) {
    // TODO: handle failed to update dashboard later
    await updateCanvasDashboard(id, { dashboardUpdateEvents: events, datasets, repoVersion });

    const embedDashboard: EmbedDashboard = {
      id,
      type: 'Dashboard',
      title: definition.title,
      uname: definition.uname,
    };

    const { workspace, index } = findEmbedDashboardInWorkspace(id);
    if (workspace && !isEqual(embedDashboard, workspace[index])) {
      workspace[index] = embedDashboard;
    }

    return embedDashboard;
  }

  async function deleteEmbedDashboard (id: number) {
    await deleteCanvasDashboards({ ids: [id] });

    const { workspace, index } = findEmbedDashboardInWorkspace(id);
    if (workspace && index !== -1) {
      workspace.splice(index, 1);
    }
  }

  const hasEditPermission = computed(() => {
    const actionBasedPermission = embedPortal.value?.actionBasedPermission;

    return actionBasedPermission && (actionBasedPermission.orgWorkspace.canEdit || actionBasedPermission.personalWorkspace.canEdit);
  });

  return {
    hashcode,
    secretToken,
    embedPortal,
    isFetchingEmbedPortal,
    fetchEmbedPortalError,
    createEmbedDashboard,
    updateEmbedDashboard,
    deleteEmbedDashboard,
    fetchEmbedPortal,
    hasEditPermission,
  };
});
