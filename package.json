{"name": "holistics", "private": true, "description": "Holistics", "workspaces": ["packages/*", "app/javascript"], "scripts": {"api-docs:build": "yarn run openapi-cli --mode=lint,bundle", "api-docs:build:3-0": "yarn run openapi-cli --mode=lint,bundle --openapi=openapi/openapi_3_0.yml --out=dist/openapi/3_0", "api-docs:dev": "yarn run openapi-cli --mode=lint,bundle,preview --watch", "ci:yarn:install": "npm_config_nodegit_binary_host_mirror=\"$npm_package_config_nodegit_binary_host_mirror\" .circleci/h_yarn_install.sh", "create:package": "npx plop package", "e2e": "playwright test --config=e2e/playwright.config.ts", "preinstall": "npx only-allow yarn", "postinstall": "patch-package && ./bin/check_aml_packages.sh", "lint-check:holistics-app": "eslint ./app/javascript -c .eslintrc.js --quiet", "lint-check:packages": "eslint ./packages -c .eslintrc.js --quiet", "test:unit": "TZ=\"Asia/Kuala_Lumpur\" jest", "test:unit:coverage": "TZ=\"Asia/Kuala_Lumpur\" jest --coverage", "yarn:install": "npm_config_nodegit_binary_host_mirror=\"$npm_package_config_nodegit_binary_host_mirror\" yarn install"}, "config": {"nodegit_binary_host_mirror": "http://cdn.holistics.io/libs"}, "resolutions": {"esbuild": "^0.25.0", "loader-utils": "2.0.4", "semver": "^7.5.2"}, "dependencies": {"@holistics/aml-editor": "2.16.0-alpha.5", "pinia": "^2.2.8", "puppeteer": "24.6.1", "puppeteer-experimental": "npm:puppeteer", "vue": "^3.5.13"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/plugin-proposal-class-properties": "^7.17.12", "@babel/plugin-proposal-decorators": "^7.23.5", "@babel/plugin-proposal-object-rest-spread": "^7.18.9", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.16.11", "@esbuild-plugins/node-modules-polyfill": "^0.1.4", "@faker-js/faker": "^8.1.0", "@holistics/configs": "0.2.3", "@holistics/eslint-plugin": "^0.0.5", "@holistics/openapi-cli": "^2.5.0", "@pinia/testing": "^0.1.2", "@playwright/test": "^1.52.0", "@puppeteer/browsers": "^2.2.0", "@sentry/vite-plugin": "^3.4.0", "@size-limit/file": "^8.1.0", "@tailwindcss/typography": "^0.5.8", "@types/chroma-js": "^2.4.2", "@types/git-rev-sync": "^2.0.0", "@types/jest": "^27.4.1", "@types/leaflet": "^1.7.9", "@types/lodash": "^4.14.182", "@types/node": "^22.14.1", "@types/rosie": "^0.0.40", "@typescript-eslint/parser": "^5.46.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-typescript": "^11.0.2", "@vue/test-utils": "^2.4.1", "@vue/vue3-jest": "^29.2.4", "autoprefixer": "^10.4.16", "babel-jest": "^27.5.1", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-transform-import-meta": "^2.2.0", "babel-plugin-transform-require-context": "^0.1.1", "babel-preset-typescript-vue3": "^2.0.17", "chalk": "^4.1.2", "chokidar": "^3.5.3", "confusing-browser-globals": "^1.0.11", "danger": "^12.3.1", "danger-plugin-istanbul-coverage": "^1.6.2", "danger-plugin-yarn": "^1.6.0", "eslint": "^8.56.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "npm:eslint-plugin-i", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-json-format": "^2.0.1", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-vue": "^9.32.0", "eslint-plugin-yaml": "^0.5.0", "flush-promises": "^1.0.2", "git-rev-sync": "^3.0.2", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "patch-package": "^7.0.2", "postcss": "^8.4.31", "postcss-flexbugs-fixes": "^5.0.2", "postcss-import": "^15.1.0", "postcss-preset-env": "^7.8.2", "postinstall-postinstall": "^2.1.0", "rosie": "^2.1.0", "sass": "^1.64.2", "size-limit": "^8.1.0", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.2", "vite": "^5.4.19", "vite-plugin-ruby": "^5.1.1", "zod": "^3.24.2"}, "optionalDependencies": {"dependency-cruiser": "^16.8.0"}, "engines": {"node": "^18.0.0"}}