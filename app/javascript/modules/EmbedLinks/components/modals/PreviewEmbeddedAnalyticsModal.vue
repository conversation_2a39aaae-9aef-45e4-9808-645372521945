<template>
  <h-modal
    v-h-loading.body="isFetching"
    title="Embedded Analytics Sandbox"
    no-footer
    class="preview-embedded-analytics-modal ci-data-exploration-modal flex flex-col overflow-hidden"
    @dismiss="closeModal"
  >
    <template #description>
      <span>
        Generate the Embed Code and preview your Simulated Dashboard.
      </span>
      <a
        :href="docsUrl"
        target="_blank"
      >
        Read more
      </a>
    </template>
    <div class="settings-and-preview flex min-h-full w-full min-w-0 flex-col">
      <div class="embed-builder-and-preview flex flex-row overflow-hidden">
        <EmbedBuilder
          v-model="embedConfigs"
          class="h-full"
          :conditions="conditions"
          :filters="filters"
          :user-attributes-payload="embedConfigs.user_attributes"
          :ui-schemes="filterUiSchemes"
          :source="dashboardSource"
          :allow-hide-control-panel="!isCanvasDashboard"
          :allow-hide-control="!isCanvasDashboard"
          :embed-exploration-enabled="embedExplorationEnabled"
          :allow-dashboard-export-setting="allowDashboardExportSetting"
        />
        <EmbedPreviewTabs
          class="flex-1"
          :embed-link="embedLink"
          :disable-run="isIframeLoading"
          :target-url="targetUrl"
          :embed-builder="embedConfigsBySource"
          @run="run"
        />
      </div>
      <div class="mt-6 flex min-h-36 min-w-0 flex-1 flex-col">
        <div class="text-xs font-medium">
          Embedded Dashboard Preview
        </div>
        <div
          v-if="iframeSource"
          class="embedded-dashboard border"
        >
          <iframe
            ref="iframe"
            :src="iframeSource"
            class="embedded-dashboard-iframe h-[800px] w-full"
            frameborder="0"
            allowfullscreen
            @load="onLoad"
          />
        </div>
        <div
          v-else
          class="mt-2 flex min-h-0 min-w-0 flex-1 flex-col items-center justify-center border"
        >
          <img
            src="https://cdn.holistics.io/assets/chart.svg"
          >
          <p class="mt-3 text-gray-500">
            Click <span class="font-medium text-gray-900">Run button</span> to view your simulated dashboard
          </p>
        </div>
      </div>
    </div>
  </h-modal>
</template>

<script setup lang="ts">
import {
  computed, onMounted, ref, watch, type Ref,
} from 'vue';
import EmbedBuilder from '@/modules/EmbedLinks/components/EmbedBuilder.vue';
import { submitFetchFilters } from '@/modules/DynamicFilters/services/dynamicFilters.ajax';
import EmbedPreviewTabs from '@/modules/EmbedLinks/components/EmbedPreviewTabs.vue';
import JsonWebToken from '@/es6/json_web_token';
import Utils from '@/es6/utils';
import generateDocsLink from '@/modules/FeatureToggleGroup/utils/generateDocsLink';
import { createDefaultCondition } from '@/modules/Viz/submodules/VizFilters/utils';
import { CRITICAL_TTL_MS, EXP_CHECK_INTERVAL_MS, REFRESH_TOKEN_POST_MESSAGE_TYPE } from '@/modules/EmbedLinks/constants/embedToken';
import { forEach, isEmpty, pick } from 'lodash';
import getDashboardTimezoneRegion from '@/modules/DynamicDashboards/services/getDashboardTimezoneRegion';
import { getDashboardTimezone as getCanvasDashboardTz } from '@/modules/DashboardAsCode/composables/useDashboardTimezone';
import type { Dashboard } from '@/modules/DynamicDashboards/utils/types';
import type { DashboardAsCode } from '@/modules/DashboardAsCode/types';
import { fetchDashboard } from '@/modules/DashboardAsCode/services/dashboards.ajax';
import { handleAjaxError } from '@/core/services/ajax';
import type { DataDeliverySource } from '@/modules/DataDelivery/types';
import type DynamicFilterUiScheme from '@/modules/DynamicFilters/models/DynamicFilterUiScheme';
import type DynamicFilter from '@/modules/DynamicFilters/models/DynamicFilter';
import type { Condition } from '@holistics/types';
import { checkFeatureToggle } from '@aml-studio/h/services';
import type { ActionBased, EmbedConfigs, EmbedLink } from '../../types';
import { FT_EMBED_ENABLE_DASHBOARD_EXPORT_SETTING } from '../../constants/embedFeatureToggles';

const emit = defineEmits<{(e: 'dismiss'): void}>();
const props = defineProps<{
  embedLink: EmbedLink
  source: DataDeliverySource
}>();

const dashboard = ref<Dashboard | DashboardAsCode>();
const docsUrl = generateDocsLink('/docs/embedded-analytics');
const isFetching = ref(false);
const iframeSource = ref<string>();
const checkEmbedTokenIntervalId = ref<ReturnType<typeof setInterval>>();

const isCanvasDashboard = computed(() => {
  return props.source.sourceType === 'Dashboard' && dashboard.value?.version === 4;
});
const tokenExpiration = ref();
const embedExplorationEnabled = checkFeatureToggle('embed_link:data_explore') && props.source.sourceType === 'Dashboard' && props.source.data.version === 4;
const defaultActionBased: ActionBased | undefined = embedExplorationEnabled
  ? { explore: { datasets: [] } }
  : undefined;

const allowDashboardExportSetting = computed(() => isCanvasDashboard.value
&& checkFeatureToggle(FT_EMBED_ENABLE_DASHBOARD_EXPORT_SETTING));

const embedConfigs = ref<EmbedConfigs>({
  settings: {
    enable_export_data: false,
    default_timezone: null,
    allow_to_change_timezone: false,
    enable_dashboard_export: false,
    hide_header_panel: false,
    hide_control_panel: false,
  },
  permissions: {
    row_based: [],
    action_based: defaultActionBased,
  },
  filters: {},
  user_attributes: {},
});
watch(() => allowDashboardExportSetting.value, (isDashboardExportAllowed) => {
  // remove enable_dashboard_export if isDashboardExportAllowed is false
  if (isDashboardExportAllowed) {
    embedConfigs.value.settings.enable_dashboard_export = false;
  } else {
    delete embedConfigs.value.settings.enable_dashboard_export;
  }
}, { immediate: true });

const isIframeLoading = ref(false);
const filterUiSchemes = ref<Record<string, DynamicFilterUiScheme>>({});
const conditions = ref<Record<string, Condition | Record<string, unknown>>>({});
const filters = ref<DynamicFilter[]>([]);
const targetUrl = `${Utils.baseUrl()}/embed`;
const iframe: Ref<HTMLIFrameElement | null> = ref(null);
const secretKey = computed(() => {
  return props.embedLink.secret_key;
});

const embeddedDashboardTimezone = computed(() => {
  if (!dashboard.value) {
    return null;
  }
  if (dashboard.value.version === 4) {
    return getCanvasDashboardTz(dashboard.value);
  }
  return getDashboardTimezoneRegion(dashboard.value);
});

const dashboardSource = computed(() => {
  return {
    source_id: props.source.data.id,
    source_type: props.source.sourceType,
    timezone: embeddedDashboardTimezone.value,
    version: props.source.data.version,
  };
});

const embedConfigsBySource = computed(() => {
  if (isCanvasDashboard.value) {
    // remove hidden filters settings
    if (embedConfigs.value && embedConfigs.value.filters) {
      Object.keys(embedConfigs.value.filters).forEach((filterKey) => {
        if (embedConfigs.value.filters[filterKey]) {
          delete embedConfigs.value.filters[filterKey].hidden;
        }
      });
    }

    return {
      settings: pick(embedConfigs.value.settings, [
        'enable_export_data',
        'default_timezone',
        'allow_to_change_timezone',
        ...(allowDashboardExportSetting.value ? ['enable_dashboard_export'] : []),
        'hide_header_panel',
      ]),
      permissions: embedConfigs.value.permissions,
      filters: embedConfigs.value.filters,
      user_attributes: embedConfigs.value.user_attributes,
    };
  }
  return embedConfigs.value;
});

function initEmbedFilterConfigs (dynamicFilters: DynamicFilter[]) {
  return dynamicFilters.reduce((result, filter) => {
    result[filter.uname] = {
      hidden: false,
      default_condition: {
        ...filter.definition.default_condition,
      },
    };
    return result;
  }, {} as Record<string, any>);
}

function handleFilterUiSchemes (uiSchemes: DynamicFilterUiScheme[]) {
  forEach(uiSchemes, value => {
    filterUiSchemes.value[value.id] = value;
    if (!value.error) {
      conditions.value[value.id] = value.default_condition || {};
    }
  });
}

async function fetchFilters () {
  const { dynamicFilters, filterUiSchemes: uiSchemes } = await submitFetchFilters('Dashboard', props.source.data.id);
  filters.value = dynamicFilters.map((filter: DynamicFilter) => {
    if (isEmpty(filter.definition.default_condition)) {
      const { filter_type: fieldType } = filter.definition;
      filter.definition.default_condition = createDefaultCondition(fieldType);
    }

    return filter;
  });
  embedConfigs.value.filters = initEmbedFilterConfigs(filters.value);
  handleFilterUiSchemes(uiSchemes);
}

function getTokenExpFromDuration (durationInSecond: number) {
  return new Date().getTime() / 1000 + durationInSecond;
}

async function generateToken () {
  const payload = {
    settings: embedConfigs.value.settings,
    permissions: embedConfigs.value.permissions,
    filters: embedConfigs.value.filters,
    user_attributes: embedConfigs.value.user_attributes,
    // @ts-ignore
    exp: window.H.env !== 'test' ? getTokenExpFromDuration(60 * 5) : getTokenExpFromDuration(3),
  };
  tokenExpiration.value = payload.exp;
  return JsonWebToken.sign(payload, secretKey.value);
}

async function refreshToken () {
  const token = await generateToken();
  if (iframe.value) {
    iframe.value.contentWindow?.postMessage({ type: REFRESH_TOKEN_POST_MESSAGE_TYPE, token }, targetUrl);
  }
}

async function checkExpAndRefreshToken () {
  const currentTime = Date.now();
  const expiration = tokenExpiration.value * 1000;

  const timeUntilExpiration = expiration - currentTime;
  if (timeUntilExpiration <= CRITICAL_TTL_MS) {
    await refreshToken();
  }
}

function clearCheckEmbedTokenInterval () {
  if (checkEmbedTokenIntervalId.value) {
    clearInterval(checkEmbedTokenIntervalId.value);
    checkEmbedTokenIntervalId.value = undefined;
  }
}

async function run () {
  clearCheckEmbedTokenInterval();
  isIframeLoading.value = true;
  const token = await generateToken();
  iframeSource.value = `${targetUrl}/${props.embedLink.hash_code}?_token=${token}`;
  checkEmbedTokenIntervalId.value = setInterval(() => checkExpAndRefreshToken(), EXP_CHECK_INTERVAL_MS);
}

function onLoad () {
  isIframeLoading.value = false;
}

function closeModal () {
  emit('dismiss');
  clearCheckEmbedTokenInterval();
}

onMounted(async () => {
  try {
    isFetching.value = true;
    ({ dashboard: dashboard.value } = await fetchDashboard(props.source?.data?.id));
    await fetchFilters();
  } catch (error) {
    handleAjaxError(error);
  } finally {
    isFetching.value = false;
  }
});
</script>

<style lang="postcss" scoped>
  .preview-embedded-analytics-modal {
    border-radius: 5px;
    height: calc(100vh - 42px);

    .settings-and-preview {
      .embed-builder-and-preview {
        height: 540px;
        flex-shrink: 0;
      }
    }
  }
</style>
<style lang="postcss">
  .preview-embedded-analytics-modal.h-modal .h-modal-body {
    flex: 1;
    flex-direction: column;
    overflow: auto;
  }
</style>
