Model vendors {
  type: 'query'
  label: 'Vendors'
  description: ''
  data_source_name: 'pg'
  dimension id {
    label: 'Vendor Id'
    type: 'number'
    hidden: false
    definition: @sql {{ #SOURCE.id }};;
  }
  dimension name {
    label: 'Vendor Name'
    type: 'text'
    hidden: false
    definition: @sql {{ #SOURCE.name }};;
  }

  owner: '<EMAIL>'
  query: @sql
    with vendors(
      id,
      name
    ) as (
      values
        (1, 'Vendor A'),
        (2, 'Vendor B')
    )
    select
      id,
      name
    from
      vendors
  ;;

  models: []
}
