# typed: true

module Canal
  module Cache
    class EntryKey < T::Struct
      const :physical_id, Integer
      const :logical_key, String
      const :tenant_id, Integer

      sig { returns(String).checked(:tests) }
      def physical_key
        "#{Rails.env}/#{tenant_id}/#{physical_id}"
      end
    end

    EntryColumn = T.type_alias do
      {
        'original_col_name' => String,
        'original_col_type' => String,
        'cache_col_name' => String,
        'cache_col_type' => String,
        'cache_arrow_type' => T.nilable(String), # old entries and exec_on_cache_to_new_cache don't have this
      }
    end

    class Entry < T::Struct
      const :entry_key, Cache::EntryKey
      const :user_id, T.nilable(Integer)
      const :num_rows, Integer
      const :columns, T::Array[EntryColumn]
      const :extra_info, T::Hash[String, T.untyped]
      const :queryable_location, String
      const :saved_at, Time
      const :expired_at, Time

      class << self
        extend T::Sig

        sig { params(record: T::Hash[String, T.untyped]).returns(Entry) }
        def from_physical_record(record)
          Entry.new(
            entry_key: EntryKey.new(
              physical_id: record['id'],
              logical_key: record['logical_key'],
              tenant_id: record['tenant_id'],
            ),
            user_id: record['user_id'],
            num_rows: record['num_rows'],
            columns: Oj.load(record['columns'], mode: :json),
            queryable_location: record['queryable_location'],
            extra_info: Oj.load(record['extra_info'], mode: :json),
            saved_at: record['saved_at'].utc,
            expired_at: record['expired_at'].utc,
          )
        end
      end

      sig { params(with_indifferent_access: T::Boolean).returns(HashWithIndifferentAccess).checked(:tests) }
      def metadata(with_indifferent_access: true)
        metadata = extra_info.merge(
          {
            'columns' => columns,
            'saved_at' => saved_at,
            'expired_at' => expired_at,
            'num_rows' => num_rows,
            'tenant_id' => tenant_id,
          },
        )

        metadata = metadata.with_indifferent_access if with_indifferent_access

        metadata
      end

      sig { returns(String).checked(:tests) }
      def logical_key
        entry_key.logical_key
      end

      sig { returns(String).checked(:tests) }
      def physical_key
        entry_key.physical_key
      end

      sig { returns(Integer).checked(:tests) }
      def tenant_id
        entry_key.tenant_id
      end

      sig { returns(String).checked(:tests) }
      def sql_table_name
        if columns.empty?
          # duckdb cannot query parquet files that have 0 columns
          '(select 1)'
        else
          queryable_location
        end
      end
    end

    class QueryError < Holistics::AppError
      extend T::Sig

      OriginalError = T.type_alias do
        T.any(
          Arrow::Error::Invalid,
          Arrow::Error::Unknown,
        )
      end

      sig { returns(OriginalError) }
      attr_reader :original

      sig { params(original: OriginalError, message: String).void }
      def initialize(original, message)
        @original = original
        super(message)
      end
    end

    class << self
      extend T::Sig

      sig do
        params(
          logical_key: String,
          tenant_id: Integer,
          user_id: T.nilable(Integer),
          metadata: Hash,
        ).returns(EntryKey).checked(:tests)
      end
      def init_physical_entry(logical_key, tenant_id:, user_id:, metadata: {})
        physical_id = connection.exec_query(
          <<~SQL,
            INSERT INTO cache_physical_entries(logical_key, tenant_id, user_id, extra_info, saved_at, expired_at)
            VALUES($1, $2, $3, $4, $5, $5)
            RETURNING id
          SQL
          'insert_cache_physical_entries',
          [
            logical_key,
            tenant_id,
            user_id,
            Oj.dump(metadata, mode: :json),
            Time.now.utc.iso8601(6), # should be updated properly later
          ],
        )[0]['id']

        EntryKey.new(
          logical_key: logical_key,
          physical_id: physical_id,
          tenant_id: tenant_id,
        )
      end

      sig do
        params(
          entry_key: EntryKey,
          cache_duration_s: Integer,
          num_rows: Integer,
          columns: T::Array[EntryColumn],
          queryable_location: String,
        ).returns(Entry).checked(:tests)
      end
      def commit_entry(entry_key, cache_duration_s:, num_rows:, columns:, queryable_location:)
        now = Time.now.utc
        now_iso8601 = now.iso8601(6)

        connection.transaction(requires_new: true, joinable: false) do
          physical_record = connection.exec_query(
            <<~SQL,
              UPDATE cache_physical_entries
              SET
                num_rows = $1,
                columns = $2,
                queryable_location = $3,
                saved_at = $4,
                expired_at = $5
              WHERE id = $6
              RETURNING *
            SQL
            'commit_cache_physical_entries',
            [
              num_rows,
              Oj.dump(columns, mode: :json),
              queryable_location,
              now_iso8601,
              (now + cache_duration_s.seconds).iso8601(6),
              entry_key.physical_id,
            ],
          )[0]

          entry = Entry.from_physical_record(physical_record)

          # NOTE: assuming tenant_id of a logical entry never changes
          connection.exec_query(
            <<~SQL,
              WITH inserted AS (
                -- Update logical entry to point to latest physical entry
                INSERT INTO cache_logical_entries as le (key, physical_id, tenant_id, created_at, updated_at)
                VALUES($1, $2, $3, $4, $5)
                ON CONFLICT (key) DO UPDATE
                SET
                  physical_id = GREATEST(le.physical_id, EXCLUDED.physical_id),
                  tenant_id = EXCLUDED.tenant_id,
                  updated_at = EXCLUDED.updated_at
                RETURNING physical_id
              )
              -- Expire other (orphan) physical entries
              UPDATE cache_physical_entries pe
              SET expired_at = $4
              FROM inserted
              WHERE pe.logical_key = $1
                AND pe.id <> inserted.physical_id
                AND pe.expired_at > $4
            SQL
            'commit_cache_logical_entries',
            [
              entry_key.logical_key,
              entry_key.physical_id,
              entry.tenant_id,
              now_iso8601,
              now_iso8601,
            ],
          )

          entry
        end
      end

      sig { params(logical_key: String).returns(T::Boolean) }
      def exist(logical_key)
        connection.exec_query(
          <<~SQL,
            SELECT EXISTS(
              SELECT 1
              FROM cache_logical_entries le
                LEFT JOIN cache_physical_entries pe
                  ON le.physical_id = pe.id
              WHERE le.key = $1
                AND pe.expired_at >= $2
            ) existed
          SQL
          'exist_cache_entry',
          [
            logical_key,
            Time.now.utc.iso8601(6),
          ],
        )[0]['existed']
      end

      # Use this to fetch the latest cache entry
      sig { params(logical_key: T.nilable(String)).returns(T.nilable(Entry)) }
      def fetch_entry(logical_key)
        return unless logical_key

        record = connection.exec_query(
          <<~SQL,
            SELECT pe.*
            FROM cache_logical_entries le
              LEFT JOIN cache_physical_entries pe
                ON le.physical_id = pe.id
            WHERE le.key = $1
              AND pe.expired_at >= $2
          SQL
          'fetch_cache_entry',
          [
            logical_key,
            Time.now.utc.iso8601(6),
          ],
        )[0]
        return unless record

        Entry.from_physical_record(record)
      end

      # Use this to fetch the exact physical cache entry
      sig { params(physical_id: T.nilable(Integer)).returns(T.nilable(Entry)) }
      def fetch_entry_by_id(physical_id)
        return unless physical_id

        record = connection.exec_query(
          <<~SQL,
            SELECT pe.*
            FROM cache_physical_entries pe
            WHERE pe.id = $1
              AND pe.expired_at >= $2
          SQL
          'fetch_cache_entry_by_id',
          [
            physical_id,
            Time.now.utc.iso8601(6),
          ],
        )[0]
        return unless record

        Entry.from_physical_record(record)
      end

      sig { params(queryable_location: String).void }
      def expire_by_location(queryable_location)
        connection.exec_query(
          <<~SQL,
            UPDATE cache_physical_entries pe
            SET expired_at = $1
            WHERE pe.queryable_location = $2
          SQL
          'expire_by_location',
          [
            # 1.second.ago so that it is also effective to subsequent requests that are also in the same current second
            1.second.ago.utc.iso8601(6),
            queryable_location,
          ],
        )
      end

      sig do
        params(
          prefix: String,
          before: Time,
          batch_size: Integer,
        ).returns(Integer)
      end
      def expire_by_key(prefix:, before:, batch_size:)
        res = connection.exec_query(
          <<~SQL,
            WITH target_keys AS (
              SELECT logical_key
              FROM cache_physical_entries
              WHERE
                logical_key LIKE '#{prefix}%'
                AND saved_at < $1
                AND expired_at > $1
              LIMIT $2
            ), updated as (
              UPDATE cache_physical_entries
              SET expired_at = $1
              WHERE logical_key IN (SELECT logical_key from target_keys)
            )
            SELECT COUNT(*) count FROM target_keys
          SQL
          'expire_by_key',
          [
            # -1.second so that it is also effective to subsequent requests that are also in the same current second
            T.cast(before - 1.second, Time).utc.iso8601(6),
            batch_size,
          ],
        )
        res[0]['count']
      end

      sig { params(entry: Entry).returns(Modeling::Models::DataModel).checked(:tests) }
      def model(entry)
        table_name = entry.sql_table_name
        columns = entry.columns
        field_formats = entry.extra_info['field_formats']

        table_model = Modeling::Models::TableModel.new(
          name: "cache_t#{entry.entry_key.physical_id}",
          label: '',
          table_name: table_name,
          data_source: data_source,
          dimensions: columns.map do |col|
            type = cache_col_type_to_h_type(col['cache_col_type'])
            Modeling::Field.new(
              name: cache_col_name(
                col['original_col_name'],
                # NOTE: why don't we escape in sql_generation instead: because we only need to sanitize the original column names, not all column names
                # Otherwise, always doing this in sql_generation would add unnecessary processing
                escape: true,
              ),
              type: type,
              sql: cache_col_sql(col['cache_col_name'], type),
              format: field_formats&.dig(col['original_col_name']),
            )
          end.presence || empty_model_fields(entry),
        )

        # Here we are basically doing table_model.explore_model.select('*')
        # except that we construct field.sql using Operation instead of string so that it works with any column names
        model = table_model.explore_model
        Modeling::Models::DataModel.merge(
          model,
          dimensions: model.dimensions.map do |f|
            f.merge(
              # TODO: do this in sql_generation instead
              sql: SqlGeneration::Values::SqlStructures::Operation.new(
                operator: :Field,
                operands: [table_model.reference.to_s, f.name],
              ),
            )
          end,
        ).select('*')
      end

      # NOTE: currently, only adhoc_query flows use this method.
      # New flows should use #model instead
      sig { params(entry: Entry).returns(DataModel).checked(:tests) }
      def db_model(entry)
        ActiveRecord::Base.transaction(requires_new: true, joinable: false) do
          backend = CanalLakeModel.create!(
            tenant_id: entry.tenant_id,
            cache_logical_key: entry.logical_key,
            cache_physical_id: entry.entry_key.physical_id,
          )

          dm = DataModel.create!(
            tenant_id: backend.tenant_id,
            backend: backend,
            name: "cache_t#{entry.entry_key.physical_id}",
            fields: entry.columns.map do |col|
              type = cache_col_type_to_h_type(col['cache_col_type'])
              DataModeling::Field.new(
                name: cache_col_name(
                  col['original_col_name'],
                  # NOTE: field names should be normalized later by DataModel (ActiveRecord) hooks -> no need to escape here
                  escape: false,
                ),
                label: col['original_col_name'],
                type: type,
                sql: cache_col_sql(col['cache_col_name'], type),
                dbtype: DataSource::DBTYPE_CANAL_LAKE,
              )
            end.presence || empty_db_model_fields(entry),
            category_id: 0,
            owner_id: entry.user_id,
          )
          dm.set_defaults
          dm
        end
      end

      sig { params(entry: Entry).returns(T::Array[Modeling::Field]).checked(:tests) }
      def empty_model_fields(entry)
        [
          Modeling::Field.new(
            name: '_',
            type: 'text',
            sql:
              if entry.extra_info['non_select_query']
                "'Non-select query has been executed successfully'"
              else
                "'Empty result'"
              end,
          ),
        ]
      end

      sig { params(entry: Entry).returns(T::Array[DataModeling::Field]).checked(:tests) }
      def empty_db_model_fields(entry)
        [
          DataModeling::Field.new(
            name: 'status',
            label: '_',
            type: 'text',
            sql:
              if entry.extra_info['non_select_query']
                "'Non-select query has been executed successfully'"
              else
                "'Empty result'"
              end,
            dbtype: DataSource::DBTYPE_CANAL_LAKE,
          ),
        ]
      end

      sig { params(entry: Entry).returns([Array, T::Array[Array]]).checked(:tests) }
      def fetch_data(entry)
        model = self.model(entry)
        Queries::Services::ExecuteSql.new(data_source: db_data_source).call(model.to_sql)
      end

      sig { returns(Modeling::Values::DataSource).checked(:tests) }
      def data_source
        Modeling::Values::DataSource.new(
          name: Constants::DISPLAY_NAME.parameterize(separator: '_'),
          dbtype: DataSource::DBTYPE_CANAL_LAKE,
        )
      end

      sig { returns(DataSource).checked(:tests) }
      def db_data_source
        ds = DataSource.new(
          name: Constants::DISPLAY_NAME.parameterize(separator: '_'),
          dbtype: DataSource::DBTYPE_CANAL_LAKE,
          dbconfig: {
            host: T.must(ENV.fetch(Constants::ENV_LAKE_HOST, '127.0.0.1').presence),
            port: T.must(ENV.fetch(Constants::ENV_LAKE_PORT, '1324').presence),
          },
          settings: {},
        )
        ds.readonly!
        ds
      end

      # Methods for testing/debugging
      # Do not use these for application runtime because they are not efficient
      unless Rails.env.production?
        sig { returns(Integer).checked(:tests) }
        def count_logical_entries
          connection.exec_query(
            'SELECT COUNT(*) c FROM cache_logical_entries',
          )[0]['c']
        end

        sig { returns(Integer).checked(:tests) }
        def count_physical_entries
          connection.exec_query(
            'SELECT COUNT(*) c FROM cache_physical_entries',
          )[0]['c']
        end

        sig { returns(T::Array[Hash]).checked(:tests) }
        def all_logical_entries
          connection.exec_query(
            'SELECT * FROM cache_logical_entries ORDER BY id',
          ).to_a
        end

        sig { returns(T::Array[Hash]).checked(:tests) }
        def all_physical_entries
          connection.exec_query(
            'SELECT * FROM cache_physical_entries ORDER BY id',
          ).to_a
        end

        sig { returns(T::Array[Hash]).checked(:tests) }
        def unexpired_physical_entries
          connection.exec_query(
            'SELECT * FROM cache_physical_entries WHERE expired_at > $1 ORDER BY id',
            'unexpired_physical_entries',
            [
              Time.now.utc.iso8601(6),
            ],
          ).to_a
        end
      end

      private

      def connection
        ActiveRecord::Base.connection
      end

      sig { params(cache_col_type: String).returns(String).checked(:tests) }
      def cache_col_type_to_h_type(cache_col_type)
        PostgresCache::Connector.data_class_for_cache_type(cache_col_type)
      end

      sig { params(cache_col_name: String, h_type: String).returns(String).checked(:tests) }
      def cache_col_sql(cache_col_name, h_type)
        # NOTE: why don't we do this in sql_generation instead: because we only need to sanitize the original column names
        # Otherwise, always doing this in sql_generation would add unnecessary processing
        sanitized_col_name = PG::Connection.quote_ident(cache_col_name)[1...-1]

        sql = "{{ #SOURCE.#{sanitized_col_name} }}"

        case h_type
        when 'datetime'
          # timestamps, after our timezone processing, are stored as strings
          # -> need to cast them for further processing
          "CAST(#{sql} AS timestamp)"
        when 'duration'
          # intervals are stored as strings
          "CAST(#{sql} AS interval)"
        when 'text'
          # Some data types, while having pgcache type 'text', are being stored as more primitive data types.
          # E.g. binary, time
          # -> Need to explicitly cast them to VARCHAR so that:
          #   * They can be processed using Holistics text operations
          #   * They are encoded into readable strings (e.g. binary is encoded as hexadecimal string)
          "CAST(#{sql} AS VARCHAR)"
        else
          # for other types, the data should already be stored in proper types and ready for further processing
          sql
        end
      end

      sig { params(original_col_name: String, escape: T::Boolean).returns(String).checked(:tests) }
      def cache_col_name(original_col_name, escape:)
        if original_col_name.blank?
          # some dbs like sqlserver may return columns with empty name
          # such empty names would break querying on cache because for example, `select "hcol0" as ""` is invalid
          # so we turn them into a space character
          original_col_name = ' '
        end
        if escape
          # escape special characters
          PG::Connection.quote_ident(original_col_name)[1...-1]
        else
          original_col_name
        end
      end
    end
  end
end
