<template>
  <UserAttributeValuesInput
    v-for="(attributeValues, attributeName) of modelValue"
    :key="attributeName"
    class="!mt-2"
    :attribute-name="attributeName"
    :attribute-type="userAttributes[attributeName].attribute_type"
    :values="attributeValues"
    :is-required="false"
    @update-value="values => updatePayloadUserAttribute(attributeName, values)"
    @delete="deletePayloadUserAttribute(attributeName)"
  />

  <!-- ADD ATTRIBUTE BUTTON -->
  <HDropdown
    class="mt-4"
    content-class="w-[200px]"
    :options="unsetUserAttributeOptions"
    @select="a => updatePayloadUserAttribute((a as DropdownOptionDefault).key, [])"
  >
    <HButton
      type="secondary-default"
      icon="add"
      size="md"
    >
      Add user attribute
    </HButton>
  </HDropdown>
</template>

<script setup lang="ts">
import { AttributeValuesType } from '@/modules/EmbedPortal/types';
import UserAttribute from '@/modules/UserAttributes/models/UserAttribute';
import { computed, onMounted, ref } from 'vue';
import * as UserAttributes from '@/modules/UserAttributes/services/userAttribute.ajax';
import {
  difference, get, keyBy, keys,
  omit,
} from 'lodash';
import {
  DropdownOption, DropdownOptionDefault, HDropdown, IconName, HButton,
} from '@holistics/design-system';
import { CORE_TYPES_MAP } from '@/modules/DataModels/constants';
import { useToasts } from '@/core/composables/useToasts';
import UserAttributeValuesInput from './EmbedPortal/PayloadBuilder/UserAttributes/UserAttributeValuesInput.vue';

const props = defineProps<{
  modelValue: Record<string, AttributeValuesType>
}>();

const emit = defineEmits<{
  'update:modelValue': [modelValue: Record<string, AttributeValuesType>],
}>();

const userAttributes = ref<Record<string, UserAttribute>>({});

const updatePayloadUserAttribute = (attributeName: string, attributeValues: AttributeValuesType) => {
  emit('update:modelValue', {
    ...props.modelValue,
    [attributeName]: attributeValues,
  });
};

const deletePayloadUserAttribute = (attributeName: string) => {
  emit('update:modelValue', omit(props.modelValue, attributeName));
};

const { toast } = useToasts();

onMounted(async () => {
  try {
    const res = await UserAttributes.fetchAll();
    userAttributes.value = keyBy(res, 'name');
  } catch {
    toast.danger("Can't fetch user attributes");
  }
});

const unsetUserAttributeNames = computed(() => {
  return difference(keys(userAttributes.value), keys(props.modelValue));
});

const unsetUserAttributeOptions = computed<DropdownOption[]>(() => {
  return unsetUserAttributeNames.value.map(name => ({
    key: name,
    label: name,
    icon: get(CORE_TYPES_MAP, [userAttributes.value[name].attribute_type, 'icon'], 'unknown') as IconName,
  }));
});

</script>
