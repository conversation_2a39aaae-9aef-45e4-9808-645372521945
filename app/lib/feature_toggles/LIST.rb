# typed: true

# @folder-tag #internal_control/feature_toggles
module FeatureToggles
  # rubocop:disable Layout/LineLength
  LIST_FOR_TESTS = [
    *5.times.map do |i|
      { key: "test#{i}", notes: 'for specs', default_toggle_mode: 'disabled' }
    end,
  ].freeze

  LIST = [
    { key: 'test', notes: 'for testing', default_toggle_mode: 'disabled' },
    { key: 'gleap', notes: 'enable gleap: https://www.gleap.io/', default_toggle_mode: 'disabled' },
    { key: 'activity_logs:index', notes: 'Allow fetching activity logs', default_toggle_mode: 'enabled' },
    { key: 'adhoc_queries:keep_viz_setting', notes: '', default_toggle_mode: 'disabled' },
    { key: 'adhoc:use_gzip', notes: "- enabled: Use Ruby's gzip\n- disabled: Use Linux's native gzip", default_toggle_mode: 'disabled' },
    { key: 'api:deprecate_sync_endpoint', notes: '', default_toggle_mode: 'disabled' },
    { key: 'autocomplete', notes: '', default_toggle_mode: 'disabled' },
    { key: 'billings:plans_v3', notes: 'Exclude active tenants that have no plan. This is the legacy case when their plans are only stored at Zoho', default_toggle_mode: 'disabled' },
    { key: 'billing_v2', notes: '', default_toggle_mode: 'disabled' },
    { key: 'billing:charge_new_subscription_immediately', notes: 'Charge new subscription immediately regardless trial days left', default_toggle_mode: 'disabled' },
    { key: 'billing:charge_explorer', notes: 'Charge explorer usage', default_toggle_mode: 'disabled' },
    { key: 'bulk_actions', notes: 'Allow to use bulk actions e.g. move, delete, copy', default_toggle_mode: 'disabled' },
    { key: 'chart_annotations', notes: 'Showing annotations in charts', default_toggle_mode: 'enabled' },
    { key: 'commenting', notes: 'Allow users to comment on reports/dashboards', default_toggle_mode: 'disabled' },
    { key: 'csv_attachments:parameterize_csv_headers', notes: '', default_toggle_mode: 'disabled' },
    { key: 'csv_encoding:support_utf8_bom', notes: 'Exported CSV file will append BOM prefix in order for Microsoft Excel to show properly Unicode UTF-8', default_toggle_mode: 'disabled' },
    { key: 'csv_export:raw_headers', notes: '', default_toggle_mode: 'disabled' },
    { key: 'data_manager', notes: '', default_toggle_mode: 'enabled' },
    { key: 'data_models:data_model_panel', notes: '', default_toggle_mode: 'disabled' },
    { key: 'data_models:explore_controls', notes: '', default_toggle_mode: 'disabled' },
    { key: 'data_models:manager', notes: '', default_toggle_mode: 'disabled' },
    { key: 'data_models:table_custom_label', notes: '', default_toggle_mode: 'disabled' },
    { key: 'data_models:show_aml_tab', notes: 'Show AML tab in data model details page', default_toggle_mode: 'disabled' },
    { key: 'data_models:new_sql_generation', notes: 'New three-phase SQL generation logic', default_toggle_mode: 'enabled' },
    { key: 'data_models:sql_generation_gem', notes: 'Use the new SQL generation gem', default_toggle_mode: 'disabled' },
    { key: 'data_models:sql_generation_gem_on_single_model', notes: 'Use the new SQL generation gem when exploring single Data Model', default_toggle_mode: 'disabled' },
    { key: DataModeling::SqlGenConverters::ConvertDataModel::USE_QUERY_CACHE_FROM_DB_MODEL, notes: 'Use the SQL cache from the old db model', default_toggle_mode: 'disabled' },
    { key: 'data_models:use_viz_type_for_pgcache_model', notes: 'prioritize viz setting type over pgcache model guess type', default_toggle_mode: 'disabled' },
    { key: 'data_schedules:test_execution', notes: '', default_toggle_mode: 'enabled' },
    { key: 'data_source:enable_schema_info', notes: '', default_toggle_mode: 'disabled' },
    { key: QueryRunner::FT_ALWAYS_GET_TYPES, notes: 'Always get column types of query runner result', default_toggle_mode: 'disabled' },
    { key: 'data_sources:automated_reverse_tunnel', notes: '', default_toggle_mode: 'enabled' },
    { key: 'data_sources:custom_field', notes: '', default_toggle_mode: 'disabled' },
    { key: 'data_sources:google_analytics', notes: '', default_toggle_mode: 'enabled' },
    { key: 'debug_import_hif', notes: '', default_toggle_mode: 'disabled' },
    { key: 'disable_googlemap', notes: '', default_toggle_mode: 'disabled' },
    { key: 'dsl_connector', notes: '', default_toggle_mode: 'disabled' },
    { key: 'email_schedule:attachment_password', notes: '', default_toggle_mode: 'enabled' },
    { key: 'email_schedule:cron', notes: '', default_toggle_mode: 'disabled' },
    { key: 'email_schedule:fresh_report_data', notes: 'Allow email schedules to bypass cache', default_toggle_mode: 'disabled' },
    { key: 'embed_link:allow_public_user_bust_cache', notes: '', default_toggle_mode: 'disabled' },
    { key: 'embed_link:accept_any_identifier_var_value', notes: 'https://app.asana.com/0/76997687380943/1132425491343055/f', default_toggle_mode: 'disabled' },
    { key: 'enable_dedup_format_export', notes: '', default_toggle_mode: 'disabled' },
    { key: 'enabled_redshift_date_data_fix', notes: '', default_toggle_mode: 'disabled' },
    { key: 'exportings:debug_renderer_input', notes: '', default_toggle_mode: 'disabled' },
    { key: 'exportings:pdf', notes: '', default_toggle_mode: 'enabled' },
    { key: 'exportings:use_application_css', notes: '', default_toggle_mode: 'disabled' },
    { key: 'exportings:include_holistics_logo', notes: 'Include Holistics logo in exported images', default_toggle_mode: 'enabled' },
    { key: 'exportings:pdf_export_all_table_rows', notes: 'Export all table rows for data table, pivot table and retention heatmap when exporting PDF', default_toggle_mode: 'disabled' },
    {
      key: ImageExporters::PuppeteerRunner::FT_APPLY_CSP,
      notes: 'Apply CSP rules in Puppeteer exportings',
      default_toggle_mode: 'enabled',
    },
    {
      key: ImageExporters::PuppeteerRunner::FT_BLOCK_IFRAME,
      notes: 'Block iframes in Puppeteer exportings',
      default_toggle_mode: 'disabled',
    },
    {
      key: ImageExporters::PuppeteerRunner::FT_EXPERIMENTAL_VERSION,
      notes: 'Run experimental version of puppeteer',
      default_toggle_mode: Rails.env.development? ? 'enabled' : 'disabled',
    },
    { key: 'exports:secure_bucket', notes: 'Enable to store exported files in Holistics private S3 bucket, otherwise exported files will be stored in public bucket', default_toggle_mode: 'disabled' },
    { key: 'exports:tenant_s3_bucket', notes: '', default_toggle_mode: 'disabled' },
    { key: 'fast_excel_export', notes: 'Use fast Exel gem to generate Excel output', default_toggle_mode: 'enabled' },
    { key: 'feature_wall:export_with_tenant_s3_bucket', notes: '', default_toggle_mode: 'disabled' },
    { key: 'feature_wall:ip_whitelist', notes: '', default_toggle_mode: 'disabled' },
    { key: 'filterable:restrict_parent_child_filters', notes: 'https://holistics.zendesk.com/agent/tickets/1471', default_toggle_mode: 'disabled' },
    { key: 'filters:pg_cache', notes: '', default_toggle_mode: 'disabled' },
    { key: 'fullstory:track_tenant', notes: '', default_toggle_mode: 'disabled' },
    { key: 'fullstory:track_trial', notes: '', default_toggle_mode: 'enabled' },
    { key: 'google_sheet_source_with_sheet_api', notes: '', default_toggle_mode: 'enabled' },
    { key: 'gsheet_schedule:fresh_report_data', notes: '', default_toggle_mode: 'enabled' },
    { key: 'integrations:sftp', notes: '', default_toggle_mode: 'enabled' },
    { key: 'integrations:slack', notes: '', default_toggle_mode: 'enabled' },
    { key: 'jobs:pending_jobs_redis_cache', notes: '', default_toggle_mode: 'enabled' },
    { key: 'mailgun_webhook.notify_owner', notes: '', default_toggle_mode: 'enabled' },
    { key: 'metric_sheets_render_color', notes: '', default_toggle_mode: 'disabled' },
    { key: 'mongo_source:no_cursor_timeout', notes: '', default_toggle_mode: 'disabled' },
    { key: 'mysql:ssl_cipher', notes: '', default_toggle_mode: 'disabled' },
    { key: 'new_ability', notes: '', default_toggle_mode: 'enabled' },
    { key: 'one_click_import', notes: '', default_toggle_mode: 'disabled' },
    { key: 'perm:disable_share_all', notes: '', default_toggle_mode: 'disabled' },
    { key: 'pg_cache:cast_on_write', notes: '', default_toggle_mode: 'disabled' },
    { key: 'piwik_tracking', notes: '', default_toggle_mode: 'enabled' },
    { key: 'presto:do_not_drop_previous_tables_of_view', notes: '', default_toggle_mode: 'enabled' },
    { key: 'public_users:drilldowns', notes: '', default_toggle_mode: 'enabled' },
    { key: 'report:conversion_enabled', notes: '', default_toggle_mode: 'disabled' },
    { key: 'report:direct_export', notes: 'Direct Export feature inside report', default_toggle_mode: 'enabled' },
    { key: 'report:excel_export_disabled', notes: '', default_toggle_mode: 'disabled' },
    { key: 'report:version_history', notes: '', default_toggle_mode: 'enabled' },
    { key: 'report_widget:dedup_job', notes: '', default_toggle_mode: 'enabled' },
    { key: 'sample_data:stop_cloning_data_models', notes: '', default_toggle_mode: 'enabled' },
    { key: 'schema_explorer:enable_refresh', notes: '', default_toggle_mode: 'disabled' },
    { key: 'schema_synchronization', notes: '', default_toggle_mode: 'enabled' },
    { key: 'search:folder', notes: 'Allow users to search for folders as well as reports/dashboards', default_toggle_mode: 'enabled' },
    { key: 'settings:disable_change_login_mechanism', notes: '', default_toggle_mode: 'disabled' },
    { key: 'shareable_link:enabled', notes: 'Allow shareable links access', default_toggle_mode: 'disabled' },
    { key: 'shareable_link:password', notes: '', default_toggle_mode: 'disabled' },
    { key: 'slack_schedule:fresh_report_data', notes: '', default_toggle_mode: 'disabled' },
    { key: 'slack_schedules:dashboard', notes: '', default_toggle_mode: 'disabled' },
    { key: 'slack:unfurls', notes: '', default_toggle_mode: 'disabled' },
    { key: 'special_event:cheer_u23_vn', notes: 'Viet Nam vo dich!', default_toggle_mode: 'disabled' },
    { key: 'special_event:let_it_snow', notes: 'Merry Christmas everyone!', default_toggle_mode: 'disabled' },
    { key: 'special_event:lunar_new_year', notes: '', default_toggle_mode: 'disabled' },
    { key: 'special_login', notes: '', default_toggle_mode: 'disabled' },
    { key: 'tenant:ip_whitelist', notes: 'Allow IP whitelisting feature', default_toggle_mode: 'disabled' },
    { key: 'transform:presto_hotswap_using_view', notes: '', default_toggle_mode: 'disabled' },
    { key: 'ui_ux:friendly_loading', notes: '', default_toggle_mode: 'enabled' },
    { key: 'use_new_viz_exporter_to_render_data_tables', notes: 'whether to use new viz exporter (which applies VizSetting) to render report preview tables, excel tables', default_toggle_mode: 'enabled' },
    { key: 'use_new_viz_exporter_to_export_csv', notes: 'whether to use new viz exporter (which applies VizSetting) to export report csv for tenant < 3.0 (not dataset based report)', default_toggle_mode: 'disabled' },
    { key: 'viz_settings:reset_on_model_change', notes: '', default_toggle_mode: 'disabled' },
    { key: 'transform:drop_tmp_table_presto', notes: 'Data Transform in PrestoDB will use ALTER RENAME TABLE instead of using VIEW', default_toggle_mode: 'disabled' },
    { key: 'data_models:custom_field', notes: '', default_toggle_mode: 'disabled' },
    { key: 'data_models:custom_column', notes: 'Allow user to add custom column when creating 3rd-party data model', default_toggle_mode: 'disabled' },
    { key: 'data_sets:enabled', notes: 'Show data set for user on the left sidebar', default_toggle_mode: 'disabled' },
    { key: 'data_models:persistence', notes: 'Allow persisting query model/model model1 to database table', default_toggle_mode: 'disabled' },
    { key: 'data_models:show_relationships', notes: 'Show relationships panel in data models view', default_toggle_mode: 'disabled' },
    { key: 'personal_workspace:enabled', notes: 'Show Personal Workspace and Shared With Me for user on the left sidebar', default_toggle_mode: 'disabled' },
    { key: 'api_v1:email_schedules', notes: 'Expose email schedule APIs', default_toggle_mode: 'disabled' },
    { key: 'billing:hard_restriction', notes: 'Billing: Hard restriction blocking when tenant has exceeed the number of usage objects', default_toggle_mode: 'disabled' },
    { key: 'reporting_nav:enabled', notes: 'Show reporting navigation bar and new homepage experience', default_toggle_mode: 'disabled' },
    { key: 'report:select_data_model', notes: 'Show data model tab in report', default_toggle_mode: 'disabled' },
    { key: 'email_schedule:include_holistics_logo_footer', notes: 'Include Holistics logo in schedules email', default_toggle_mode: 'enabled' },
    { key: 'filters:limit_rows', notes: 'Limit number of rows returned by SQL based filter', default_toggle_mode: 'disabled' },
    { key: 'postgres_cache:debug', notes: 'Whether to log PostgresCache debugging info', default_toggle_mode: 'disabled' },
    { key: 'tracking:gtm', notes: 'GTM integration', default_toggle_mode: 'disabled' },
    { key: 'filter_valuable:compute_overriding_filtervalues', notes: 'https://app.asana.com/0/1112409515421684/1127234203063838/f', default_toggle_mode: 'disabled' },
    { key: 'public_users:cache_params_without_token', notes: 'Cache controller response without the token params', default_toggle_mode: 'enabled' },
    { key: 'billing:cut_off_date_for_hard_restriction', notes: 'Cut-off date for hard restriction, could remove after 10 July 2019. https://app.asana.com/0/inbox/634410292653853/1128665656764149/1128681176267634', default_toggle_mode: 'disabled' },
    { key: 'qds:fetch_columns', notes: 'Enable fetching columns for Qds data sources', default_toggle_mode: 'disabled' },
    { key: 'data_schedule:failure_notif_for_slack_and_gsheet', notes: 'Enable sending failure notif for Slack and Gsheet schedules', default_toggle_mode: 'disabled' },
    { key: 'metric:hide', notes: 'Disable Metric and Metric Sheets page, UI all New Metric button', default_toggle_mode: 'disabled' },
    { key: 'new_dropdown:clean', notes: 'Remove New Metric, New adhoc_query, Data Import, Transform in New Button Dropdown', default_toggle_mode: 'disabled' },
    { key: 'header:remove_data_center', notes: 'Remove Data Center from Nav, move Data Manager into Tools', default_toggle_mode: 'disabled' },
    { key: 'data_manager:hide_import_and_transform', notes: 'Hide Data Imports and Data Transforms in Data Manager.', default_toggle_mode: 'disabled' },
    { key: 'billing:hide_in_app_pricing', notes: 'Hide In-app pricing in setting', default_toggle_mode: 'disabled' },
    { key: 'data_source_form:hide_unsupported_dm', notes: 'Hide unsupported data model db in data source form', default_toggle_mode: 'disabled' },
    { key: 'viz_result:show_context_menu_on_data_table', notes: 'Show context menu on data talbe', default_toggle_mode: 'disabled' },
    { key: 'viz_result:left_click_enabled', notes: 'Enable left click on viz result to show context menu', default_toggle_mode: 'disabled' },
    { key: 'explore:include_empty_children_rows', notes: 'Enable include empty children rows', default_toggle_mode: 'disabled' },
    { key: ::SharedFilter::FEATURE_TOGGLE_STANDARDIZE_SETTINGS, notes: 'Standardize the fields of settings column', default_toggle_mode: 'enabled' },
    { key: ::QueryReports::WriteToS3::FT_UNIQUE_FILE_BASED_ON_CACHE_ENTRY, notes: 'Whether to make Report S3 exported files unique based on cache entry. Otherwise, the files will be unique based on cache key only', default_toggle_mode: 'disabled' },
    { key: ::User::FT_DISABLE_EXPORT_FOR_BIZ_USER, notes: 'Disable export for business users', default_toggle_mode: 'disabled' },
    { key: ::ShareableLink::FT_DISABLE_EXPORT, notes: 'Disable export for shareable links', default_toggle_mode: 'disabled' },
    { key: 'filters:unique_values_in_dropdown_filters', notes: 'Hide duplicated values in dropdown filters', default_toggle_mode: 'disabled' },
    { key: ::DataSource::ZENDESK_DATA_IMPORT_FT, notes: 'Enable to use Zendesk Data Import', default_toggle_mode: 'disabled' },
    { key: ::User::FT_EXPLORER_USER, notes: 'Enable user role explorer', default_toggle_mode: 'disabled' },
    { key: ::Tenant::FT_WHITELABELED, notes: 'Enable whitelabeling for tenant', default_toggle_mode: 'disabled' },
    { key: ::Connectors::MongoConnector::FT_SUPPORT_ATLAS_CONNECTIONS, notes: 'Support MongoDB Atlas connections', default_toggle_mode: 'enabled' },
    { key: 'hif_reader:skip_non_unicode', notes: 'If cell can not parsed, we will show it as (not displayable)', default_toggle_mode: 'disabled' },
    { key: ::DataSet::FT_CUSTOM_EXPRESSION, notes: 'Allow creating custom expression in DataSet', default_toggle_mode: 'disabled' },
    { key: ::Connectors::BigqueryConnector::FT_FETCH_ALL_COLUMNS_BY_QUERY, notes: 'A new approach to fetch all columns using INFORMATION_SCHEMA views', default_toggle_mode: 'disabled' },
    { key: ::DataImport::FT_INSERT_DATA_SAME_COLUMNS_ORDER_WITH_HIF, notes: 'Generated query to Insert data from hif has explicitly columns https://app.asana.com/0/634410292653855/980254822489390/f', default_toggle_mode: 'enabled' },
    { key: ::ShareableLink::FT_STRICT_MODE, notes: 'Only enable shareable link to admin and require password for each link', default_toggle_mode: 'disabled' },
    { key: 'shareable_link:ui_enabled', notes: 'Hide shareable link tools, preferences on FE.', default_toggle_mode: 'enabled' },
    { key: ::QueryReports::Visualization::FT_EXPORT_LEGACY_VIZ_WITH_PPTR, notes: 'Export legacy viz with puppeteer', default_toggle_mode: 'enabled' },
    { key: ::DataModels::Query::Parser::FT_KEEP_ONLY_REFERENCED_FIELDS, notes: 'An option to optimize DM query parser', default_toggle_mode: 'enabled' },
    { key: ::Dashboard::FT_V1_CREATION, notes: 'Whether to allow creating Dashboard v1', default_toggle_mode: 'disabled' },
    { key: ::Dashboard::FT_DASHBOARD_CONSUMPTION, notes: 'Track and display dashboard consumption stats. DO NOT toggle this FT directly. Please request an engineer\'s assistance in executing the designated toggling script for correct setup.', default_toggle_mode: 'enabled' },
    { key: ::Dashboard::FT_V3_CREATION, notes: 'Whether to allow creating Dashboard v3', default_toggle_mode: 'enabled' },
    { key: ::Dashboard::FT_V4_CREATION, notes: 'Whether to allow creating Dashboard v4 (as-code)', default_toggle_mode: 'disabled' },
    { key: ::Dashboard::FT_V4_CODEGEN, notes: 'Whether to allow generating AML code for dashboard v3 (for migrating to v4)', default_toggle_mode: 'disabled' },
    { key: ::Dashboard::FT_V4_AI_CODEGEN, notes: 'AI canvas dashboard generator', default_toggle_mode: 'disabled' },
    { key: ::Dashboard::FT_V3_CONVERSION, notes: 'Allow converting old dashboards to v3 and vice versa', default_toggle_mode: 'disabled' },
    { key: ::DashboardWidget::FT_STRICT_VERSION, notes: 'Whether to enforce that the widget type must match the dashboard version', default_toggle_mode: 'enabled' },
    { key: ::Connectors::SftpConnector::FT_DEBUGGING_MODE, notes: 'Enable debugging mode for SFTP connector', default_toggle_mode: 'disabled' },
    { key: ::Transports::RedshiftSource::FT_UNLOAD_CSV_FORMAT, notes: 'add FORMAT CSV into redshift unload statement to get proper csv file', default_toggle_mode: 'disabled' },
    { key: ::Transports::RedshiftSource::FT_APPLY_WHERE_CLAUSE, notes: 'whether to apply WHERE clause when importing from Redshift', default_toggle_mode: 'enabled' },
    { key: 'embedded_analytics:hide', notes: 'Disable 2.0: Hide the Embedded Analytics feature to new users', default_toggle_mode: 'disabled' },
    { key: 'query_templates:hide', notes: 'Disable 2.0: Hide Query Templates option at Tool', default_toggle_mode: 'disabled' },
    { key: 'filter_templates:hide', notes: 'Disable 2.0: Hide Filter Templates option at Tool', default_toggle_mode: 'disabled' },
    { key: 'table:infinite_scroll', notes: 'Enable infinite scroll for table (now only available in widget)', default_toggle_mode: 'disabled' },
    { key: 'data_connection:enabled', notes: 'Holistics 3.0: Enable the new data connection feature', default_toggle_mode: 'disabled' },
    { key: ::DataModel::FT_REFRESH_DEPENDANT_MODELS, notes: 'Enable to refresh the dependant data models whenever the depended data model\'s schema changes', default_toggle_mode: 'disabled' },
    { key: ::DataModel::FT_AML, notes: 'Enable aml expression for custom measure', default_toggle_mode: 'disabled' },
    { key: ::DataModel::FT_AQL, notes: 'Enable aql expression engine', default_toggle_mode: 'disabled' },
    { key: ::DataModel::FT_AML_INTEGERATION, notes: 'Enable aml synchronization', default_toggle_mode: 'disabled' },
    { key: ::DataModel::FT_AQL_CHECK_PATH_RESOLUTION, notes: 'Enable checking path resolution', default_toggle_mode: 'enabled' },
    { key: ::DataModel::FT_AQL_CHECK_ON_FRONTEND, notes: 'Enable checking AQL on the front-end', default_toggle_mode: 'enabled' },
    { key: ::DataModel::FT_AQL_ALLOW_AMBIGUOUS_PATHS, notes: 'Allow ambiguous paths in AQL dataset', default_toggle_mode: 'disabled', js: true },
    { key: ::DataModel::FT_AQL_FILL_MISSING_PIVOT_CELLS, notes: 'Try to fill missing cell caused by pivot better', default_toggle_mode: 'enabled', js: true },
    { key: 'billing:freemium_expired_modal', notes: 'Show the new modal to pick freemium for expired tenants', default_toggle_mode: 'disabled' },
    { key: 'settings:bust_cache:hide', notes: 'Hide Bust Cache button in settings', default_toggle_mode: 'disabled' },
    { key: 'pivot:export_excel_with_format', notes: 'Export pivot Excel with currency, percentage format', default_toggle_mode: 'disabled' },
    { key: ::QueryRun::FT_QUERY_RUNS_WRAPPER, notes: 'Enable query runs wrapper for sql based connectors', default_toggle_mode: 'disabled' },
    { key: ::Permission::FT_EXPLORER_EDIT_DASHBOARD, notes: 'Enable sharing CRUD permission to explorer', default_toggle_mode: 'disabled' },
    { key: ::Permission::FT_COMPOUND_ACTIONS, notes: 'Enable sharing compound actions', default_toggle_mode: 'disabled' },
    { key: ::Google::GoogleSheet::FT_REMOVE_ZERO_WIDTH_CHARACTERS, notes: 'Remove zero width characters', default_toggle_mode: 'disabled' },
    { key: 'drillthrough:enabled', notes: 'Enable drill-through', default_toggle_mode: 'disabled' },
    { key: 'table:single_row', notes: 'Enable single-row line for table', default_toggle_mode: 'disabled' },
    { key: ::Join::FT_AUTO_SYNC_FROM_FKEYS, notes: 'Auto sync Joins from database foreign keys', default_toggle_mode: 'disabled' },
    { key: 'shareable_link:export_csv:hide_front_end', notes: 'Hide export CSV on shareable link', default_toggle_mode: 'disabled' },
    { key: ::Transports::MysqlSource::FT_FORCE_ENCODING_UTF8, notes: 'Force encoding to utf8 while writing to hif', default_toggle_mode: 'disabled' },
    { key: ::Transports::MysqlSource::FT_CONVERT_MYSQL_TIME_TO_ISO8601, notes: 'Convert MySQL datetime to format iso8601', default_toggle_mode: 'disabled' },
    { key: ::Transports::BigqueryDest::FT_REMOVE_BAD_CHARS, notes: 'Remove bad chars before import to Bigquery', default_toggle_mode: 'disabled' },
    { key: 'new_navigation_node', notes: 'new_navigation_node, currently only reporting tab', default_toggle_mode: 'enabled' },
    { key: 'ga:re_authorize', notes: '', default_toggle_mode: 'disabled' },
    { key: ::BaseAbility::FT_INTERSECT_PARENT_ABILITIES, notes: 'Whether to intersect abilities of impersonator and impersonatee; public user and sharer', default_toggle_mode: 'enabled' },
    { key: ::QueryReport::FT_ALLOW_SQL_CREATION, notes: 'Whether to allow creating Reports from SQL', default_toggle_mode: 'disabled' },
    { key: ::QueryReport::FT_ALLOW_STANDALONE_DATASET, notes: 'Whether to allow creating standalone Dataset Reports', default_toggle_mode: 'disabled' },
    { key: ::DataImport::FT_V1_CREATION, notes: 'Whether to allow creating DataImports directly', default_toggle_mode: 'disabled' },
    { key: ::QueryRuns::HardRestriction::FT_BILLING_HARD_RESTRICTION, notes: 'Enable to apply hard restriction when a tenant query runs exceeded the current plan.', default_toggle_mode: 'disabled' },
    { key: ::DataTransform::FT_V1_CREATION, notes: 'Whether to allow creating DataTransforms directly', default_toggle_mode: 'disabled' },
    { key: ::User::FT_ALLOW_TOGGLING_EXPORT_DATA, notes: 'Whether to allow admins from toggling enable_export_data of their users', default_toggle_mode: 'disabled' },
    { key: ::Transports::MysqlDest::FT_CONVERT_TO_MYSQL_HIF, notes: 'Whether to convert hif format to mysql hif format', default_toggle_mode: 'enabled' },
    { key: 'aml_studio:enable', notes: 'Allow showing aml tab', default_toggle_mode: 'disabled' },
    { key: ::DataImport::FT_V3_CONVERSION, notes: 'Allow converting old data imports to v3 import models', default_toggle_mode: 'disabled' },
    { key: ::Slack::SlackService::FT_OAUTH_V2, notes: 'Use Slack oauth flow v2', default_toggle_mode: 'disabled' },
    { key: ::Tenant::FT_TRIAL_DEMO_TENANT, notes: 'Enable when tenant is a Trial Demo Tenant. This FT must be disabled all the time except the Trial Demo Tenant', default_toggle_mode: 'disabled' },
    { key: ::SamlProvider::FW_SAML_SSO, notes: 'Enable SAML Single Sign-On', default_toggle_mode: 'disabled' },
    { key: 'special_event:christmas', notes: 'Show homepage Christmas decoration (2022)', default_toggle_mode: 'disabled' },
    { key: 'special_event:christmas_2023', notes: 'Show homepage Christmas decoration (2023)', default_toggle_mode: 'disabled' },
    { key: 'special_event:christmas_2024', notes: 'Show homepage Christmas decoration (2024)', default_toggle_mode: 'disabled' },
    { key: 'special_event:lunar_new_year_2021', notes: 'Show homepage Lunar New Year decoration', default_toggle_mode: 'disabled' },
    { key: 'crossfilter:enabled', notes: 'Enable cross-filter', default_toggle_mode: 'enabled' },
    { key: ::Tenant::FT_NEW_TIMEZONE_CONFIG, notes: 'New timezone config (UI config, display data, relative filter)', default_toggle_mode: 'disabled' },
    { key: ::Tenant::FT_ENABLE_USER_PROFILE, notes: 'Show avatar and update user info', default_toggle_mode: 'disabled' },
    { key: ::Timezone::Helper::FT_DASHBOARD_TIMEZONE, notes: 'Support dashboard timezone', default_toggle_mode: 'disabled' },
    { key: 'exportings:friendly_filename', notes: 'Use report/widget/dashboard title to name its export file', default_toggle_mode: 'disabled' },
    { key: 'exportings:email_schedule_friendly_filename', notes: 'Use report/widget/dashboard title to name its export file in email', default_toggle_mode: 'disabled' },
    { key: ::VizSetting::FT_DISABLE_LEGACY_HEATMAP, notes: 'Disable legacy heatmap, only allow create new heatmap', default_toggle_mode: 'enabled' },
    { key: ::Permissions::JobPermission::FT_CHECK_EMBED_PAYLOAD, notes: 'Whether to check embed payload when checking job permission', default_toggle_mode: 'enabled' },
    { key: DataModeling::Services::BuildAdjacencyMap::FT_INCLUDE_DATA_LOADS, notes: 'Whether include Data Loads in DM adjacency map', default_toggle_mode: 'disabled' },
    { key: ::Viz::Constants::FT_PIVOT_V2, notes: 'Use pivot mechanism v2', default_toggle_mode: 'disabled' },
    { key: ::Viz::Constants::FT_TABLE_V2, notes: 'Use table mechanism v2', default_toggle_mode: 'enabled' },
    { key: ::Tenant::FT_ENABLE_TENANT_WEEK_START_DAY, notes: 'Enable to use the Tenant Week Start Date', default_toggle_mode: 'disabled' },
    { key: 'tracking:snowplow_micro', notes: 'Use snowplow-micro to check schemas on local', default_toggle_mode: 'disabled' },
    { key: Viz::Data::V3::RegressionModel::FT_REGRESSION_ENABLED, notes: 'Enable Regression calculation', default_toggle_mode: 'disabled' },
    { key: Viz::Data::V3::ReferenceModel::FT_REFERENCE_ENABLED, notes: 'Enable Reference calculation', default_toggle_mode: 'disabled' },
    { key: Viz::Data::V3::VizModelTransformers::Pop::FT_POP_ENABLED, notes: 'Enable Period-over-Period comparison', default_toggle_mode: 'disabled' },
    { key: Viz::Data::V3::VizModelTransformers::Pop::FT_POP_CLICKHOUSE, notes: 'Enable POP for Clickhouse', default_toggle_mode: 'disabled' },
    { key: Viz::Data::V3::VizModelTransformers::Pop::FT_POP_CUSTOM_PERIOD, notes: 'Whether to enable Custom Period for PoP', default_toggle_mode: 'enabled' },
    { key: ScheduleRunner::SlackSender::FT_UPLOAD_IMAGE_TO_SLACK, notes: 'Whether to upload images directly to Slack when sending data to Slack', default_toggle_mode: 'disabled' },
    { key: DataAlert::FT_DATA_ALERTS, notes: '', default_toggle_mode: 'disabled' },
    { key: 'dynamic_filters:related_filters', notes: 'Whether to enable related filter mappings', default_toggle_mode: 'disabled' },
    { key: ::DynamicFilter::FT_INTERACTIVE_CONTROL_POP, notes: 'Whether to enable PoP Interactive Control', default_toggle_mode: 'enabled' },
    { key: ::DynamicFilter::FT_INTERACTIVE_CONTROL_DATE_DRILL, notes: 'Whether to enable Date Drill Interactive Control', default_toggle_mode: 'enabled' },
    { key: ::DynamicFilter::FT_INTERACTIVE_CONTROL_DATE_DRILL_SHOW_GUIDE_POPUP, notes: 'Whether to show popup to guide using Date Drill Interactive Control', default_toggle_mode: 'disabled' },
    { key: Connectors::SnowflakeConnector::FT_NODEJS_CONNECTOR, notes: 'Use Node client instead of ODBC for Snowflake', default_toggle_mode: 'disabled' },
    { key: Connectors::SnowflakeConnector::FT_NODEJS_STREAM_RESULTS, notes: 'Enable streaming results from Nodejs connector', default_toggle_mode: 'disabled' },
    { key: Connectors::SnowflakeConnector::FT_NODEJS_BATCH_STREAM, notes: 'Enabled - stream in batches. Disabled - stream every single record', default_toggle_mode: 'disabled' },
    { key: Connectors::Connection::SnowflakeConnection::FT_SNOWFLAKE_JS_CONNECTOR_ON_EXTERNAL_SERVER, notes: 'Use snowflake js connector on external node_utils server', default_toggle_mode: 'disabled' },
    { key: Connectors::Connection::SnowflakeConnection::FT_SNOWFLAKE_JS_CONNECTOR_QUOTE_DB, notes: 'Quote DB name when passing to snowflake js connector', default_toggle_mode: 'disabled' },
    { key: 'timezone:enable_string_cast_in_field_resolver', notes: 'Temporarily disable cast datetime to string at the field resolver', default_toggle_mode: 'enabled' },
    { key: DataSource::FT_RESTRICT_HOLISTICS_TUNNEL, notes: 'If enabled, only allow data source to connect to holistics tunnel using assigned ports', default_toggle_mode: 'enabled' },
    { key: Viz::Services::ValidateScope::FT_ALLOW_ANY_DATE_SCOPE, notes: 'Viz scope validation: allow viewers and public users to change date transformation for date fields', default_toggle_mode: 'disabled' },
    { key: DataSet::FT_CREATION_IN_REPORTING, notes: 'Enable option to create Datasets in Reporting, only disable for full 4.0 tenants', default_toggle_mode: 'enabled' },
    { key: ::Dashboard::FT_V3_DATE_DRILL, notes: 'Enable dashboard date-drill for dashboard v3', default_toggle_mode: 'disabled' },
    { key: DataImport::FT_DEPRECATE_APP_SOURCE, notes: 'Deprecate app source in import (fb ads, google analytics, zendesk, pipedrive,..)', default_toggle_mode: 'disabled' },
    { key: DataImport::FT_UPSERT_CHECK_DUP, notes: 'Check duplicated records in source before running upsert_table', default_toggle_mode: 'disabled' },
    { key: Viz::Constants::FT_SORT_BEFORE_LIMIT, notes: 'Sort before limit 1000000', default_toggle_mode: 'disabled' },
    { key: 'workbook:disabled', notes: 'Disable folder workbook option when create, edit folder. We deprecated this feature but many tenants are using it', default_toggle_mode: 'disabled' },
    { key: AmlStudio::Project::FT_EXPLICIT_GIT, notes: 'Whether to enable explicit git', default_toggle_mode: 'disabled' },
    { key: AmlStudio::Project::FT_MERGE_RENORMALIZE, notes: 'Whether to enable renormalize option when git merge (explicit git)', default_toggle_mode: 'disabled' },
    { key: DataImport::FT_DEPRECATE_MONGODB, notes: 'Deprecate MongoDB in import :jennie-meh:', default_toggle_mode: 'disabled' },
    { key: 'viz_setting:stricter_format_string_to_date', notes: 'Requires arbitrary string to be in predefined format before parsing to date', default_toggle_mode: 'disabled' },
    { key: Viz::Constants::FT_RUNNING_TOTAL, notes: 'Running total', default_toggle_mode: 'disabled' },
    { key: 'data_center:redirect_to_last_visited_host', notes: 'Re-direct customers to the last visited host when they enter sign-in, sso, forgot password screen', default_toggle_mode: 'disabled' },
    { key: DataModels::Explorable::FT_SQL_GEN_EMULATE_FULL_JOIN_REDSHIFT, notes: 'Whether to emulate full join in Redshift SQL Generation', default_toggle_mode: 'disabled' },
    { key: 'pg:enable_keepalives', notes: 'When enabled, set pg connection keepalives to prevent timeout', default_toggle_mode: 'disabled' },
    { key: QueryReport::FT_KEEP_VIZ_TYPE_WHEN_EXPORT_EXCEL, notes: '', default_toggle_mode: 'disabled' },
    { key: Viz::Constants::FT_EXPLICITLY_CAST_PIVOT_FIELDS_BIGQUERY, notes: 'Whether to explicitly cast pivot values into their field data types on Bigquery', default_toggle_mode: 'disabled' },
    { key: ::Transports::RedshiftSource::FT_TRY_PARSE_WINDOWS_CARRIAGE_RETURN, notes: 'Whether to try parsing Windows carriage return when original process failed', default_toggle_mode: 'disabled' },
    { key: Connectors::FT_NEW_CONNECTORS, notes: 'Use new connectors from data_connector gem', default_toggle_mode: 'disabled' },
    { key: Viz::Constants::FT_TABULAR_DATA_LAZY_TABLE_DATA, notes: 'Load table data of tabular data on user demand only', default_toggle_mode: 'enabled' },
    { key: AdhocQuery::FT_EXPORT_SYNC, notes: 'Whether to export adhoc queries synchronously. Sync exports can block our web workers', default_toggle_mode: 'disabled' },
    { key: 'pivot:apply_conditional_formatting_on_total', notes: '', default_toggle_mode: 'enabled' },
    { key: AmlStudio::Project::FT_EXTERNAL_GIT, notes: 'Whether to enable external git integration', default_toggle_mode: 'disabled' },
    { key: DataModelPersistence::FT_USE_MODEL_FIELD_TYPES, notes: 'Use Model field types as column types in persistence', default_toggle_mode: 'disabled' },
    { key: DataModel::FT_INCLUDE_BIGQUERY_PROJECT_IDENTIFIER, notes: 'Include BigQuery project name in SQL identifiers', default_toggle_mode: 'disabled' },
    { key: DataModel::FT_INCLUDE_SNOWFLAKE_DB_IDENTIFIER, notes: 'Include Snowflake DB name in SQL identifiers', default_toggle_mode: 'disabled' },
    { key: BaseAbility::FT_CACHE, notes: 'Whether to enable caching in Ability checking', default_toggle_mode: 'enabled' },
    { key: Job::FT_OPTIMIZED_ARGUMENTS, notes: 'Whether to enable optimization for Job arguments', default_toggle_mode: 'enabled' },
    { key: DataSets::AmlObjectsCacheService::FT_COERCE_CACHED_STRUCT, notes: 'Whether to enable coercion when loading cached AML Dataset Structs', default_toggle_mode: 'enabled' },
    { key: Connectors::SnowflakeConnector::FT_NODEJS_CONNECTION_POOL, notes: 'Whether to enable connection pooling in Snowflake nodejs connector', default_toggle_mode: 'enabled' },
    { key: Connectors::SqlserverConnector::FT_SANE_SQLSERVER_CONFIG, notes: 'Whether to enable sane connection config for sqlserver', default_toggle_mode: 'disabled' },
    { key: ::User::FT_IMPERSONATION_REMINDER, notes: 'When enabled, impersonation session will be terminated after 2 hours', default_toggle_mode: 'enabled' },
    { key: ::ReportCache::FT_PGCACHE_VERBOSE_LOG, notes: 'Enable verbose log for pgcache, useful to debug performance issue', default_toggle_mode: 'disabled' },
    { key: QueryReports::Runner::Base::FT_DEDUP_SAME_USER_ONLY, notes: 'When enabled, only dedup SQL report jobs of the same user only', default_toggle_mode: 'disabled' },
    { key: Viz::Constants::FT_PRODUCTION_AML_FRONTEND_CACHE, notes: 'Whether to enable frontend cache for production AML objects', default_toggle_mode: 'enabled' },
    { key: AmlStudio::FetchObjects::FT_ALLOW_DEV_MODE_GLOBALLY, notes: 'When enabled, aml studio dev mode is applied globally', default_toggle_mode: 'disabled' },
    { key: AmlStudio::Repositories::Compiler::FT_SC_SERVER, notes: 'When enabled, aml studio will use new aml server which use source control service', default_toggle_mode: 'disabled' },
    { key: 'data_table:bottom_total_average_rows', notes: 'Move total and average rows to bottom instead of top in data table. Please keep this disabled for legacy report tenants', default_toggle_mode: 'disabled' },
    { key: AmlStudio::Project::FT_AML_VERSION, notes: 'Whether to enable AML 2.0', default_toggle_mode: 'disabled' },
    { key: 'jobs:job_queue_listing', notes: 'Show Job Queue in Jobs Listing page', default_toggle_mode: 'enabled' },
    { key: ::Annotation::FT_ANNOTATIONS, notes: 'Enable annotations. Currently used in v2', default_toggle_mode: 'disabled' },
    { key: Viz::Constants::FT_CUSTOM_CHART, notes: 'Enable custom charts', default_toggle_mode: 'disabled' },
    { key: Viz::Constants::FT_UNIFY_CUSTOM_CHART_STYLES, notes: 'Try to unify custom styles with highcharts styles (fonts, colors, etc)', default_toggle_mode: 'enabled' },
    { key: Viz::Constants::FT_ORDER_UNIONED_MODEL_IN_OUTER_QUERY, notes: 'Whether to use ORDER BY on outer query of the UNIONed model (pivot v2)', default_toggle_mode: 'disabled' },
    { key: Viz::Constants::FT_FUNNEL_LABEL_INSIDE, notes: 'Show label inside funnel and pyramid chart', default_toggle_mode: 'disabled' },
    { key: 'dataset_explorer:viz_caching_v2', notes: 'Use caching flow v2 of dataset explorer', default_toggle_mode: 'enabled' },
    { key: Viz::Constants::FT_EXPORT_RENDER_WITH_RAW_VIZ_SETTING, notes: 'Use raw viz setting when exporting, because we using raw viz setting on front-end so if we use explore viz setting the conditional formatting wont work', default_toggle_mode: 'enabled' },
    { key: Connectors::MysqlConnector::FT_QUERY_TIMEOUT, notes: 'Enable query timeout feature for MySQL data sources', default_toggle_mode: 'enabled' },
    { key: Viz::Constants::FT_PIVOT_TABLE_ALLOW_NON_NUMBER_MEASURE, notes: 'Allow non-number measures in pivot tables', default_toggle_mode: 'enabled' },
    { key: Timezone::Helper::FT_PROCESS_UNSUPPORTED_DBTYPES, notes: 'Whether to check data source type to return timezone value', default_toggle_mode: 'enabled' },
    { key: DashboardWidget::FT_EXPORT_WITH_LOCAL_FILTERS, notes: 'Apply local filters when exporting widgets (2.0) directly', default_toggle_mode: 'enabled' },
    { key: ::User::FT_USERS_PAGINATION_API, notes: 'Use users pagination API instead of paginating on Front-end', default_toggle_mode: 'enabled' },
    { key: 'aml_studio:introduce_analytics_as_code', notes: 'Introduce analytics as code for trial user 3.0 only', default_toggle_mode: 'enabled' },
    { key: Connectors::PostgresConnector::FT_LOG_PID, notes: 'Log PID during postgres sql execution', default_toggle_mode: 'disabled' },
    { key: ReportCategory::FT_SEARCH_WITH_WIDGETS, notes: 'enable widgets search', default_toggle_mode: 'enabled' },
    { key: ::HOtel::FT_OTEL_ENABLED, notes: 'Enable OpenTelemetry instrumentation', default_toggle_mode: 'disabled' },
    { key: User::FT_COMMAND_PALETTE, notes: 'Command Palette', default_toggle_mode: 'disabled' },
    { key: DataTransforms::TransformRunner::FT_COLUMNS_FROM_TEMP_TABLE, notes: 'Get column config for DataTransform from created temp table instead of the transform sql', default_toggle_mode: 'disabled' },
    { key: 'bigquery_connector:hotswap_using_rename', notes: 'Perform table hotswapping using RENAME statement', default_toggle_mode: 'enabled' }, # Use raw string instead of FT_HOTSWAP_USING_RENAME to avoid CircleCI failure because of GlobalConfig.get_value usage in BigqueryConnector
    { key: Viz::Constants::FT_DEDUP_EXPLORE_JOB, notes: 'Dedup viz explore jobs', default_toggle_mode: 'enabled' },
    { key: ::DataSource::DBT_MANIFEST_LOGS, notes: 'View dbt manifest file uploads', default_toggle_mode: 'disabled' },
    { key: InternalEmbedLink::FT_USAGE_MONITORING, notes: 'Enable usage monitoring dashboard in admin setting', default_toggle_mode: 'disabled' },
    { key: InternalEmbedLink::FT_USAGE_MONITORING_MAINTENANCE, notes: 'Enable maintenance mode for usage monitoring', default_toggle_mode: 'disabled' },
    { key: InternalEmbedLink::FT_USAGE_MONITORING_EXTRA_FEATURES, notes: 'FT to control the extra features for UM dashboards, such as export raw data, date drill,..', default_toggle_mode: 'disabled' },
    { key: InternalEmbedLink::FT_JOB_MONITORING, notes: 'Enable Job monitoring dashboard in admin setting', default_toggle_mode: 'disabled' },
    { key: InternalEmbedLink::FT_JOB_MONITORING_MAINTENANCE, notes: 'Enable maintenance mode for Job monitoring', default_toggle_mode: 'disabled' },
    { key: DataAlerts::GetExploreResults::RUN_SOURCE_ON_DATA_TABLE_FORM, notes: 'Build explore based on the data table form of the source viz setting', default_toggle_mode: 'enabled' },
    { key: Viz::Constants::FT_IMPLICIT_PLACEHOLDER_DATA_TYPE, notes: 'Use implicit data type on pivot placeholder values', default_toggle_mode: 'enabled' },
    { key: 'jobs:display_revamped_job_statuses', notes: 'Display revamped job statuses instead of database job statuses. https://www.notion.so/holistics/Revamp-Job-Statuses-9f5caa1d866141adb0a0d7f0bdb4b2e3', default_toggle_mode: 'disabled' },
    { key: Viz::Constants::FT_TREAT_PLACEHOLDERS_AS_LITERALS, notes: 'Treat placeholder values as literal values', default_toggle_mode: 'enabled' },
    { key: Connectors::PostgresConnector::FT_FETCH_ALL_MATERIALIZED_VIEWS, notes: 'Fetch materialized views when fetching all columns in a Postgres data source', default_toggle_mode: 'disabled' },
    { key: Billing::BillingUpgradeService::FT_UPGRADE_BILLING_USING_JOB, notes: 'Update billing in a job', default_toggle_mode: 'enabled' },
    { key: 'filter:time_filter', notes: 'Enable time filter', default_toggle_mode: 'enabled' },
    {
      key: Viz::Constants::FT_ENABLE_EMBED_WORKERS_FOR_EXPLORE,
      notes: 'When enabled, explore jobs (e.g. running dataset-based report) of embed users will run via embed workers',
      default_toggle_mode: 'enabled',
    },
    { key: AmlStudio::Project::FT_DELETE_ORPHANED_PRODUCTION_DATASET, notes: 'When enabled, allow directly deleting orphaned production datasets that have no dependencies', default_toggle_mode: 'disabled' },
    { key: ::SearchService::Base::FT_SEARCH_V2B, notes: 'Full text search using PostgreSQL. https://www.postgresql.org/docs/current/textsearch.html', default_toggle_mode: 'enabled' },
    { key: DataSet::FT_CONSTRUCT_RL_PERMISSIONS_FOR_ALL_USERS, notes: 'When enabled, dataset permission rules will always be constructed in the dataset\'s explores, making sure the explore structure is consistent among all users', default_toggle_mode: 'enabled' },
    { key: Connectors::Base::FT_USE_QUOTED_NAME, notes: 'Connectors return quoted database identifiers (table name, schema name...)', default_toggle_mode: 'disabled' },
    { key: CodingUtils::FQNames::FT_PARSE_USING_REG_EXP, notes: 'Enable FQName.parse to use regular expression for parsing name strings', default_toggle_mode: 'enabled' },
    {
      key: DataSet::FT_STRICT_PERMISSION_RULES,
      notes: 'When enabled, strictly check that all permission rules are applied in dataset explore',
      default_toggle_mode: 'enabled',
    },
    { key: 'ui:use_filter_select', notes: 'use new added component for dashboard filter', default_toggle_mode: 'disabled' },
    {
      key: 'filter:new_filter_ux_revamp_v1_032023',
      notes: 'When enabled, new filter UX v1 (built in 03/2023) will be used',
      default_toggle_mode: 'disabled',
    },
    {
      key: ShareableLink::FT_USE_API_V2_IN_UI,
      notes: 'When enabled, use the new APIs v2 in Shareable Link UIs',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'aml_studio_search_metadata',
      notes: 'Feature toggle allow search metadata in AML files',
      default_toggle_mode: 'disabled',
    },
    {
      key: DataModel::FT_PROCESS_DYNAMIC_MODELS,
      notes: 'Process Dynamic Models when resolving fields (E.g: Query Params)',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'ag-grid:data-table',
      notes: 'When enabled, use the data table from AG-Grid',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'ag-grid:pivot-table',
      notes: 'When enabled, use the pivot table from AG-Grid',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'ag-grid:metric-sheet',
      notes: 'When enabled, use the metric sheet from AG-Grid',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'ag-grid:cohort-retention',
      notes: 'When enabled, use the cohort retention from AG-Grid',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'aml_studio_recent_edited_files',
      notes: 'Feature toggle to allow show recent edited files section in default page',
      default_toggle_mode: 'disabled',
    },
    {
      key: ::Viz::Constants::FT_SKIP_FILTERS_ON_PIVOT_META_DATA,
      notes: 'When enabled, do NOT apply filters on pivot meta data (including pivot column headers, totals data, etc.)',
      default_toggle_mode: 'enabled',
    },
    {
      key: ::AmlStudio::WorkingEnvironment::FT_STRICT_ENV_CHECK,
      notes: 'When enabled, working environment can be enforced strictly',
      default_toggle_mode: 'enabled',
    },
    {
      key: ::VizSetting::FT_SORT_PROCESSING_V2,
      notes: 'When enabled, use sort processing logic v2 for VizSetting sort settings',
      default_toggle_mode: 'enabled',
    },
    {
      key: ::AmlStudio::Project::FT_BINDING_CACHE_ONLY,
      notes: 'When enabled, AML dataset in reporting layer will be fetch directly from binding cache',
      default_toggle_mode: 'disabled',
    },
    { key: Connectors::SqlserverConnector::FT_QUERY_TIMEOUT, notes: 'Enable query timeout feature for SQL Server data sources', default_toggle_mode: 'enabled' },
    { key: Connectors::ClickHouseConnector::FT_QUERY_TIMEOUT, notes: 'Enable query timeout feature for ClickHouse data sources', default_toggle_mode: 'enabled' },
    { key: Connectors::BigqueryConnector::FT_QUERY_TIMEOUT, notes: 'Enable query timeout feature for BigQuery data sources', default_toggle_mode: 'enabled' },
    { key: ::DataSet::FT_DATASET_VIEWS, notes: 'Allow displaying Dataset Views', default_toggle_mode: 'disabled' },
    { key: Connectors::SnowflakeConnector::FT_QUERY_TIMEOUT, notes: 'Enable query timeout feature for Snowflake data sources', default_toggle_mode: 'enabled' },
    { key: 'prompt_trial_call:enable_close', notes: 'Allow user to close prompt', default_toggle_mode: 'disabled' },
    {
      key: ::DataModel::FT_SQL_GEN_GEM_QUERY_SPLITTER_V2,
      notes: 'V2 query splitter engine with better performance and functionalities',
      default_toggle_mode: 'enabled',
    },
    { key: Connectors::AthenaConnector::FT_QUERY_TIMEOUT, notes: 'Enable query timeout feature for Athena data sources', default_toggle_mode: 'enabled' },
    {
      key: ::ModelFieldDependency::FT_MODEL_FIELD_DEPENDENCIES,
      notes: 'Whether to update the new reporting field dependencies',
      default_toggle_mode: 'enabled',
    },
    { key: 'onboarding:enable_onboarding_40_tooltips', notes: 'Enable onboarding 4.0 tooltips tour', default_toggle_mode: 'disabled' },
    { key: ::Dashboard::FT_V4_CREATION, notes: 'Whether to allow creating Dashboard v4 (as-code)', default_toggle_mode: 'disabled' },
    { key: ::Connectors::BigqueryConnector::FT_BATCH_FETCH_ALL_COLUMNS_BY_QUERY, notes: 'Batch fetch all columns using INFORMATION_SCHEMA views. Each batch fetches all columns of all datasets within 1 region.', default_toggle_mode: 'enabled' },
    {
      key: DataAlerts::DataAlertWebhookSender::FT_WEBHOOK_DESTINATION,
      notes: 'Webhook destination for data alerts',
      default_toggle_mode: 'disabled',
    },
    { key: ::Viz::Constants::FT_LIMIT_100_PIVOT_COLUMNS, notes: 'Load only 100 pivot columns (in pivot tables) to avoid heavy rendering', default_toggle_mode: 'enabled' },
    { key: ::Viz::Constants::FT_LIMIT_100_CHART_PIVOT_COLUMNS, notes: 'Load only 100 pivot columns (in charts) to avoid heavy rendering', default_toggle_mode: 'enabled' },
    { key: ::DataSource::FT_DATABRICKS_ENABLED, notes: 'Enable Databricks for Data Sources', default_toggle_mode: 'disabled' },
    { key: 'tenant_migration:disable_foreign_key_mapping', notes: 'Disable foreign key mapping during tenant migration to avoid timeout. ONLY FOR TESTING', default_toggle_mode: 'disabled' },
    { key: 'viz_result:queue_update_v2', notes: 'Use v2 (supposedly better) mechanism to process consecutive updates on VizResult', default_toggle_mode: 'disabled' },
    { key: 'viz_result:queue_update_v3', notes: 'Use v3 (supposedly more stable) mechanism to process consecutive updates on VizResult', default_toggle_mode: 'enabled' },
    { key: 'smartlook:enable', notes: 'Use smartlook to monitor this trial tenant', default_toggle_mode: 'disabled' },
    { key: ::DashboardWidget::FT_RENDER_IN_VIEWPORT_ONLY, notes: 'Only render widgets that are in viewport', default_toggle_mode: 'enabled' },
    {
      key: ::AmlStudio::Project::FT_DASHBOARD_V4,
      notes: 'AML Studio: Whether to allow creating Dashboard v4 (as-code)',
      default_toggle_mode: 'disabled',
    },
    { key: ::AmlStudio::Project::FT_EDIT_IN_PRODUCTION,
      notes: 'Can edit deployed canvas dashboard in production',
      default_toggle_mode: 'enabled',  },
    {
      key: ::AmlStudio::Project::FT_AUTOSAVE,
      notes: 'AML Studio: Whether to allow autosaving',
      default_toggle_mode: 'enabled',
    },
    {
      key: ::AmlStudio::Project::FT_MONACO_EDITOR_EOL_LF,
      notes: 'AML Studio: Set monaco editor to auto switch eol to lf',
      default_toggle_mode: 'enabled',
    },
    {
      key: ::AmlStudio::Project::FT_INIT_DEFAULT_GITATTRIBUTE,
      notes: 'AML Studio: Whether to init default gitattribute for new repo',
      default_toggle_mode: 'disabled',
    },
    {
      key: ::DataModel::FT_SQL_GEN_GEM_IN_DB_MODELING,
      notes: 'Always use sql_generation gem during query parsing and generation',
      default_toggle_mode: 'enabled',
    },
    {
      key: ::DataSet::FT_USE_DATASET_V2_API,
      notes: 'Fetch datasets with v2 API in front end in dashboard flow. Will remove this FT if theres no error in production',
      default_toggle_mode: 'enabled',
    },
    { key: 'aml_studio:enable_data_source_validation', notes: 'Enable data source validation for AML Studio', default_toggle_mode: 'enabled' },
    {
      key: Job::FT_ALLOW_CHANGE_JOB_TAG_IN_SET_DATA,
      notes: "Disable change job's tag is set_data method. Will remove this FT if it's reliable on production",
      default_toggle_mode: 'disabled',
    },
    { key: Users::OmniauthCallbacksController::FT_AUTO_REMEMBER_ME_ENABLED, notes: 'Enable SSO auto remember me', default_toggle_mode: 'enabled' },
    {
      key: ::AmlStudio::DataModels::ValidateField::FT_VALIDATE_CUSTOM_AGGREGATION_MEASURE_V2,
      notes: 'New custom aggregation measure validation in AML Studio',
      default_toggle_mode: 'disabled',
    },
    {
      key: ::AmlStudio::Project::FT_ENCRYPT_PASSPHRASE_WITH_MESSAGE_ENCRYPTOR,
      notes: 'Whether to encrypt ssh passphrase with ActiveSupport::MessageEncryptor',
      default_toggle_mode: 'enabled',
    },
    {
      key: DataModelPersistence::FT_ALLOW_CUSTOM_DDL,
      notes: 'Allow custom ddl for data model persistence',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'onboarding:display_trial_info_banner',
      notes: 'Display trial info banner',
      default_toggle_mode: 'enabled',
    },
    { key: 'loading_progress:use_elapsed_time', notes: 'Use elasped time (1s) instead of percentage (1%) for loading', default_toggle_mode: 'disabled' },
    { key: ::HOtel::FT_PAGE_TRACING, notes: 'Enable HOTel page tracing', default_toggle_mode: 'disabled' },
    {
      key: ::AmlStudio::Project::FT_ENABLE_DEFAULT_BRANCH,
      notes: 'Whether to create a new default branch for new user in AML Studio',
      default_toggle_mode: 'disabled',
    },
    {
      key: AmlStudio::Project::FT_UTILIZE_FE_PROCESSING,
      notes: 'Utilize FE processing for viz api',
      default_toggle_mode: 'disabled',
    },
    {
      key: AmlStudio::Project::FT_FE_UNIFY_FIELD_TYPE,
      notes: 'Unify field type in FE',
      default_toggle_mode: 'enabled',
    },
    {
      key: Connectors::BigqueryConnector::FT_STORAGE_API,
      notes: 'Enable using storage API for BigQuery connector',
      default_toggle_mode: 'enabled',
    },
    {
      key: AdhocQueriesController::AQL_SQL_EDITOR,
      notes: '[Internal usage only] Enable AQL syntax explore in SQL editor',
      default_toggle_mode: 'disabled',
    },
    {
      key: Connectors::BigqueryConnector::FT_DATASET_SCHEMA_WHITELIST,
      notes: 'When enabled, the Admin can configure which specific Bigquery Datasets should be schema-synchronized',
      default_toggle_mode: 'disabled',
    },
    {
      key: ::DataSource::FT_HIDE_JSON_CREDENTIALS,
      notes: 'Hide JSON credentials in data source form for data sources that have JSON-like setup, e.g. BigQuery',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'feature_wall:albus',
      notes: 'Enable Albus - ask meaningfun questions feature',
      default_toggle_mode: 'disabled',
    },
    { key: 'aml_studio:enable_navigate_from_reporting', notes: 'Enable navigation from reporting to modeling', default_toggle_mode: 'enabled' },
    { key: ::Job::FT_UNUSED_JOB_TTL_TENANT_SETTING, notes: 'Allow tenants to change unused report jobs timeout in Setting page', default_toggle_mode: 'disabled' },
    {
      key: ::Excel::Formats::Value::FT_FORMAT_TIMESTAMP,
      notes: 'Format timestamp to YYYY-MM-DD HH:mm:ss when exporting report to excel or csv',
      default_toggle_mode: 'disabled',
    },
    {
      key: Viz::AmqlSettings::FT_AMQL_FILTER_GROUPS,
      notes: 'Use AMQL specific filters: Filter Groups',
      default_toggle_mode: 'disabled',
    },
    {
      key: AmlStudio::Project::FT_GIT_RESTORE,
      notes: 'Enable git (version) restoring in AML Studio',
      default_toggle_mode: 'disabled',
    },
    {
      key: ::Tenant::FT_TENANT_VISUALIZATION_SETTING_V2,
      notes: 'Allow tenants to config charts and tables\' default row limit from in-app tenant setting',
      default_toggle_mode: 'disabled',
    },
    {
      key: ::Tenant::FT_FORCE_DEPRECATING_OLD_COLOR_PALETTE,
      notes: 'Force deprecating old color palette (only enable for internal tenants)',
      default_toggle_mode: 'disabled',
    },
    {
      key: ::DashboardWidget::FT_PRECISE_DASHBOARD_HEADER_FOR_EXPANDING_WIDGET_V3,
      notes: 'In dashboard v3, dashboard header in expanded widget will show the cache status of current widget instead of whole dashboard. This also applies for operations such as refreshing, canceling widget',
      default_toggle_mode: 'enabled',
    },
    {
      key: ::AmlStudio::Project::FT_SERIALIZED_CACHE,
      notes: 'AML Studio: Use the new serialized cache instead of binding cache',
      default_toggle_mode: 'disabled',
    },
    {
      key: Job::FT_ROLLING_POLLING,
      notes: 'Poll jobs with interval that starts very low but then increases incrementally. NOTE: not affected by Global Config job_polling_interval',
      default_toggle_mode: 'enabled',
    },
    {
      key: ::HOtel::FT_VUE_INSTRUMENTATION,
      notes: 'Auto instrument Vue component lifecycle',
      default_toggle_mode: 'disabled',
    },
    {
      key: JobLog::FT_SAVE_USING_RAW_SQL,
      notes: 'Using raw SQL to insert job log',
      default_toggle_mode: 'enabled',
    },
    {
      key: Job::FT_SIDEKIQ_SCHEDULE_QUEUE,
      notes: 'Push jobs to Sidekiq queue schedule. Notion docs: https://www.notion.so/holistics/Experiment-new-Sidekiq-queue-setup-a746e191a3ef490ea4fce59418d6d434',
      default_toggle_mode: 'enabled',
    },
    {
      key: Viz::AmqlSettings::FT_AMQL_ADHOC_FIELDS,
      notes: 'Allow creating AQL Ad-hoc fields and store them in VizSetting AMQL Settings.',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'web_workers:show_unsupported_browser_mesage',
      notes: 'Show warning banner on unsupported browsers due to web worker import syntax https://holistics.slack.com/archives/CN8S0UF8E/p1711340298845659',
      default_toggle_mode: 'disabled',
    },
    {
      key: PostgresCache::FT_USE_BULK_LOAD_V2,
      notes: 'Use postgres_cache bulk_load_v2',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'metric_kpi:new_renderer_for_v4',
      notes: 'User the new metric kpi renderer for dashboard v4',
      default_toggle_mode: 'disabled',
    },
    {
      key: Job::FT_ALLOW_ANALYST_TO_MANAGE_JOB,
      notes: 'Allow analyst to manage jobs',
      default_toggle_mode: Rails.env.development? ? 'enabled' : 'disabled',
    },
    {
      key: 'viz:customizable_tooltips',
      notes: 'Support adding measures to chart tooltips.',
      default_toggle_mode: 'disabled',
    },
    { key: ::DataModel::FT_AQL_AS_DEFAULT, notes: 'Use AQL as the default engine for all datasets', default_toggle_mode: 'disabled' },
    { key: ::DataModel::FT_AQL_OPTIMIZE_FANOUT_AGG, notes: 'Run optimize fan out agg phase in AQL', default_toggle_mode: 'enabled' },
    {
      key: Viz::Action::FT_ACTION,
      notes: 'Support adding action to chart.',
      default_toggle_mode: 'disabled',
    },
    { key: ::DataModel::FT_AQL_AS_DEFAULT_WITH_BIZ_CAL, notes: 'Use AQL as the default engine for all datasets but keep the legacy BizCal fields', default_toggle_mode: 'disabled' },
    { key: Billing::RestrictionService::HARD_RESTRICTION_FOR_EMBED, notes: 'Billing: Hard restriction blocking embedded analytics when tenant has exceeed the number of usage objects', default_toggle_mode: 'disabled' },
    { key: Tenant::FT_ALLOW_SENDER_EMAIL, notes: 'Allow to set data schedule Sender\'s email address', default_toggle_mode: 'enabled' },
    {
      key: ::AmlStudio::Project::FT_GIT_SYNC,
      notes: 'AML Studio: Whether to allow auto git sync',
      default_toggle_mode: 'disabled',
    },
    { key: ::Viz::Constants::FT_REFRESH_WITH_APPLIED_VIZ_SETTING, notes: 'Use the latest applied viz setting when refresh viz', default_toggle_mode: 'enabled' },
    { key: ::User::FT_ALLOW_EXPLORER_BIZ_USER_SHARE_PRIVATE_ITEM, notes: 'When enabled, the explorers and biz users have ability to share the private item', default_toggle_mode: 'enabled' },
    {
      key: 'aml_studio:status_panel',
      notes: 'Enable new status panel in AML Studio',
      default_toggle_mode: 'enabled',
    },
    {
      key: Viz::Data::Amql::PreAggregations::FT_ENABLED,
      notes: 'Enable Pre-Aggregations',
      default_toggle_mode: 'enabled',
    },
    { key: 'email_schedule:allow_config_image_width', notes: 'User can config the image width for the inline attachments', default_toggle_mode: 'disabled' },
    { key: Tenant::FT_SANDBOX_TENANT, notes: 'Sandbox tenant for demo', default_toggle_mode: 'disabled' },
    {
      key: AmlStudio::Repositories::Compiler::FT_AML_SERVER_USE_WORKER_THREAD,
      notes: 'AML Server uses Worker Thread',
      default_toggle_mode: 'disabled',
    },
    {
      key: AdhocQuery::FT_AVOID_GUESS_COL_TYPE,
      notes: 'When enabled, we will avoid the legacy column type guessing mechanism',
      default_toggle_mode: 'enabled',
    },
    { key: AmlStudio::Project::FT_REPO_STATE_V2, notes: 'Use explicit:repo_state v2', default_toggle_mode: 'enabled' },
    {
      key: Viz::Constants::FT_CACHE_KEY_V2,
      notes: 'Use v2 of cache key generation when running viz',
      default_toggle_mode: 'enabled',
    },
    { key: ::Viz::Constants::FT_AMQL_NEW_EDIT_UI, notes: 'New pop-up UI to edit aql field', default_toggle_mode: 'enabled' },
    { key: ::Viz::Constants::FT_AMQL_ADHOC_FIELD_REUSE, notes: 'Enabled re-usable adhoc fields', default_toggle_mode: 'enabled' },
    { key: AmlStudio::Project::FT_AUTO_IMPORT_RELATIONSHIP, notes: 'Allow adding relationships when creating new dataset + auto import existing relationships', default_toggle_mode: 'enabled' },
    {
      key: Dashboard::FT_PIN_DASHBOARD,
      notes: 'Allow admin pin dashboard to homepage',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'ag-grid:allow_save_column_width_size',
      notes: 'Allow save column width size of pivot table and data table in AG-Grid for canvas dashboard',
      default_toggle_mode: 'enabled',
    },
    {
      key: Viz::Constants::FT_FIELD_SUGGESTIONS_STRICT_ANALYST_PERMISSION,
      notes: 'Apply strict permission checking to Analyst when fetching field suggestions',
      default_toggle_mode: 'enabled',
    },
    {
      key: AmlStudio::Project::FT_ALL_MODELS_PUBLIC_API,
      notes: '4.0 GET /data_models Public API',
      default_toggle_mode: 'enabled',
    },
    { key: 'percent_of_total:enabled', notes: 'Enable Percent of Total calculation', default_toggle_mode: 'enabled' },
    {
      key: Viz::Exporting::Exporters::Excel::FT_FAST_EXCEL_DATE_TIME_CORRECT_PRECISION,
      notes: 'Enable this to make sure fast excel will render date time serial number value with correct precision. This FT only works if the monkey-patch in config/initializers/fast_excel.rb get enabled. Notion docs: https://www.notion.so/holistics/fast_excel-61ba441b544e44f0b174110858f6af60?pvs=4#5266cb3e008d42a985101bd0a3cafc9e',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'table:reorder_columns',
      notes: 'Allow user reordering table columns',
      default_toggle_mode: 'disabled',
    },
    { key: ::Viz::Constants::FT_AQL_SORT_BEFORE_LIMIT, notes: 'Sort-before-limit for AQL engine', default_toggle_mode: 'enabled' },
    { key: ::Viz::Constants::FT_STANDARD_SQL_IS_OPERATOR, notes: 'Use the correct behavior from SQL Standard for is/is not operator', default_toggle_mode: 'disabled' },
    { key: 'dac:drillthrough', notes: 'Whether to enable drillthrough on canvas dashboard or not.', default_toggle_mode: 'disabled' },
    { key: 'viz_settings:explorer_control_v2', notes: 'Enable revamped submit button behavior', default_toggle_mode: 'enabled' },
    {
      key: 'table:freeze_columns',
      notes: 'Allow user freezing table columns',
      default_toggle_mode: 'disabled',
    },
    { key: 'moving_calculation:enabled', notes: 'Enable Moving calculation', default_toggle_mode: 'enabled' },
    {
      key: Canal::Constants::FT_ENABLED,
      notes: 'Enable Canal engine',
      default_toggle_mode: 'disabled',
    },
    {
      key: AmlStudio::Project::FT_USE_DBT_CLOUD_CREDENTIALS,
      notes: 'Use dbt credentials data in aml_studio_projects table',
      default_toggle_mode: 'disabled',
    },
    { key: ::ShareableLink::FT_EMBEDDABLE, notes: 'Allow embedding shareable links in iframe', default_toggle_mode: 'disabled' },
    {
      key: AmlStudio::Project::FT_ALLOW_DBT_CLOUD_CUSTOM_URL,
      notes: 'Allow custom url for dbt cloud',
      default_toggle_mode: 'disabled',
    },
    {
      key: ::AmlStudio::Project::FT_GIT_SYNC_NUX,
      notes: 'AML Studio: Enable onboarding tooltip for git sync user',
      default_toggle_mode: 'enabled',
    },
    { key: Job::FT_SELECT_MINIMAL_COLUMNS, notes: 'Try to fetch minimal columns when fetching Job records', default_toggle_mode: 'enabled' },
    {
      key: Viz::Constants::FT_NEW_JOB_EXPLORE_PAYLOAD,
      notes: 'Use new (optimized) explore payload when executing explore jobs',
      default_toggle_mode: 'enabled',
    },
    {
      key: DataSets::AmlObjectsCacheService::FT_ARGUMENTS_SERIALIZER_CACHE,
      notes: 'Enable caching of ArgumentsSerializer for Dataset cache',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'dashboards_v4:auto_placement_for_new_block',
      notes: 'Enable auto placement for new block',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'pivot:freeze_columns',
      notes: 'Allow user freezing pivot row fields columns',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'exportings:allow_include_cross_filter',
      notes: 'Allow export including cross filter on canvas dashboard',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'out-of-sync:show-banner',
      notes: 'Show banner when report is out-of-sync',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: 'node_tree:auto_scroll_current_view_item',
      notes: 'Sidebar (Tree list): auto-scroll to the currently viewed item https://app.asana.com/0/76997687380943/1207284888095562/f',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'export:enable_auto_download',
      notes: 'Auto check the checkbox for auto download when exporting',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'aml_studio:branch_param',
      notes: 'Enable branch param in AML Studio',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'table:hide_fields',
      notes: 'Allow user hiding table fields',
      default_toggle_mode: 'disabled',
    },
    {
      key: AmlStudio::Repositories::Compiler::FT_AML_SERVER_USE_GZIP,
      notes: 'Use gzip compression when working with aml server',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'pie_chart:sort_desc_by_default',
      notes: 'Sort pie chart in desc by default',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'out-of-sync:block-table-interaction',
      notes: 'Block table interaction when out-out-sync',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: 'google_sheets:write_unsupported_date_formats_as_string',
      notes: 'Write date value with google sheet unsupported formats (wwww and qq) as string to google sheet dest',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'submit_generate:use_gzip',
      notes: 'Compress submit generate request (gzip)',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'exports:exclude_hidden_fields',
      notes: 'The exported result does not show the hidden fields',
      default_toggle_mode: 'enabled',
    },
    {
      key: Viz::Constants::FT_BATCH_SUBMIT_GENERATE,
      notes: 'Batch submit_generate (viz) requests',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'aml_studio:init_repo_loading_screen',
      notes: 'Show loading screen when initializing repo',
      default_toggle_mode: 'enabled',
    },
    {
      key: Canal::Constants::FT_SNOWFLAKE_USE_ARROW_DOWNLOADER,
      notes: '(Canal) Whether to fetch Snowflake result using Arrow downloader',
      default_toggle_mode: 'enabled',
    },
    {
      key: Canal::Constants::FT_CONNECTION_POOL,
      notes: '(Canal) Whether to enable connection pooling',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'pivot:hide_fields',
      notes: 'Allow user hiding pivot row fields',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'pivot:post_update_when_paginating_v2',
      notes: 'Apply new flow when sorting, scrolling infinitely, and paginate',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'feature_wall:two_factor_auth',
      notes: '',
      default_toggle_mode: 'disabled',
    },
    {
      key: AmlCompiledCache::FT_USE_AML_COMPILED_CACHE,
      notes: 'Use AML compiled cache from database instead of Redis',
      default_toggle_mode: 'enabled',
    },
    {
      key: Viz::Constants::FT_DASHBOARD_FETCH_DATASETS_ASYNC,
      notes: 'When enabled, Viz in Dashboard will run asynchronously without waiting for fetching Datasets',
      default_toggle_mode: 'enabled',
    },
    {
      key: Viz::Constants::FT_PIE_LABEL_POSITION_SETTING,
      notes: 'Enable setting for users to specify the position of labels in pie, pyramid, funnel charts',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'user:allow_edit_user_email',
      notes: 'Allow admin to edit user email',
      default_toggle_mode: 'disabled',
    },
    {
      key: Viz::AmqlSettings::FT_AMQL_FILTER_CONDITIONS,
      notes: 'Use AQL ad-hoc filter condition',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'table:auto_size',
      notes: 'Allow user auto-size columns in data table',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'pivot:auto_size',
      notes: 'Allow user auto-size columns in pivot table',
      default_toggle_mode: 'disabled',
    },
    {
      key: AmlStudio::Project::FT_SOURCE_CONTROL_POSTIONAL_ARGS,
      notes: 'Using positional args for source control entry methods',
      default_toggle_mode: 'enabled',
    },
    {
      key: AmlStudio::Project::FT_SOURCE_CONTROL_WARNING_KEYWORD_ARGS,
      notes: 'Detect/warn if source control method is using keyword args',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'exportings:allow_include_cross_filter_on_dynamic_dashboard',
      notes: 'Allow export including cross filter on dynamic dashboard',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'table:remove_columns',
      notes: 'Allow user removing columns in data table',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'pivot:remove_columns',
      notes: 'Allow user removing fields in pivot table',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'aml_editor:advanced_language_features',
      notes: 'Enable hover information, auto complete, live project validation, etc',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'table:rename_columns',
      notes: 'Allow user renaming columns in data table',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'pivot:rename_columns',
      notes: 'Allow user renaming row fields in pivot table',
      default_toggle_mode: 'disabled',
    },
    {
      key: ::Slack::SlackService::FT_FILES_UPLOAD_V2,
      notes: 'Use new Slack files upload API. Slack announcement: https://api.slack.com/changelog/2024-04-a-better-way-to-upload-files-is-here-to-stay',
      default_toggle_mode: 'disabled',
    },
    {
      key: AmlStudio::Project::FT_CONFLICT_RESOLUTION_V2,
      notes: 'Use the new conflict resolution flow',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'table:aggregate_columns',
      notes: 'Allow user aggregating columns in data table',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'ask_witch:enabled',
      notes: 'Enable Ask Witch',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'pivot:aggregate_columns',
      notes: 'Allow user aggregating row fields and measure fields in pivot table',
      default_toggle_mode: 'disabled',
    },
    {
      key: AmlStudio::Project::FT_JSONRPC_CLIENT_USE_OJ_JSON_MODE,
      notes: 'Use Oj JSON mode to parse colon correctly. Related slack: https://holistics.slack.com/archives/CP38TMPNU/p1733732456071639',
      default_toggle_mode: 'enabled',
    },
    {
      key: Canal::Constants::FT_REPLACE_RUBY_DRIVERS,
      notes: 'Replace Ruby connector drivers with Canal',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'viz_result:persist_adhoc_settings',
      notes: 'Persist interactions such as chart zoom, date drill, sort when expanding/collapsing widgets/blocks',
      default_toggle_mode: 'enabled',
    },
    {
      key: AmlStudio::Project::FT_LOG_REQUEST_PARAMETER,
      notes: 'Enable log request parameter to Source Control and AML server',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'table:add_edit_calculations',
      notes: 'Allow user adding and editing calculation columns for measure/metric fields in data table',
      default_toggle_mode: 'disabled',
    },
    { key: 'aql_pop:enabled', notes: 'Enable AQL-based PoP GUI creation', default_toggle_mode: 'enabled' },
    {
      key: 'pivot:add_edit_calculations',
      notes: 'Allow user adding and editing calculation columns for measure/metric fields in pivot table',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'table:add_new_columns',
      notes: 'Allow user adding new column on data table, the new column locates at the very right side',
      default_toggle_mode: 'disabled',
    },
    { key: ::EmbedLink::FT_DATA_EXPLORE, notes: 'Allow data exploration in embedded', default_toggle_mode: 'disabled' },
    {
      key: Canal::Constants::FT_SANE_SQLSERVER_CONFIG,
      notes: 'Canal: Set sane sql configs for sqlserver to make it behaves similar to sql standards',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'billing:billing_page_composition_api',
      notes: 'Enable billing page using composition api',
      default_toggle_mode: 'enabled',
    },
    {
      key: Plan::FT_TRIAL_PLAN,
      notes: 'Allow assign trial plan for all new trial submissions',
      default_toggle_mode: 'disabled',
    },
    {
      key: Ai::Constants::FT_PHOEBE_EXAMPLES,
      notes: 'Enable constructing examples for Prompt-to-Explore AI agent',
      default_toggle_mode: 'disabled',
    },
    {
      key: Ai::Constants::FT_PHOEBE,
      notes: 'Enable Prompt-to-Explore AI agent',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: Ai::Constants::FT_DOCSEARCH,
      notes: 'Enable Doc Search AI agent',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: Ai::Constants::FT_MEG,
      notes: 'Enable Metadata Generation AI agent',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: Ai::Constants::FT_AQUA,
      notes: 'Enable AQL AI agent',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: Ai::Constants::FT_SCOUT,
      notes: 'Enable AMQL Autocomplete agent',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: Ai::Constants::FT_COMME,
      notes: 'Commit message generator',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: EmailSchedule::FT_ENABLE_CUSTOM_EMAIL_DYNAMIC_MESSAGE,
      notes: 'Enable users to customize variables and adjust the email subject and body for data alerts and schedules. This FT only works for 3.0 and 4.0 tenants',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: SlackDest::FT_ENABLE_CUSTOM_SLACK_DYNAMIC_MESSAGE,
      notes: 'Enable users to customize variables and adjust the slack message for data alerts and schedules. This FT only works for 3.0 and 4.0 tenants',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: 'table:manage_actions',
      notes: 'Allow user adding new actions on data table by context menu',
      default_toggle_mode: 'disabled',
    },
    {
      key: DataModel::FT_SQL_GEN_CAST_TEXT_IN_CONDITION_VALUE,
      notes: 'Generate SQL query with text type casting for condition values',
      default_toggle_mode: 'enabled',
    },
    {
      key: ImageExporters::PuppeteerRunner::FT_MOVE_DASHBOARD_TITLE_TO_BOTTOM,
      notes: 'Move dashboard title to the bottom of the exported image',
      default_toggle_mode: 'disabled',
    },
    {
      key: ImageExporters::PuppeteerRunner::FT_APPLY_DASHBOARD_BG_THEME,
      notes: 'Apply dashboard background theme to the exported image',
      default_toggle_mode: 'disabled',
    },
    {
      key: AmlStudio::Project::FT_PR_WORKFLOW,
      notes: 'Enable new PR Workflow',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: AmlStudio::Project::FT_SOURCE_CONTROL_CACHE_GIT_OBJECTS,
      notes: 'Whether to cache git objects in redis',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'table:interaction_on_dashboard',
      notes: 'User can do interaction on dashboard view for table and pivot',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'aml_studio:search_and_replace',
      notes: 'User can view Search and Replace tab in AML Studio',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'viz_interaction:persist_adhoc_actions',
      notes: 'When enabled, preserves adhoc actions (like date drill and metric sheet settings) and table interactions after dashboard filter changes.',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'aql_helper',
      notes: 'AQL Helper in AQL Editor',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'table:show_data_type_icon_on_header',
      notes: 'Show data type icon on header for table and pivot',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'pivot:transpose',
      notes: 'Allow user transposing pivot table',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: 'pivot:transpose:show_context_menu_on_all_value_labels',
      notes: 'If disabled, context menu only shows in total row labels. Disabled for better performance',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: 'table:track_scrollbar_height_glitching',
      notes: 'Track scrollbar height glitching',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: 'table:show_friendly_aql_compile_error',
      notes: 'Show friendly AQL compile error message in when interacting',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: EmbedLink::FT_USE_PORTAL_CONFIGS_FOR_DASHBOARD,
      notes: 'Use new embed portal configs for embed dashboard source',
      default_toggle_mode: 'enabled',
    },
    {
      key: EmbedPortal::FT_EMBED_PORTAL_ENABLED,
      notes: 'Whether to embed portal',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: 'table:track_scrollbar_height_glitching',
      notes: 'Track scrollbar height glitching',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: Connectors::RedshiftConnector::FT_USE_LATE_BINDING_VIEW_COLS,
      notes: 'Use pg_get_late_binding_view_cols in Redshift',
      default_toggle_mode: 'enabled',
    },
    {
      key: Connectors::RedshiftConnector::FT_RETRIEVE_EXTERNAL_TABLES,
      notes: 'Retrieve external tables in Redshift',
      default_toggle_mode: 'disabled',
    },
    {
      key: 'drill_features:view_underlying_data',
      notes: 'Whether to enable view underlying data features',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: 'drill_features:drill_down',
      notes: 'Whether to enable drill down',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: ::Dashboard::FT_V4_BOTTOM_TOOLBAR,
      notes: 'Whether to place the toolbar at bottom in dashboard v4 (legacy behavior)',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: EmbedLink::FT_USE_SINGLE_EMBED_KEY,
      notes: 'Whether to use single embed key',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: ::Dashboard::FT_V4_FIT_TO_WIDTH_BY_DEFAULT,
      notes: 'Whether fit to width by default in canvas dashboard v4',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: EmbedLink::FT_NEW_EMBED_ANALYTICS_UI,
      notes: 'Whether to use new embed analytics UI',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: RowLevelPermissionRule::FT_RLP_AS_CODE,
      notes: 'RLP as code',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: 'pie_chart:remap_color_for_group_long_tails',
      notes: 'Remap color for group long tails when needed',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: AmlStudio::Project::FT_LOOKER_MIGRATOR,
      notes: 'Enable Looker Migrator',
      default_toggle_mode: 'enabled',
    },
    {
      key: ImageExporters::PuppeteerRunner::FT_ALL_VIZ_HAVE_RENDER_EVENT,
      notes: 'During exporting, assume that all viz components have render event and wait for that event to make sure the viz is fully rendered',
      default_toggle_mode: 'enabled',
    },
    {
      key: ::Dashboard::FT_V4_BLOCK_LIBRARY,
      notes: 'Enable block library',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: EmbedPortal::FT_EMBED_PORTAL_SSBI_ENABLED,
      notes: 'Whether to enable SSBI for embed portal',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: 'color_palette:skip_fallback_color_palette',
      notes: 'Skip mapping default color palette in some cases',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: Canal::Constants::FT_FORCE_ENABLE_BY_DEFAULT,
      notes: 'New data sources that have dbtype belong to Global config canal:stable_dbtypes will enabled Canal by default',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: Canal::Constants::FT_FORCE_ENABLE_FOR_SUCCESSFULLY_CONNECTED,
      notes: "Existing data sources that have settings flag #{Canal::Constants::DS_SETTINGS_SUCCESS_CONNECTION_TIMESTAMP_FLAG} will enabled Canal",
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: Canal::Constants::FT_CAST_RESULT_DATES,
      notes: 'Explicitly cast dates in query results to preserve data type after transformations like date_trunc',
      default_toggle_mode: 'enabled',
    },
    {
      key: Canal::Constants::FT_QUERY_API_STREAM_RESPONSE,
      notes: 'Enable streaming response when calling Canal Query API',
      default_toggle_mode: 'enabled',
    },
    {
      key: 'field_promotion',
      notes: 'Promote AQL adhoc fields from Reporting to Dataset level',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: 'shared_filters:always_normalize_variable_when_adding',
      notes: 'Always normalize variable name when adding shared filter',
      default_toggle_mode: 'disabled',
      js: true,
    },
    {
      key: EmbedLink::FT_EMBED_PREVIEW_WORKER,
      notes: 'Allow embed workers to be configured to use the embed preview worker for rendering embed links',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: 'exporting:export_tabs_on_canvas_dashboard',
      notes: 'Allow exporting multiple tabs on canvas dashboard exporting',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: Viz::Data::Amql::PreAggregations::FT_HANDLE_CROSS_JOIN,
      notes: 'Let Aggregate Awareness match against cross joins',
      default_toggle_mode: 'enabled',
    },
    {
      key: Viz::Constants::FT_VIZ_SETTING_UNDO_REDO,
      notes: 'Enable undo-redo feature on Viz Setting',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: ImageExporters::PuppeteerRunner::FT_HIDE_TABLE_FOOT_NOTE_WHEN_SINGLE_ROW,
      notes: 'Hide table foot note when exporting table or pivot table that only has single row',
      default_toggle_mode: 'enabled',
    },
    {
      key: ::Dashboard::FT_V4_EMBED_FLOATING_SUBMIT_BTN,
      notes: 'Whether to make the submit button floating (following viewport) in v4 embed',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: ::Dashboard::FT_V4_USE_DASHBOARD_FE_VIZ_CACHE,
      notes: 'Use dashboard level frontend cache to cache viz results',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: 'reporting:sidebar-tree-ds',
      notes: 'Use `Tree` component from Design System for Reporting Sidebar',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: Tenant::FT_EMBED_ENABLE_DASHBOARD_EXPORT_SETTING,
      notes: 'Add enable_dashboard_export to embed setting. Additionally, move the export button from the metadata to the toolbar.',
      default_toggle_mode: 'enabled',
      js: true,
    },
    {
      key: EmbedLink::FT_EMBED_DASHBOARD_USER_ATTRIBUTES,
      notes: 'allow user attributes in embedded dashboard',
      default_toggle_mode: 'enabled',
      js: true,
    },
  ].freeze
  # rubocop:enable Layout/LineLength
end
