<!--<docs>
  Form for editing report title, description and destination.
  Then call the create or update report functions
  @Input:
    - reportParams: properties of a report, can be an empty object
    - createReportFunc, updateReportFunc: callback function to create/update report
</docs>-->
<template>
  <HModal
    :shown="shown"
    title="Save Report"
    class="save-report-modal"
    data-modal-id="save_report"
    @dismiss="closeModal"
    @resolve="save"
    @update:shown="(val) => shown = val"
  >
    <validation-form
      v-show="!submitted"
      ref="form"
      as="div"
      class="saving-form"
    >
      <div
        v-show="isCreate"
        class="new-report-container"
      >
        <validation-field
          v-slot="{ errors, field }"
          tag="div"
          class="row h-form-group title"
          :class="{ 'has-error': errors && errors[0] }"
          name="Title"
          rules="required"
          :model-value="localReport.title"
          @update:model-value="v => localReport.title = v"
        >
          <div class="row h-form-group ci-title">
            <div class="col-sm-3 h-form-label pt-2">
              <span>Title</span>
              <span class="text-red-700"> *</span>
            </div>

            <div class="col-sm-9">
              <input
                v-bind="field"
                type="text"
                placeholder="Report Title"
                class="h-input h-form-input"
                :class="{'has-error': errors && errors[0]}"
              >
              <div
                v-show="errors[0]"
                class="h-form-invalid-feedback mt-1"
              >
                {{ errors[0] }}
              </div>
            </div>
          </div>
        </validation-field>
        <div class="row h-form-group description">
          <div class="col-sm-3 h-form-label pt-2">
            <span>Description</span>
          </div>
          <div
            v-click-outside="hideDescEditor"
            class="col-sm-9"
          >
            <div
              v-show="descriptionEditMode"
              class="description-editor"
            >
              <div class="flex-container-row">
                <markdown-editor
                  ref="markdown-editor"
                  v-model="localReport.description"
                />
                <HButton
                  type="primary-highlight"
                  size="sm"
                  style="margin-left: auto"
                  @click="descriptionEditMode = false"
                >
                  OK
                </HButton>
              </div>
            </div>
            <div
              v-show="!descriptionEditMode"
              @click="editDescription"
            >
              <HTooltip
                :content="'Click to edit'"
                placement="top"
              >
                <vue-markdown
                  class="vue-markdown h-form-input h-input pt-2"
                  placeholder="Report Description"
                  :source="localReport.description"
                />
              </HTooltip>
            </div>
          </div>
        </div>
        <validation-field
          v-slot="{ errors }"
          :class="{ 'has-error': errors && errors[0] }"
          :name="reportStorageName"
          rules="required|folderField"
          :model-value="destNode"
        >
          <div class="row">
            <div class="col-sm-3">
              <label
                v-show="canSaveAsExternalReport"
                class="h-form-label pt-2"
              >
                Folder/Dashboard
                <span class="text-red-700"> *</span>
              </label>
              <label
                v-show="!canSaveAsExternalReport"
                class="h-form-label pt-2"
              >
                Dashboard
                <HTooltip
                  :content="'Save result as an independent report will be supported soon'"
                  placement="top"
                >
                  <h-icon
                    name="info"
                  />
                </HTooltip>
                <span class="text-red-700"> *</span>
              </label>
            </div>
            <div class="col-sm-9">
              <resource-treeselect
                v-model="destNode"
                :types="savingDestinationTypes"
                :selectable-types="selectableTypes"
                :disable-fn="disabledNodeFn"
                class="h-form-input ci-folder"
                :has-error="errors && errors[0]"
                :custom-action="customAction"
                permission-action="update"
                @clear="() => destNode = null"
              />
              <div
                v-show="errors[0]"
                class="h-form-invalid-feedback mt-1"
              >
                {{ errors[0] }}
              </div>
              <div
                v-if="dashboardVersion === 1"
                class="h-form-description mt-1"
              >
                SQL Report (Report 2.0) can only be placed in Dashboard 2.0.
                <a
                  :href="publicUrl"
                  target="_blank"
                >Learn more</a>
              </div>
              <div
                v-if="dashboardVersion === 3"
                class="h-form-description mt-1"
              >
                Dataset Report (Report 3.0) can only be placed in Dashboard 3.0.
                <a
                  :href="publicUrl"
                  target="_blank"
                >Learn more</a>
              </div>
            </div>
          </div>
        </validation-field>
        <!-- Reason to disable banner https://app.asana.com/0/0/1203488387969939/1203592176967417/f
        <h-alert
          v-if="shouldShowTimezoneChangeWarning"
          type="info"
          class="mt-3"
        >
          <div class="font-medium">The result can be changed after saving into a specific dashboard.</div>
          Different dashboards might have different timezone settings, which can affect the final result.
        </h-alert> -->
      </div>
    </validation-form>
    <template #footer>
      <div class="flex items-center justify-end bg-blue-gray-50 px-6 py-4">
        <HButton
          v-show="!submitted"
          :disabled="submitting"
          type="secondary-default"
          @click="closeModal"
        >
          Cancel
        </HButton>
        <HButton
          v-show="!submitted"
          type="primary-highlight"
          class="ci-save-report-from-explore-modal onboarding-save-report-btn sp-tracking-btn ml-1"
          data-button-id="confirm_save_report"
          data-button-function="submit"
          :icon="submitting ? 'loading' : undefined"
          :icon-spin="submitting"
          :disabled="submitting"
          @click="save"
        >
          Save
        </HButton>
        <HButton
          v-show="submitted"
          class="ci-close-after-save-report-from-explore-modal"
          type="secondary-default"
          @click="closeModal"
        >
          Close
        </HButton>
      </div>
    </template>
  </HModal>
</template>
<script>
import { ref } from 'vue';
import { HButton, HModal, HTooltip } from '@holistics/design-system';
import { get } from 'lodash';
import { DATASET_EXPLORE_TOUR_STEPS, TOUR_NAMES } from '@/modules/Home/components/onboarding/InAppOnboarding/constants.ts';
import ResourceTreeselect from '@/core/components/ResourceTreeselect.vue';
import { check as checkFeatureToggle } from '@/core/services/featureToggle';
import { success } from '@/core/services/notifier';
import { handleAjaxError } from '@/core/services/ajax';
import VueMarkdown from '@/vue_components/vue_markdown.vue';
import MarkdownEditor from '@/vue_components/markdown_editor.vue';
import newDashboardModal from '@/modules/Dashboards/services/modals/newDashboard.modal';
import { Field as ValidationField, Form as ValidationForm } from 'vee-validate';
import eventBus, { GlobalEvents } from '@/core/services/eventBus';
import { mapActions, mapGetters } from 'vuex';
import usageReminderModal from '@/modules/AppAlerts/services/modals/usageReminder.modal';
import { getParentNodeInfo } from '@holistics/node-tree';
import generateDocsLink from '@/modules/FeatureToggleGroup/utils/generateDocsLink';
import Tenant from '@/core/models/tenant';
import { allowCreateCanvasDashboard } from '@/modules/DashboardAsCode/services/createCanvasDashboard';

export default {
  name: 'SaveReportModal',
  components: {
    HTooltip,
    HButton,
    ResourceTreeselect,
    VueMarkdown,
    MarkdownEditor,
    ValidationForm,
    ValidationField,
    HModal,
  },
  props: {
    reportParams: {
      type: Object,
      default: () => ({}),
    },
    createReportFunc: {
      type: Function,
      default: null,
    },
    updateReportFunc: {
      type: Function,
      default: null,
    },
    hasMoreThanOneDataModelInDataSet: {
      type: Boolean,
      default: false,
    },
    mode: {
      type: String,
      required: true,
    },
  },
  emits: ['resolve', 'dismiss'],
  setup () {
    const shown = ref(true);

    return { shown };
  },
  data () {
    const tenant = new Tenant();

    return {
      override: false,
      destNode: {},
      submitting: false,
      submitted: false,
      success: false,
      reportLinkAfterSave: null,
      descriptionEditMode: false,
      currentTenant: window.H.current_tenant,
      createdWidget: null,

      localReport: {
        id: null,
        title: '',
        description: '',
        can_crud: false,
      },

      // put these in data as can't use $options in test
      allowExternalDataset: checkFeatureToggle('dataset_report:allow_standalone'),
      dashboardStrictVersion: checkFeatureToggle('dashboard_widgets:strict_version'),
      dashboardV1Creation: checkFeatureToggle('dashboards_v1:creation'),
      dashboardV3Creation: checkFeatureToggle('dashboards_v3:creation'),
      allowCanvasDashboard: allowCreateCanvasDashboard() && tenant.productionEditableProjects.length > 0,
    };
  },
  computed: {
    ...mapGetters('tenantSubscription', {
      isExceedTenantUsage: 'isExceedTenantUsage',
    }),
    reportStorageName () {
      return this.canSaveAsExternalReport ? 'Folder/Dashboard' : 'Dashboard';
    },
    isSqlMode () {
      return this.mode === 'sql';
    },
    isDatasetMode () {
      return this.mode === 'dataset';
    },
    canSaveAsExternalReport () {
      return this.isSqlMode || (this.isDatasetMode && this.allowExternalDataset);
    },
    dashboardVersion () {
      if (this.dashboardStrictVersion && this.isSqlMode) return 1;
      if (this.dashboardStrictVersion && this.isDatasetMode) return 3;
      return null;
    },
    hasCreatableDashboardVersion () {
      return (
      // when strict version is disabled (dashboardVersion === null) and at least 1 dashboard creation toggle is enabled
        (this.dashboardVersion === null && (this.dashboardV1Creation || this.dashboardV3Creation))
          || (this.dashboardVersion === 1 && this.dashboardV1Creation)
          || (this.dashboardVersion === 3 && this.dashboardV3Creation)
      );
    },
    isAdhoc () {
      return this.destNode.type === 'Dashboard';
    },
    shouldShowTimezoneChangeWarning () {
      return checkFeatureToggle('new_timezone_config') && checkFeatureToggle('timezone:dashboard_timezone') && this.destNode.id && this.destNode.id !== -1;
    },
    isCreate () {
      return !this.isReportExisted || !this.override;
    },
    isReportExisted () {
      return !!this.localReport.id;
    },
    selectableTypes () {
      return this.canSaveAsExternalReport ? ['PersonalCategory', 'ReportCategory', 'Dashboard'] : ['Dashboard'];
    },
    savingDestinationTypes () {
      const types = ['Dashboard', 'PersonalCategory']; // personal workspace feature is checked inside resource_treeselect store, no need to check here

      if (this.$user.canEditInReportCategory) {
        types.push('ReportCategory');
      }

      return types;
    },
    customAction () {
      if (!this.hasCreatableDashboardVersion) {
        return undefined;
      }

      return {
        label: 'Create new dashboard',
        class: 'ci-create',
        icon: 'add',
        action: () => this.createDashboard(),
      };
    },
  },
  created () {
    this.publicUrl = generateDocsLink('/docs/holistics-product-version');
    if (this.isExceedTenantUsage) {
      this.showUsagePopup();
      return;
    }
    this.localReport = {
      ...this.localReport,
      ...this.reportParams,
      // when updating report, we will use the original title of the report. Which is `this.reportParams.title`
      // when creating the new report, we will prioritize auto-fill the title field with generated title,
      // if no then use original of the report
      title: this.reportParams.generatedTitle || this.reportParams.title,
    };
    this.destNode.id = this.reportParams.category_id;
    this.destNode.type = this.reportParams.category_type;
  },
  methods: {
    ...mapActions('nodes', ['onReportingNodeChanged']),
    ...mapActions('tree', ['fetchNodeChildren', 'refreshNodeParent']),
    editDescription () {
      this.descriptionEditMode = true;
      this.$nextTick(() => {
        if (this.$refs['markdown-editor'] && this.$refs['markdown-editor'].$refs.textarea) {
          this.$refs['markdown-editor'].$refs.textarea.focus();
        }
      });
    },
    disabledNodeFn (node) {
      if (!node.version || !this.dashboardVersion) return false;
      // better check if dashboard project is in the `tenant.productionEditableProjects`, but BE does not return this value
      if (this.allowCanvasDashboard && node.version === 4) return false;

      return this.dashboardVersion !== node.version;
    },
    close () {
      this.resolveOrDismiss();
    },
    async save () {
      try {
        this.submitting = true;
        this.submitted = false;

        if (this.isCreate && this.createReportFunc) {
          const { valid } = await this.$refs.form.validate();
          if (!valid) {
            this.submitting = false;
            return;
          }

          const createdReport = await this.createReportFunc({
            title: this.localReport.title,
            description: this.localReport.description,
            is_adhoc: this.isAdhoc,
            category_type: this.destNode.type,
            category_id: !this.isAdhoc ? this.destNode.id : -1,
            dashboard_id: this.isAdhoc ? this.destNode.id : null,
            dashboard_version: this.destNode.version,
          });
          if (createdReport?.widget) {
            this.createdWidget = createdReport.widget;
            this.createdWidget.report = createdReport;
          }

          this.success = true;
          if (createdReport) {
            this.handleUiNodeChanged(createdReport);
          }
          this.onboardingNextStep();
          this.reportLinkAfterSave = this.isAdhoc ? `/dashboards/${this.destNode.id}` : `/queries/${createdReport.id}`;
          this.resolve(createdReport);
        } else if (!this.isCreate && this.updateReportFunc) {
          const updatedReport = await this.updateReportFunc({
            title: this.reportParams.title,
          });
          this.reportLinkAfterSave = this.isAdhoc ? `/dashboards/${this.destNode.id}` : `/queries/${updatedReport.id}`;
          this.resolve(updatedReport);
        } else {
          throw new Error("Can't create or update report");
        }

        this.submitting = false;
        this.submitted = true;
      } catch (error) {
        this.submitting = false;
        this.submitted = false;
        this.closeModal();
        handleAjaxError(error);
      }
    },
    hideDescEditor () {
      this.descriptionEditMode = false;
    },
    openLink () {
      window.open(this.reportLinkAfterSave, '_blank');
    },
    closeModal () {
      this.resolveOrDismiss();
    },
    resolveOrDismiss () {
      if (this.createdWidget) {
        this.$emit('resolve', { createdWidget: this.createdWidget });
      } else {
        this.$emit('dismiss');
      }
    },
    resolve (report) {
      success(`${this.isCreate ? 'Created' : 'Updated'} report successfully!`);
      this.$emit('resolve', {
        report,
        title: this.localReport.title,
        url: this.reportLinkAfterSave,
        isCreate: this.isCreate,
        isOverride: this.override,
        createdWidget: this.createdWidget,
      });
    },
    async createDashboard () {
      try {
        const result = await newDashboardModal({ dashboardVersion: this.dashboardVersion });
        if (get(result, 'status') === 'resolved') {
          // refresh the resource_tree to fetch the new dashboard
          eventBus.$emit('resource_treeselect:refresh', false);
          const dashboard = get(result, 'data', null);
          const dashboardId = get(dashboard, 'id', null);
          if (dashboardId) {
            this.destNode = {
              id: dashboardId,
              type: 'Dashboard',
            };
          }
          success('Dashboard created successfully');
        }
      } catch (error) {
        handleAjaxError(error);
      }
    },
    handleUiNodeChanged (data) {
      const node = getParentNodeInfo(data);
      this.fetchNodeChildren({
        node,
        options: {
          isForce: true,
        },
      });
    },
    async showUsagePopup () {
      try {
        await usageReminderModal();
      } finally {
        this.closeModal();
      }
    },
    onboardingNextStep () {
      const tour = this.$tours[TOUR_NAMES.DataSetExploreTour];
      if (tour
          && tour.isRunning
          && this.hasMoreThanOneDataModelInDataSet
          && (tour.step.name === DATASET_EXPLORE_TOUR_STEPS.WaitForAddingMoreDataModels || tour.step.name === DATASET_EXPLORE_TOUR_STEPS.WaitForSavingReport)) {
        eventBus.$emit(GlobalEvents.onboardingDataSetExploreTourEnd);
      }
    },
  },
};
</script>
<style lang="postcss">
.save-report-modal {
  .h-modal-body {
    .description-editor {
      textarea {
        resize: none;
        /* border/highlight/strong: Used for highlight border */
        @apply border-blue-600;
      }
    }

    .vue-markdown {
      overflow: auto;
      max-height: 200px;
      word-wrap: break-word;
    }
  }

  .new-dashboard-btn {
    .btn {
      width: 100%;
      display: flex;
      height: 38px;
      padding: 6px 5px;
      align-items: center;
    }

    /* border/default: Used for panel border */
    @apply border-b border-gray-300;
  }

  .has-error {
    &.treeselect-wrapper .vue-treeselect__control, &.h-input {
      /* border/danger/strong: Used for error state border */
      @apply border-red-600;
    }
  }

  .vue-treeselect__option--disabled .vue-treeselect__label-container {
    /* text/default: Used for default text */
    @apply text-gray-800;
  }
}
</style>
