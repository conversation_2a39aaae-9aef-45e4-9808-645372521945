<template>
  <div
    v-if="shouldShowFooter"
    class="table-pagination pt-3"
  >
    <!--temporary hidden-->
    <div
      v-if="isInfiniteScrollEnabled"
      class="total-row-wrapper ci-total-row"
    >
      <span
        class="total-row-info"
      >
        <b>Showing max {{ shownFetchedRows }}</b> {{ pluralize('row', Number(shownFetchedRows)) }} out of  <b>{{ shownNumRows }}</b> {{ pluralize('row', Number(shownNumRows)) }}
      </span>
    </div>
    <!--change report-pagination to be v-else-if when enable total-row-info-->
    <report-pagination
      v-if="!isInfiniteScrollEnabled && pagination"
      :pagination="pagination"
      @page-changed="onPageChange"
      @page-size-changed="onPageSizeChanged"
    />
  </div>
</template>

<script setup lang="ts">
import ReportPagination from '@/modules/QueryReports/components/ReportPagination.vue';
import pluralize from '@holistics/utils/pluralize';
import { usePagination } from '../composables/pagination';
import type { DataTablePropData, Pagination, PivotTablePropData } from '../types/propData';

const props = defineProps<{
  data: DataTablePropData | PivotTablePropData
  isInfiniteScrollEnabled: boolean
  isWidget: boolean
  pagination: Pagination
}>();

const emit = defineEmits<{(e: 'paginate', value: { page: number, pageSize: number }): void,
}>();

function emitPagination (value: { page: number, pageSize: number }) {
  emit('paginate', value);
}

const {
  shouldShowFooter,
  shownFetchedRows,
  shownNumRows,
  onPageChange,
  onPageSizeChanged,
} = usePagination({
  data: props.data, pagination: props.pagination, emitPagination, isInfiniteScrollEnabled: props.isInfiniteScrollEnabled, inWidgetMode: props.isWidget,
});

</script>
