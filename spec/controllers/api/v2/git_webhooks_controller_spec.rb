# typed: false
# frozen_string_literal: false

require 'rails_helper'

describe Api::V2::GitWebhooksController, :api, type: :controller do
  include_context 'aml_studio_explicit'

  let(:pull_request_payload) do
    {
      'action' => 'closed',
      'number' => 123,
      'pull_request' => {
        'number' => 123,
        'state' => 'closed',
        'title' => 'Fix all the bugs',
        'merged' => true,
        'draft' => false,
        'html_url' => 'https://github.com/owner/repo/pull/123',
        'updated_at' => '2022-01-08T00:00:00Z',
        'merged_at' => '2022-01-08T00:00:00Z',
        'merge_commit_sha' => 'abc123',
        'head' => {
          'ref' => 'fix-bugs',
        },
        'base' => {
          'ref' => 'master',
        },
      },
      'repository' => {
        'html_url' => 'https://github.com/owner/repo',
        'ssh_url' => '**************:owner/repo.git',
      },
    }
  end

  before do
    FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_EXPLICIT_GIT, true)
    FeatureToggle.toggle_global(AmlStudio::Project::FT_EXTERNAL_GIT, true)

    git_provider = AmlStudio::ExternalGit::ParseRemoteUrl.new.call('ssh://**************/owner/repo')
    AmlStudio::Project.any_instance.stub(:git_provider).and_return(git_provider)
    project.update(settings: project.settings.merge(enabled_pr_workflow: true, is_external_integrated: true))

    # Create the integration directly instead of using the removed method
    integration = AmlStudio::ExternalGitIntegration.create!(
      provider: git_provider.provider,
      repo_url: git_provider.normalize_git_url_to_https,
    )
    project.update!(external_git_integration: integration)

    external_git_integration = project.external_git_integration
    external_git_integration.update(webhook_id: 123_456, webhook_secret: 'abcxyz',
                                    token_expiration_date: 1.day.from_now,)

    request.headers['Accept'] = 'application/json'
    request.headers['Content-Type'] = 'application/json'
    request.env['HTTP_X_GITHUB_EVENT'] = 'pull_request'
    request.env['HTTP_X_GITHUB_HOOK_ID'] = '123456'

    allow(SourceControl::Backend.passphrase_encryptor).to receive(:decrypt).and_return('webhook_secret')
  end

  # Reset routes after each test
  after do
    Rails.application.reload_routes!
  end

  def generate_webhook_signature(payload, secret = 'webhook_secret')
    signature = OpenSSL::HMAC.hexdigest(
      OpenSSL::Digest.new('sha256'),
      secret,
      payload,
    )
    "sha256=#{signature}"
  end

  describe 'POST #github_event' do
    context 'with valid pull request event' do
      it 'processes the pull request event and returns success' do
        payload_json = pull_request_payload.to_json
        request.env['HTTP_X_HUB_SIGNATURE_256'] = generate_webhook_signature(payload_json)

        post :github_event, body: payload_json, as: :json

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['message']).to eq("project with id #{project.id}: success")
      end
    end

    context 'with invalid webhook id' do
      it 'returns an error when webhook id does not match' do
        request.env['HTTP_X_GITHUB_HOOK_ID'] = '999999' # Different webhook ID
        payload_json = pull_request_payload.to_json
        request.env['HTTP_X_HUB_SIGNATURE_256'] = generate_webhook_signature(payload_json)

        post :github_event, body: payload_json, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['message']).to include("This webhook is not associated with this repository's PR workflow settings")
      end
    end

    context 'with invalid signature' do
      it 'returns an error for invalid webhook signature' do
        payload_json = pull_request_payload.to_json
        request.env['HTTP_X_HUB_SIGNATURE_256'] = 'sha256=invalid_signature'

        post :github_event, body: payload_json, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context 'when repo not found' do
      it 'returns an error when repo is not found' do
        # Change repository URL in payload
        allow(AmlStudio::GitFlows::GitClient::GithubClient).to receive(:parse_repo_urls_from_payload).and_return({
                                                                                                                   'html_url' => 'https://github.com/unknown/repo',
                                                                                                                   'ssh_url' => '**************:unknown/repo.git',
                                                                                                                 })
        payload_json = pull_request_payload.to_json
        request.env['HTTP_X_HUB_SIGNATURE_256'] = generate_webhook_signature(payload_json)

        post :github_event, body: payload_json, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['message']).to include('PR workflow is not enabled on this repository')
      end
    end

    context 'with push event' do
      it 'accepts push events' do
        request.env['HTTP_X_GITHUB_EVENT'] = 'push'

        push_payload = {
          'ref' => 'refs/heads/master',
          'repository' => {
            'html_url' => 'https://github.com/owner/repo',
            'ssh_url' => '**************:owner/repo.git',
          },
        }

        payload_json = push_payload.to_json
        request.env['HTTP_X_HUB_SIGNATURE_256'] = generate_webhook_signature(payload_json)

        # This will test if the controller properly handles push events
        # For now, let it just validate and accept the event
        post :github_event, body: payload_json, as: :json

        expect(response).to have_http_status(:ok)
      end
    end

    context 'with unsupported event type' do
      it 'rejects unsupported events', :skip_schema_conform do
        request.env['HTTP_X_GITHUB_EVENT'] = 'issues'
        payload_json = pull_request_payload.to_json
        request.env['HTTP_X_HUB_SIGNATURE_256'] = generate_webhook_signature(payload_json)

        expect do
          post :github_event, body: payload_json, as: :json
          assert_schema_conform
        end.to raise_error(Committee::InvalidRequest)
      end
    end

    context 'with pr workflow disabled' do
      it 'rejects pull request events but not throw any errors' do
        project.settings = project.settings.merge(enabled_pr_workflow: false)
        project.save!
        request.env['HTTP_X_GITHUB_EVENT'] = 'pull_request'
        payload_json = pull_request_payload.to_json
        request.env['HTTP_X_HUB_SIGNATURE_256'] = generate_webhook_signature(payload_json)

        post :github_event, body: payload_json, as: :json

        expect(response).to have_http_status(:ok)
      end
    end

    context 'with token expired' do
      it 'rejects pull request events but not throw any errors' do
        external_git_integration = project.external_git_integration
        external_git_integration.token_expiration_date = Time.now - 1.day
        external_git_integration.save!
        request.env['HTTP_X_GITHUB_EVENT'] = 'pull_request'
        payload_json = pull_request_payload.to_json
        request.env['HTTP_X_HUB_SIGNATURE_256'] = generate_webhook_signature(payload_json)

        post :github_event, body: payload_json, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['message']).to include('Token has expired, PR Workflow is temporarily disabled')
      end
    end

    context 'Unsupported Git Provider', :skip_schema_conform do
      it 'throw errors unsupported git provider' do
        request.env.delete('HTTP_X_GITHUB_EVENT')
        payload_json = pull_request_payload.to_json
        request.env['HTTP_X_HUB_SIGNATURE_256'] = generate_webhook_signature(payload_json)

        post :github_event, body: payload_json, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['message']).to include('Unsupported Git provider')
      end
    end
  end
end
