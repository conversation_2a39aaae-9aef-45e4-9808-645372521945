<template>
  <div
    class="h-highchart"
  >
    <hc-action-menu
      v-if="!inPhantom"
      :export="chartExportEnabled"
      :annotator="annotatorOpt"
      :sort="sortActionOpts"
      :allow-remove-sort="allowRemoveSort"
      @hc-export="_onExport"
      @hc-sort="_onSort"
      @hc-remove-sort="onRemoveSort"
    />

    <div
      ref="highchart"
      class="highchart"
    />
  </div>
</template>

<script>
import hcActionMenu from '@/vue_components/highcharts/action_menu.vue';
import Highcharts from '@/core/services/Highcharts/highcharts';
import sortMixin from '@/vue_mixins/highcharts/sort';
import eventBus from '@/core/services/eventBus';
import {
  isEmpty, cloneDeep, get,
} from 'lodash';
import vizRenderingMixin from '@/modules/Viz/mixins/vizRenderingMixin';

export default {
  name: 'Highcharts',
  components: { hcActionMenu },
  mixins: [sortMixin, vizRenderingMixin],
  props: {
    options: {
      type: Object,
      required: true,
    },
    annotator: {
      type: Object,
      required: false,
      default: null,
    },
    editMode: {
      type: Boolean,
      default: false,
    },
    allowRemoveSort: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['sort', 'removeSort', 'selection'],
  data () {
    return {
      opts: this.options,
      chart: null,
    };
  },
  computed: {
    annotable () {
      return !window.PHANTOM && this.annotator && this.annotator.isAnnotable;
    },
    annotatorOpt () {
      return this.annotable ? this.annotator : null;
    },
    chartExportEnabled () {
      return !window.PHANTOM && get(this.options, 'exporting.enabled', null); // need to compute from the original vizOptions passed in via props
    },
    inPhantom () {
      return !!window.PHANTOM;
    },
    resetZoomBtn () {
      return this.$refs?.highchart?.querySelector('.highcharts-reset-zoom');
    },
  },
  watch: {
    options (v) {
      this.opts = v;
      this._initChart();
      if (!isEmpty(this.opts.sort)) {
        this._execSort(this.opts.sort.option, this.opts.sort.isAscending);
      }
    },
  },
  mounted () {
    this._initChart();
    this.$watch('opts', this._renderChart, { deep: true });
    if (!isEmpty(this.opts.sort)) {
      this._execSort(this.options.sort.option, this.options.sort.isAscending);
    }
    eventBus.$on('vizSettingForm:execSort', this._onSortFromEventBus);
  },
  beforeUnmount () {
    if (this.chart) {
      this.chart.destroy();
    }
    eventBus.$off('vizSettingForm:execSort', this._onSortFromEventBus);
  },
  methods: {
    _initChart () {
      this.opts.exporting = { enabled: false };
      this._renderChart();
      this._buildSortOptsAfterRender();
    },
    _renderChart () {
      const { finishRendering, onSelection } = this;
      const options = cloneDeep(this.opts);
      this.chart = new Highcharts.Chart(this.$refs.highchart, {
        ...options,
        chart: {
          ...(options.chart || {}),
          events: {
            ...get(options, 'chart.events', {}),
            selection: onSelection,
            load (...args) {
              finishRendering();
              if (get(options, 'chart.events.load')) {
                options.chart.events.load.apply(this, ...args);
              }
            },
          },
        },
      });
      this.chart.reflow();
      if (this.annotable) this.annotator.annotate(this.chart);
    },
    _onExport ({ action, type }) {
      if (this.chart) {
        if (action === 'print') {
          // check browser support
          // https://www.highcharts.com/docs/export-module/client-side-export
          this.chart.print();
        } else {
          const mime = ({
            jpeg: 'image/jpeg',
            png: 'image/png',
            svg: 'image/svg+xml',
            pdf: 'application/pdf',
          })[type] || 'image/png';

          this.chart.exportChart({ type: mime });
        }
      }
    },
    _onSort ({ option, isAscending }) {
      this._execSort(option, isAscending);
      this.$emit('sort', { option, isAscending });
    },
    _onSortFromEventBus (params) {
      // https://holistics.slack.com/archives/C04TCTYKKHD/p1728272637022449
      // when change sort setting in viz setting form, it uses event bus to emit vizSettingForm:execSort event
      // this is dangerous since all Highcharts instances listen to this event and handle sort
      // - workaround: only handle sort in edit mode (the check below). However we cannot ensure that there is only Highcharts in editMode at a certain time, so it may not help there
      // - long term resolution: rework sorting mechanism T.T
      // Update 2024 dec 03: use viz setting id to check for edit mode (in VizResult), to fix issue in DAC editor (aml-studio)
      // - https://holistics.slack.com/archives/C04TCTYKKHD/p1732877827073339
      if (this.editMode) {
        this._onSort(params);
      }
    },
    onRemoveSort () {
      this.$emit('removeSort');
    },
    hideResetZoom () {
      if (this.resetZoomBtn) this.resetZoomBtn.remove();
    },
    showResetZoom () {
      if (!this.resetZoomBtn) this.chart.showResetZoom();
    },
    onSelection (events) {
      const xAxis = events?.xAxis?.[0];
      const yAxis = events?.yAxis?.[0];

      const selection = {
        xAxis: xAxis ? { min: xAxis.min, max: xAxis.max } : null,
        yAxis: yAxis ? { min: yAxis.min, max: yAxis.max } : null,
      };

      if (xAxis) {
        this.showResetZoom();
      } else {
        this.hideResetZoom();
      }

      this.$emit('selection', selection);
    },
    zoom ({ xAxis, yAxis }) {
      if (xAxis) {
        this.chart.xAxis[0].setExtremes(xAxis.min, xAxis.max);
        this.showResetZoom();
      }
      if (yAxis) {
        this.chart.yAxis[0].setExtremes(yAxis.min, yAxis.max);
        this.showResetZoom();
      }

      // zoom out
      if (!xAxis) {
        this.chart.xAxis[0].setExtremes(undefined, undefined);
        this.hideResetZoom();
      }
    },
  },
};
</script>

<style lang="postcss">
.h-highchart {
  .highchart {
    @apply h-full;
    .gauge-chart-hover-yaxis {
      @apply cursor-pointer;
    }
  }
}
</style>
