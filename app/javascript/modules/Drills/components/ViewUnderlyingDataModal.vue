<!-- eslint-disable vue/no-v-html -->
<template>
  <HModal
    :shown="vudModalShown"
    size="full"
    class="view-underlying-data text-sm"
    no-footer
    no-close-button
    prevent-click-outside
    :use-simple-focus-trap="false"
    @dismiss="exitModal"
    @resolve="emit('resolve')"
    @update:shown="(val) => shown = val"
    @auto-focus-on-body-resolved.prevent
  >
    <template #header>
      <div class="flex flex-row justify-between px-6 py-4">
        <div class="flex flex-row items-center">
          <div class="text-sm font-medium">
            {{ sourceTitle }}
          </div>
          <ReportWidgetConditions
            v-if="!isEmpty(originalVizFilters)"
            class="ml-2"
            :viz-conditions="originalVizFilters"
            :data-models="dataModels || []"
            :adhoc-fields="originalVizSetting.adhoc_fields"
          />
        </div>
        <div class="flex space-x-2">
          <HTooltip
            v-if="!isInDevOrEditMode"
            :disabled="!exploreHash"
            :content="copied ? 'Copied' : 'Copy link to share with others.'"
            placement="bottom"
          >
            <HButton
              :disabled="!exploreHash"
              unified
              icon="link"
              type="tertiary-default"
              size="sm"
              @click="copyCurrentUrl"
            />
          </HTooltip>
          <HSwitch
            v-model="panelState"
            label="Show visualization"
            @update:model-value="togglePanel"
          />
          <HButton
            class="ml-2"
            unified
            type="clear-default"
            icon="cancel"
            @click="exitModal"
          />
        </div>
      </div>
    </template>
    <template #body>
      <div
        class="relative flex size-full flex-col overflow-hidden rounded bg-white"
      >
        <div class="flex flex-1 flex-col overflow-hidden p-6">
          <Transition
            name="expand-collapse"
          >
            <div
              v-show="panelState"
              class="original-viz h-[45%] max-h-[45%] items-center border-b"
            >
              <div
                v-if="isToggling"
                v-h-loading.body="{ value: true, steps: [{ type: 'skeleton' }] }"
                class="flex size-full flex-col"
              />
              <div
                v-show="!isToggling"
                class="flex size-full flex-col"
              >
                <VizResult
                  ref="vizRef"
                  class="vud-original-viz-container h-full !border-none"
                  :source="vizSource"
                  :viz-setting="originalVizSetting"
                  :selected-values="drillValues"
                  allow-cross-filter
                  :data-set="dataset"
                  :project-id="extraDetails.projectId"
                  update-trigger="auto"
                  :is-production="!extraDetails.inDevMode"
                  :options="{ container: '.vud-original-viz-container', isV4: true }"
                  :loading-options="{ type: 'skeleton' }"
                  :show-date-drill-context-menu="false"
                  allow-view-underlying-data
                  show-field-details
                  @cross-filter="onCrossFilter"
                  @updated="onOriginalVizUpdated"
                  @pivoted="onPivoted"
                  @view-underlying-data="viewUnderlyingData"
                />
              </div>
            </div>
          </Transition>
          <div
            class="underlying-data-viz flex flex-1 flex-col space-y-4 overflow-hidden"
            :class="panelState ? 'mt-6' : ''"
          >
            <div class="flex flex-row justify-between">
              <div class="left-section">
                <div
                  v-if="!hasNoDataPoint && !hasDisabledReason"
                >
                  <div
                    class="mb-1 flex flex-row"
                  >
                    <div
                      class="flex w-full flex-row items-center text-xs font-medium"
                    >
                      <span class="font-medium [&_span]:!text-gray-900">
                        <FieldInfo
                          :path="drillField?.path_hash"
                          :modifier-value="drillField?.aggregation"
                          :is-adhoc-aql-field="isAdhocAqlField"
                          :field-type="drillField?.aggregation ? 'measure' : 'dimension'"
                          :custom-label="drillField?.custom_label"
                          :custom-field-show-icon="false"
                        />
                      </span>
                      <span class="font-normal">&nbsp;is&nbsp;</span>
                      <span
                        class="text size-2 rounded-full"
                        :style="`background-color: ${dataPointColor}`"
                      />
                      <span
                        v-if="drillField"
                        class="font-medium"
                      >&nbsp;{{ abbreviateOrFormatNumber(dataPointValue, drillField?.format) }}</span>
                    </div>
                  </div>
                  <div
                    class="flex flex-row items-center"
                  >
                    <div
                      v-if="appliedFilters.length"
                      class="flex"
                    >
                      <div class="font-medium">
                        where
                      </div>
                    </div>
                    <div
                      v-for="(filter, idx) in appliedFilters"
                      :key="idx"
                    >
                      <span
                        v-if="idx > 0"
                        class="ml-1"
                      >and</span>
                      <HBadge
                        :icon="''"
                        class="pointer-events-none ml-1"
                      >
                        <span
                          class="mr-1 truncate text-nowrap font-medium text-gray-900"
                          :title="filter.label"
                        >
                          {{ filter.label }}
                        </span>
                        <span>{{ filter.valuesLabel }}</span>
                      </HBadge>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="ml-2 flex flex-row items-center"
              >
                <div class="font-medium">
                  Fields shown
                </div>
                <div class="ml-2 w-[220px]">
                  <TreeSelect
                    :model-value="selectedFieldValues"
                    :options="fieldOptions"
                    multiple
                    :show-selected-values="false"
                    :max-tag-count="1"
                    :texts="{ placeholder: `${detailedFields?.length} ${pluralize('field', detailedFields?.length)}` }"
                    :disabled="hasNoDataPoint || hasDisabledReason"
                    :popper-options="{ popperClass: 'vud-fields-select' }"
                    :virtual-scrolling="false"
                    @update:model-value="updateSelectedFields"
                  >
                    <template #search-icon>
                      <span />
                    </template>
                    <template #option-label="{ option, optionLabel }">
                      <div
                        :title="option.label"
                        v-html="optionLabel"
                      />
                    </template>
                  </TreeSelect>
                </div>
                <HButton
                  class="ml-2 !no-underline"
                  type="secondary-default"
                  data-ci="explore-from-here"
                  target="_blank"
                  :disabled="!isAbleToExplore"
                  icon="explore"
                  @click="exploreData"
                >
                  Explore from here
                </HButton>
                <ExportAction
                  :data-set="dataset"
                  :viz-setting="currentVizSetting"
                  :source="{ type: 'DataSet', id: dataset?.id, title: sourceTitle }"
                  :options="['excel-data-only', 'csv', 'csv-formatted-data']"
                  :disabled="!currentVizSetting || extraDetails.inDevMode || invalidState"
                  @on-export="onExport"
                >
                  <template #default>
                    <HButton
                      class="ml-2"
                      unified
                      type="secondary-default"
                      icon="arrow-to-bottom"
                      :disabled="!currentVizSetting || extraDetails.inDevMode || invalidState"
                    />
                  </template>
                </ExportAction>
              </div>
            </div>
            <div
              v-h-loading.body="{ value: !isVizReady, steps: [{ type: 'skeleton' }] }"
              class="flex flex-1 flex-col overflow-hidden"
            >
              <div v-if="invalidState && bannerTitle">
                <HBanner
                  :title="bannerTitle"
                  type="info"
                >
                  <HButton
                    type="clear-highlightt"
                    href="https://docs.holistics.io/docs/view-underlying-data"
                    target="_blank"
                  >
                    Learn more
                  </HButton>
                </HBanner>
              </div>
              <VizResult
                v-else-if="underlyingDataVizSetting"
                ref="vizRef"
                class="vud-viz-container size-full flex-1 flex-col"
                :source="vudVizSource"
                :viz-setting="underlyingDataVizSetting"
                :data-set="dataset"
                :project-id="extraDetails.projectId"
                update-trigger="auto"
                show-context-menu
                :show-date-drill-context-menu="false"
                :infinite-scroll="true"
                :options="{ container: '.vud-viz-container' }"
                :loading-options="{ type: 'skeleton' }"
                interaction-config="vud"
                show-field-details
                @on-explore-data="exploreData"
                @transform="onVizSettingTransform"
                @viz-message="onVizMessage"
              />
            </div>
          </div>
        </div>
      </div>
    </template>
  </HModal>
</template>

<script setup lang="ts">
import {
  computed, ref, type Ref,
  watch, provide, onMounted,
} from 'vue';
import {
  HButton, HModal, HSwitch, HBadge, HBanner,
  HTooltip,
} from '@holistics/design-system';
import VizResult from '@/modules/Viz/components/VizResult.vue';
import bTypeToHType from '@/modules/DataModels/utils/bTypeToHType';
import {
  compact, debounce, isEmpty, isEqual,
  pick,
} from 'lodash';
import TreeSelect from '@holistics/tree-select';
import type {
  VizField, VizSetting, WithRequired, AmqlAdhocField,
} from '@holistics/types';
import findField from '@/modules/Viz/utils/findField';
import { addVizSetting, fetchVizSetting } from '@/modules/DataSets/services/dataSet.ajax';
import { handleAjaxError } from '@/core/services/ajax';
import extractVizSettingFields from '@/modules/Viz/services/extractVizSettingFields';
import ExportAction from '@/modules/DataSets/components/ExportAction.vue';
import { getFieldTypeIconName } from '@holistics/aml-studio/client/utils/icons';
import FieldInfo from '@/modules/Viz/components/settingForm/FieldInfo.vue';
import FieldResolver from '@/modules/Viz/services/FieldResolver';
import { buildDrillConditions, buildVizSetting } from '@/modules/Drills/services/';
import { checkAdhocAqlField } from '@/modules/Viz/utils/isAdhocAqlField';
import { abbreviateOrFormatNumber } from '@/modules/Formatting/utils';
import { getValuesLabel } from '@/modules/Viz/submodules/VizFilters/utils';
import pluralize from '@holistics/utils/pluralize';
import { useDashboard } from '@/modules/DashboardAsCode/composables/useDashboard';
import { useViewUnderlyingData } from '@/modules/DashboardAsCode/composables/useViewUnderlyingData';
import { useVizBlockStates } from '@/modules/DashboardAsCode/composables/useVizBlockStates';
import { useDashboardConfigs } from '@/modules/DashboardAsCode/composables/useDashboardConfigs';
import { useDashboardTimezone } from '@/modules/DashboardAsCode/composables/useDashboardTimezone';
import { buildFieldRef } from '@/modules/Viz/utils';
import calculateVizFieldLabel from '@/modules/Viz/utils/calculateVizFieldLabel';
import ReportWidgetConditions from '@/modules/DynamicDashboards/components/widgets/ReportWidgetConditions.vue';
import DataSet from '@/modules/DataSets/models/DataSet';
import AMLDataset from '@holistics/aml-studio/client/models/Dataset';
import {
  DatasetStore,
  createTypeChecker,
  CheckFlags,
  parse,
  getFqn,
  normalizeDatasetToAqlDataset,
  type SqlType,
} from '@holistics/amql';
import { useStore } from 'vuex';
import { type VizBlock } from '@holistics/aml-std';
import { transformVizSetting } from '@/modules/Viz/services/transformVizSetting';
import type { VizSettingTransformation } from '@/modules/Viz/types/vizSettingTransformation';
import { buildDrillValues } from '@/modules/Drills/services/buildDrillValues';
import { until, useClipboard } from '@vueuse/core';
import type DataModel from '@/modules/DataModels/models/Model';
import type { ExtractedValues } from '@/modules/Viz/utils/valuesExtractors/types';
import { MODEL_FIELD_DELIMITER } from '@/modules/Viz/utils/fieldOption';
import {
  trackEditVudTable, trackDownloadVudData, trackExploreVudData, trackShareVudUrl,
  trackVudError,
} from '../utils/trackViewUnderlyingDataUsage';

const emit = defineEmits<{(e: 'close'): void,
  (e: 'resolve'): void,
  (e: 'dismiss'): void,
}>();

const { dashboard } = useDashboard();
const { extraDetails } = useDashboardConfigs();
const { currentTimezone } = useDashboardTimezone();
const {
  vudModalShown, vizBlock, initialDrillValues, vudBlockUname, exploreHash, initialAdhocInteractions, isInDevOrEditMode,
} = useViewUnderlyingData();
const {
  vizSource, dataset: vizDataset, appliedVizSetting: originalVizSetting, appliedVizConditions: originalVizFilters, adhocInteractions,
} = useVizBlockStates(vizBlock as Ref<VizBlock>);

const dataset = computed(() => {
  if (!vizDataset.value) {
    return {};
  }
  if (vizDataset.value?.from_aml) {
    return new AMLDataset(vizDataset.value);
  }

  return new DataSet(vizDataset.value);
});

const dataModels = computed(() => {
  return dataset.value.dataModels;
});

const sourceTitle = computed(() => `Underlying data of "${vizBlock.value?.label || vizBlock.value?.uname}"`);

const panelState = ref(true);
const isToggling = ref(false);

function togglePanel () {
  isToggling.value = true;
  setTimeout(() => {
    isToggling.value = false;
  }, 600);
}

const currentUrl = computed(() => window.location.href);
const { copy, copied } = useClipboard({ source: currentUrl });

function copyCurrentUrl () {
  trackShareVudUrl({ vizType: originalVizSetting.viz_type });
  copy(currentUrl.value);
}

const shown = ref(false);
const isAddingViz = ref(false);

const baseModel = ref();
const selectedFieldValues = ref();

const originalVizFields = computed(() => {
  return extractVizSettingFields({
    vizType: originalVizSetting.value.viz_type,
    fields: originalVizSetting.value.fields,
    dataModels: dataModels.value,
    adhocFields: originalVizSetting.value.adhoc_fields,
    metrics: dataset.value?.metrics,
    aqlAdhocFields: [],
  });
});

function findVizField (fieldKey: string) {
  return originalVizFields.value.find((vizField) => {
    return fieldKey === buildFieldRef({ fieldPath: vizField?.path_hash, aggregation: vizField?.aggregation, analytic: vizField.analytic });
  });
}

function retrieveAvailabelFields (dm: DataModel) {
  const fields = dm?.fields;
  if (isEmpty(fields)) {
    return [];
  }
  return fields?.filter((f: any) => {
    return f.fieldType === 'dimension' && !f?.is_hidden && f?.type !== 'unknown';
  });
}

function findDmField (vizField: VizField, dm: any) {
  const fields = retrieveAvailabelFields(dm)?.filter((field: any) => !field.is_hidden);
  return fields.find((dmField: any) => dmField?.name === vizField.path_hash.field_name);
}

const dataModelList = computed(() => {
  return compact([baseModel.value].concat((dataModels.value || []).filter((dm: any) => dm.id !== baseModel.value?.id)));
});

const drillValues = ref(initialDrillValues);
const drillConditions = computed(() => {
  const { rows: rowConditions, columns: columnConditions } = buildDrillConditions(drillValues.value, originalVizSetting.value);
  return [...rowConditions, ...columnConditions];
});
const drillField = computed(() => {
  const field = drillValues.value?.dataPoint?.field;
  if (!field) {
    return null;
  }

  const fieldKey = buildFieldRef({ fieldPath: field.path_hash, aggregation: field?.aggregation, analytic: field?.analytic });
  const vizField = findVizField(fieldKey);

  if (!vizField) {
    return null;
  }
  return pick(vizField, ['path_hash', 'custom_label', 'type', 'format', 'aggregation']);
});

const dataPointValue = computed(() => drillValues.value?.dataPoint?.value);
const dataPointColor = computed(() => drillValues.value?.dataPoint?.color);

const detailedFields = computed(() => {
  return (selectedFieldValues.value || []).reduce((acc: VizField[], selectedFieldId: string) => {
    if (!selectedFieldId.includes(MODEL_FIELD_DELIMITER)) {
      return acc;
    }
    const [dmId, fieldName] = selectedFieldId.split(MODEL_FIELD_DELIMITER);
    if (!dmId || !fieldName) {
      return acc;
    }
    const dm = dataModels.value?.find((model: any) => model.id === dmId);
    const field = findDmField({ path_hash: { field_name: fieldName, model_id: dm?.id } } as any, dm);
    if (!field?.name) {
      return acc;
    }

    acc.push({
      format: { type: field?.type, format: {} },
      path_hash: {
        model_id: dm?.id,
        field_name: field?.name,
      },
      type: field?.type,
    });
    return acc;
  }, [] as VizField[]);
});

const hasNoDataPoint = computed(() => !dataPointValue.value && !drillField.value);
const hasDisabledReason = computed(() => {
  return drillValues.value?.dataPoint?.disabledReason;
});

const invalidState = computed<'hasNoDataPoint' | 'hasNoSelectedFields' | 'hasDisabledReason' | null>(() => {
  if (hasDisabledReason.value) {
    return 'hasDisabledReason';
  }
  if (hasNoDataPoint.value) {
    return 'hasNoDataPoint';
  }
  if (isEmpty(detailedFields.value)) {
    return 'hasNoSelectedFields';
  }

  return null;
});

const bannerTitle = computed(() => {
  switch (invalidState.value) {
    case 'hasNoDataPoint':
      return 'Please click on a data point in the visualization to view its underlying data.';
    case 'hasNoSelectedFields':
      return 'Please select at least 1 field to view the underlying data table.';
    case 'hasDisabledReason':
      return drillValues.value?.dataPoint?.disabledReason;
    default:
      return null;
  }
});

const currentVizSetting = ref();
const isAbleToExplore = computed(() => exploreHash.value && !isAddingViz.value && !extraDetails.inDevMode && !invalidState.value);

function exitModal () {
  vudBlockUname.value = undefined;
  exploreHash.value = undefined;
}

const fieldResolver = computed(() => {
  return new FieldResolver(
    null,
    dataModels.value,
    [],
    {},
    originalVizSetting.value.adhoc_fields,
    dataset.value?.metrics,
    originalVizSetting.value.amql?.adhoc_fields,
  );
});

provide('vizSettingInjection', {
  fieldResolver,
  dataSet: dataset.value,
  dataSetId: computed(() => dataset.value?.id),
});

const vudVizSource = computed(() => {
  return {
    type: 'DataSet', id: dataset.value?.id, timezone: currentTimezone.value, action: null,
  };
});

const fieldOptions = computed(() => {
  return dataModelList.value.map((dm: any, idx: number) => {
    return {
      label: dm.label,
      id: dm.id,
      icon: 'data-model',
      isExpanded: idx === 0,
      children: retrieveAvailabelFields(dm).map((f: Record<string, any>) => {
        return {
          label: f.custom_label || f.label,
          id: [dm.id, MODEL_FIELD_DELIMITER, f?.name].join(''),
          icon: getFieldTypeIconName(f, false),
        };
      }),
    };
  });
});

const tableDrillFieldCustomLabel = computed(() => {
  if (!drillField.value) {
    return null;
  }
  const displayName = calculateVizFieldLabel(drillField.value, fieldResolver.value);

  return `<span class="flex flex-row items-center">
  <span class="text size-2 rounded-full" style="display: block; margin-right: 4px; background-color: ${dataPointColor.value}"></span>
  <span class="truncate">${displayName}</span>
</span>`;
});

const underlyingDataVizSetting = computed(() => {
  if (!currentVizSetting.value || !drillField.value) {
    return null;
  }
  const newVizSetting = {
    ...currentVizSetting.value,
    fields: {
      table_fields: currentVizSetting.value.fields.table_fields?.map((field: any) => {
        const drillFieldRef = buildFieldRef({ fieldPath: drillField.value?.path_hash, aggregation: drillField.value?.aggregation, analytic: drillField.value?.analytic });
        const fieldRef = buildFieldRef({ fieldPath: field.path_hash, aggregation: field?.aggregation, analytic: field?.analytic });
        if (drillFieldRef !== fieldRef) {
          return field;
        }
        return {
          ...field,
          custom_label: tableDrillFieldCustomLabel.value || drillField.value?.custom_label,
        };
      }),
    },
  };

  return newVizSetting;
});

const datasetFieldFromDrillField = computed(() => {
  if (!drillField.value) {
    return null;
  }
  return findField(drillField.value?.path_hash, null, dataModels.value, [], originalVizSetting.value.adhoc_fields, dataset.value?.metrics, []);
});

const isAdhocAqlField = computed(() => {
  return checkAdhocAqlField(datasetFieldFromDrillField.value, originalVizSetting.value.adhoc_fields as any, originalVizSetting.value?.amql?.adhoc_fields as any);
});

const isMetric = computed(() => {
  return datasetFieldFromDrillField.value?.is_metric;
});

function syncSelectedFieldsWithVizSetting () {
  const vizFields = extractVizSettingFields({
    vizType: currentVizSetting.value.viz_type,
    fields: currentVizSetting.value.fields,
    dataModels: dataset.value?.dataModels,
    adhocFields: currentVizSetting.value.adhoc_fields,
    metrics: dataset.value?.metrics,
    aqlAdhocFields: [],
  });
  selectedFieldValues.value = vizFields.slice(1).map((vizField) => [vizField.path_hash.model_id, MODEL_FIELD_DELIMITER, vizField.path_hash.field_name].join(''));
}

const store = useStore();
const userAttributes = computed(() => {
  const attributes = store.state.userAttributes.userAttributes;

  return attributes?.map((attr: any) => ({
    name: attr.name,
    type: attr.attribute_type as SqlType,
  })) ?? [];
});

function resolveBaseModel () {
  if (!drillField.value) {
    return null;
  }
  let modelId: any;
  try {
    if (isAdhocAqlField.value || isMetric.value) {
      const aqlDataset = normalizeDatasetToAqlDataset({ ...dataset.value, vizSetting: originalVizSetting.value } as any);
      const datasetStore = new DatasetStore(aqlDataset, aqlDataset.dataSource, { allowAmbiguousPaths: false });
      const aqlProgram = parse(datasetFieldFromDrillField.value.sql);

      const adhocMetricDeclarations = (originalVizSetting.value.adhoc_fields ?? [])
        .map((metric: any) => `metric ${metric.name} = ${metric.sql}`);

      const adhocDimensionDeclarations = (originalVizSetting.value.amql?.adhoc_fields ?? [])
        .filter((dimension: any): dimension is WithRequired<AmqlAdhocField, 'model_id'> => Boolean(dimension.model_id))
        .map((dimension: any) => `dimension ${getFqn(dimension.model_id)}.${dimension.field.name} = ${dimension.field.sql}`);

      const fieldDeclarations: any[] = compact([...adhocMetricDeclarations, ...adhocDimensionDeclarations].map((aql) => {
        try {
          const expr = parse(aql).exprs[0];
          return expr;
        } catch (error) {
          return undefined;
        }
      }));

      const checker = createTypeChecker(
        datasetStore,
        userAttributes.value,
        fieldDeclarations,
        [],
        CheckFlags.Normal,
      );
      checker.getDiagnostics(aqlProgram);
      const dfg = checker.getFieldDependencyGraph();
      const models = [...dfg.keys()]
        .filter(key => key.includes('->'))
        .map(key => key.slice(0, key.indexOf('->')));
      modelId = models[0];
    } else {
      modelId = drillField.value?.path_hash?.model_id;
    }
  } catch (error) {
    handleAjaxError(error);
  }
  return dataModels.value?.find((dm: any) => dm.id === modelId);
}

const appliedFilters = computed(() => {
  return (drillConditions.value || []).reduce((acc: any, condition: any) => {
    const { path_hash: pathHash, type } = condition;
    const { model_id: modelId } = pathHash;
    let label;
    let fieldType = type;
    const vizField = findVizField(buildFieldRef({ fieldPath: condition?.path_hash, aggregation: condition?.aggregation }));
    if (!vizField) {
      return acc;
    }

    if (modelId) {
      const dataModel = dataModels.value.find((dm: any) => dm.id === condition.path_hash.model_id);
      const fields = dataModel?.fields;
      if (!fields) {
        return acc;
      }
      const dmField = fields?.find((f: any) => f?.name === condition.path_hash.field_name);
      if (!dmField) {
        return acc;
      }
      label = dmField?.label;
      fieldType = bTypeToHType(dmField.type);
    } else {
      label = vizField?.label;
      fieldType = bTypeToHType(vizField?.type);
    }

    let { values } = condition;
    const { options } = condition;
    if (options?.raw_values?.[0]) {
      values = options?.raw_values.map((v: any) => abbreviateOrFormatNumber(v, vizField.format as any));
    }

    acc.push({
      label: vizField?.custom_label || label,
      valuesLabel: getValuesLabel(
        {
          ...condition,
          values,
        },
        fieldType,
      ),
    });

    return acc;
  }, [] as Record<string, any>);
});

function onExport (_jobId: number, options: { format: string }) {
  trackDownloadVudData({ vizType: originalVizSetting.value.viz_type, additionalProperties: [{ key: 'download_format', value: options.format }] });
}

function exploreData () {
  if (!isAbleToExplore.value) {
    return;
  }
  trackExploreVudData({ vizType: originalVizSetting.value.viz_type });
  window.open(`${dashboard.value.path}?_expl=${vizBlock?.value?.uname}&_eshash=${exploreHash.value}`, '_blank');
}

function updateSelectedFields (values: [] | number | string) {
  if (!values) {
    selectedFieldValues.value = [];
  } else if (Array.isArray(values)) {
    selectedFieldValues.value = values.map((v: any) => v.id);
  } else {
    selectedFieldValues.value = [values].concat(fieldOptions.value?.find((dmOption) => dmOption.id === values)?.children?.map((f) => f.id) || []);
  }

  currentVizSetting.value = buildVizSetting({
    originalVizSetting: originalVizSetting.value,
    currentVizSetting: currentVizSetting.value,
    detailedFields: detailedFields.value,
    drillField: drillField.value,
    drillConditions: drillConditions.value,
  });
}

const createVizSetting = debounce(async (vizSetting: VizSetting) => {
  if (extraDetails.inDevMode || !dataset.value?.id) {
    return;
  }
  try {
    isAddingViz.value = true;
    const { hashid } = await addVizSetting(dataset.value.id, vizSetting);
    exploreHash.value = hashid;
  } catch (error: any) {
    handleAjaxError(error);
  } finally {
    isAddingViz.value = false;
  }
}, 1000);

watch(() => currentVizSetting.value, () => {
  if (currentVizSetting.value?.hashid || isEmpty(detailedFields.value)) {
    return;
  }
  if (currentVizSetting.value) {
    createVizSetting(currentVizSetting.value);
  }
}, { immediate: true });

const isVizReady = ref(false);

function loadDataSet () {
  return until(dataModels).toMatch((v) => !isEmpty(v));
}

async function buildDrillData () {
  if (drillValues.value?.dataPoint?.disabledReason) {
    return;
  }
  await loadDataSet();
  baseModel.value = resolveBaseModel();
  updateSelectedFields(baseModel.value?.id);
  isVizReady.value = true;
}

function onCrossFilter (payload: { rawValues: ExtractedValues }) {
  drillValues.value = payload.rawValues;
  buildDrillData();
}

function viewUnderlyingData (extractedValues: ExtractedValues) {
  if (isEqual(extractedValues, drillValues.value)) {
    return;
  }
  drillValues.value = extractedValues;
  buildDrillData();
}

function onVizMessage (vizMessage: { type: 'error' | any, message: string }) {
  if (vizMessage?.type !== 'error') {
    return;
  }
  trackVudError({
    vizType: originalVizSetting.value.viz_type,
    errorMessage: vizMessage.message,
  });
}

function onVizSettingTransform (transformVizSettingPayload: VizSettingTransformation) {
  if (currentVizSetting.value) {
    trackEditVudTable({ vizType: originalVizSetting.value.viz_type, additionalProperties: [{ key: 'table_interaction_type', value: transformVizSettingPayload.type }] });
    currentVizSetting.value = transformVizSetting(currentVizSetting.value, transformVizSettingPayload);
    syncSelectedFieldsWithVizSetting();
  }
}

const pivotData = ref();
const hasInitialUrlParams = ref(false);

async function onOriginalVizUpdated (vizOptions: Record<string, any>) {
  if (!hasInitialUrlParams.value) {
    return;
  }
  await loadDataSet();
  drillValues.value = buildDrillValues({
    vudVizSetting: currentVizSetting.value,
    originalVizSetting: originalVizSetting.value,
    vizOptions,
    pivotData: pivotData.value,
    dataset: dataset.value,
  });

  if (isEmpty(drillValues.value)) {
    return;
  }
  syncSelectedFieldsWithVizSetting();
  isVizReady.value = true;
}

function onPivoted (pivoted: Record<string, any>) {
  if (!hasInitialUrlParams.value) {
    return;
  }
  if (['data_table'].includes(originalVizSetting.value.viz_type)) {
    return;
  }
  pivotData.value = pivoted;
}

async function fetchVudVizSetting () {
  try {
    currentVizSetting.value = await fetchVizSetting(dataset.value?.id, exploreHash.value);
  } catch (error) {
    handleAjaxError(error);
  }
}

onMounted(async () => {
  if (exploreHash.value) {
    hasInitialUrlParams.value = true;
    await fetchVudVizSetting();
  } else {
    adhocInteractions.value = initialAdhocInteractions.value;
    drillValues.value = initialDrillValues.value;
    buildDrillData();
  }
});
</script>

<style lang="postcss">
.original-viz {
  div[data-hui-section="body"] {
    @apply h-full;
    > div {
      @apply h-full;
    }
  }
}
.view-underlying-data {
  @apply !z-[1039];

  &>div {
    @apply !h-full;
  }

  .v-loading-border.run {
    @apply !top-0;
  }

  .tree-select-control {
    input::placeholder {
      @apply text-blue-gray-900;
    }
    input:focus::placeholder {
      @apply text-blue-gray-400;
    }
  }
}

.vud-fields-select {
  .tree-select-item {
    &:not(.branch) {
      @apply !ml-8 !pl-0;
    }
    .tree-select-item-content {
      @apply !items-start;
    }
  }
}

.expand-collapse-enter-active,
.expand-collapse-leave-active {
  @apply transition-all duration-300 ease-in-out;
  overflow: hidden;
}

.expand-collapse-enter-from,
.expand-collapse-leave-to {
  height: 0;
  opacity: 0;
}

.expand-collapse-enter-to,
.expand-collapse-leave-from {
  height: 45%;
  opacity: 1;
}
</style>
