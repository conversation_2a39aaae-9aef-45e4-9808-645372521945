operationId: Ai_Chat
summary: Chat with AIssistant
tags:
  - AI
requestBody:
  content:
    application/json:
      schema:
        type: object
        properties:
          query:
            type: string
          conversation_id:
            type: string
          skip_job:
            type: boolean
          agent:
            type: object
            oneOf:
              - $ref: ../../components/schemas/ai/AgentPhoebe.yml
              - $ref: ../../components/schemas/ai/AgentDocsearch.yml
              - $ref: ../../components/schemas/ai/AgentMeg.yml
            discriminator:
              propertyName: _name
              mapping:
                phoebe: ../../components/schemas/ai/AgentPhoebe.yml
                docsearch: ../../components/schemas/ai/AgentDocsearch.yml
                meg: ../../components/schemas/ai/AgentMeg.yml
                aqua: ../../components/schemas/ai/AgentPhoebe.yml
                scout: ../../components/schemas/ai/AgentScout.yml
                comme: ../../components/schemas/ai/AgentComme.yml
        required:
          - query
responses:
  "200":
    $ref: ../../components/responses/AiAnswerOrAsyncResult.yml
  "422":
    $ref: ../../components/responses/errors/BaseError.yml