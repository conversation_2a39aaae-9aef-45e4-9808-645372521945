<template>
  <div
    v-if="$_dashboard && $_dashboard.id"
    class="h-dashboard-controls flex items-center"
  >
    <div
      v-if="($user.isAdmin || $user.isAnalyst) && $_isLocked"
      class="control-section text-gray-800"
    >
      <HTooltip
        :content="'This report is locked. Only owner/admins can modify it'"
        placement="top"
      >
        <h-icon
          name="lock"
        />
      </HTooltip>
    </div>
    <dashboard-timezone
      class="control-section ci-query-processing-timezone mr-2"
      :dashboard="$_dashboard"
      @timezone-changed="onTimezoneChanged"
    />
    <div class="control-section flex h-6 items-center">
      <!-- Add button: Add new Widget / Add Filter -->
      <HTooltip
        v-if="$_crudActionsVisibility"
        :content="$_crudActionsTooltipText"
        placement="top"
      >
        <widget-dropdown
          :disabled="!$_permissions.can_update && $_crudActionsVisibility"
          class="mr-1"
        />
      </HTooltip>

      <!-- Export button: PDF,PNG / Schedule -->
      <HTooltip
        v-if="$_permissions.can_read_schedule || $_permissions.can_export || $_crudActionsVisibility"
        :content="$_crudActionsTooltipText"
        placement="top"
      >
        <export-dropdown
          :disabled="!($_permissions.can_read_schedule || $_permissions.can_export) && $_crudActionsVisibility"
          :dashboard="$_dashboard"
          class="ci-schedule-dropdown mr-1"
          @export="doExport"
        />
      </HTooltip>

      <!-- Share button: User Access/ Shareable Links -->
      <HTooltip
        v-if="$_permissions.can_share || $_crudActionsVisibility"
        :content="$_crudActionsTooltipText"
        placement="top"
      >
        <share-dropdown
          :disabled="!$_permissions.can_share && $_crudActionsVisibility"
          :resource="$_dashboard"
          :resource-type="'Dashboard'"
          :dashboard-filter-conditions="dashboardFilterConditions"
          :menu-right="true"
          class="mr-1"
        >
          <h-icon
            name="share"
            class="align-text-top"
          /> Share
        </share-dropdown>
      </HTooltip>
      <HTooltip
        v-if="$_crudActionsVisibility"
        :content="$_crudActionsTooltipText || 'More'"
        placement="top"
      >
        <more-dropdown
          :disabled="!$_permissions.can_update && $_crudActionsVisibility"
          :resource="$_dashboard"
          :resource-type="'Dashboard'"
          :dashboard-filter-conditions="dashboardFilterConditions"
          class="inline-block"
        />
      </HTooltip>
    </div>
    <div class="control-section">
      <toggle-favourite
        v-if="!$user.isPublicUser"
        :source-id="$_dashboard.id"
        source-type="Dashboard"
        type="tertiary-default"
      />

      <comments-panel
        v-if="commentingEnabled"
        target-type="Dashboard"
        :target-id="$_dashboard.id"
        class="dashboard-comments inline-block"
      />
      <HButton
        v-if="$options.codeGenEnabled && $_permissions.can_update"

        type="secondary-default"
        class="ml-2"
        label="Convert to Canvas"
        icon="canvas"
        @click="generateDashboardAsCode"
      />
    </div>
  </div>
</template>

<script>
import timezoneService from '@/modules/DynamicDashboards/services/timezoneService';
import DashboardTimezone from '@/modules/DynamicDashboards/components/DashboardTimezone.vue';
import ShareDropdown from '@/core/components/ShareDropdown.vue';
import { buildWidgetVizConditions } from '@/modules/DynamicFilters/utils';
import { submitExport } from '@/modules/DynamicDashboards/services/dashboards.ajax';
import { check as checkFeatureToggle } from '@/core/services/featureToggle';
import CommentsPanel from '@/vue_components/comments/comments_panel.vue';
import ToggleFavourite from '@/core/components/ui/ToggleFavourite.vue';
import { buildExportWidgetVizConditions } from '@/modules/DynamicFilters/utils/buildExportWidgetVizConditions';
import { HButton, HIcon, HTooltip } from '@holistics/design-system';
import MoreDropdown from './dropdowns/MoreDropdown.vue';
import WidgetDropdown from './dropdowns/WidgetDropdown.vue';
import ExportDropdown from './dropdowns/ExportDropdown.vue';
import dashboardInjection from '../mixins/dashboardInjection';
import { generateDashboardAsCode } from '../services/modals/generateDashboardAsCode';

export default {
  name: 'DashboardControls',
  components: {
    HTooltip,
    ExportDropdown,
    WidgetDropdown,
    MoreDropdown,
    ShareDropdown,
    DashboardTimezone,
    CommentsPanel,
    ToggleFavourite,
    HButton,
    HIcon,
  },
  codeGenEnabled: checkFeatureToggle('dashboards_v4:codegen'),
  mixins: [dashboardInjection],
  data () {
    return {
      isOnboardingReady: false,
    };
  },
  computed: {
    dashboardFilterConditions () {
      return this.$_filters.conditions;
    },
    hasAnyActions () {
      const {
        $_permissions: permissions,
        $_crudActionsVisibility: crudActionsVisibility,
      } = this;
      return crudActionsVisibility || permissions.can_update || permissions.can_share || permissions.can_read_schedule || permissions.can_export;
    },
  },
  created () {
    this.commentingEnabled = checkFeatureToggle('commenting') && !window.H.current_user.is_public_user;
  },
  methods: {
    async doExport (type) {
      let widgetVizConditions = [];
      if (checkFeatureToggle('exportings:allow_include_cross_filter_on_dynamic_dashboard')) {
        const widgetWithDataSetId = Object.keys(this.$_modelingSources).reduce((acc, widgetId) => {
          if (widgetId && this.$_modelingSources[widgetId]?.dataSet?.id) {
            acc[widgetId] = this.$_modelingSources[widgetId]?.dataSet?.id;
          }
          return acc;
        }, {});

        widgetVizConditions = buildExportWidgetVizConditions(
          widgetWithDataSetId,
          this.$_filters.dynamicFilters,
          this.$_filters.conditions,
          this.$_dashboard.cross_filtering_enabled,
          this.$_crossFilter,
        );
      } else {
        widgetVizConditions = buildWidgetVizConditions(this.$_dashboard.widgets, this.$_filters.dynamicFilters, this.$_filters.conditions);
      }

      const queryProcessingTimezone = checkFeatureToggle('new_timezone_config') ? timezoneService.getTimezone() : null;

      this.$exporter.startExportJob({
        sourceId: this.$_dashboard.id,
        sourceTitle: this.$_dashboard.title,
        format: type,
        submitFunc: () => submitExport(this.$_dashboard.id, type, widgetVizConditions, queryProcessingTimezone),
      });
    },
    onTimezoneChanged () {
      this.$_events.$emit('refresh:run');
    },
    generateDashboardAsCode () {
      return generateDashboardAsCode({
        dashboard: this.$_dashboard,
        filters: this.$_filters,
        widgetModelingSources: this.$_modelingSources,
      });
    },
  },
};
</script>
<style lang="postcss" scoped>
.h-dashboard-controls {
  .control-section {
    @apply px-1 flex items-center;

    &:not(:first-child):not(:empty) {
      @apply border-l;
    }
  }
}
</style>
