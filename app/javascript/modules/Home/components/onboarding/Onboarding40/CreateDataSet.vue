<!--<docs>
  # Usage
    Use to create the first dataset for onboarding tenant. Only support 4.0 or above tenant.
</docs>-->

<template>
  <div class="flex w-full">
    <div
      class="flex flex-[2] items-center justify-center"
    >
      <div class="create-data-set-onboarding flex flex-col">
        <div
          class="text-wrapper"
        >
          <p class="text-xl font-medium">
            Build a Dataset from Data Models
          </p>
          <p class="mt-2 text-gray-700">
            Dataset is a collection of data models. Dataset is used to build reports, or explore data.
            <a
              :href="docsUrl"
              target="_blank"
            >
              Learn more
            </a>
          </p>
          <p class="mt-2 text-gray-700">
            Try creating a dataset by selecting one or more related data model(s).
          </p>
        </div>
        <div class="create-data-set-form bg-white">
          <create-dataset-form
            ref="modelForm"
            v-model="clonedDataSet"
            :should-show-error="triedSubmit"
            @show-create-data-model-screen="handleShowCreateDataModelScreen"
          />
          <div class="pb-2 pt-11 text-right">
            <HButton
              class="create-dataset w-[202px]"
              type="primary-highlight"
              data-ci="ci-create-dataset"
              :disabled="createDisabled"
              @click="submitAMLDataset"
            >
              <h-icon
                v-if="submitting"
                name="circle-notch"
                spin
              />
              Next Step
            </HButton>
          </div>
        </div>
      </div>
    </div>
    <div class="illustration-wrapper flex flex-[1] content-end justify-center bg-blue-50">
      <img
        class="ml-14"
        src="https://cdn.holistics.io/assets/skeleton-dataset.svg"
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { HButton } from '@holistics/design-system';
import { computed, ref } from 'vue';
import { handleAjaxError } from '@/core/services/ajax';
import generateDocsLink from '@/modules/FeatureToggleGroup/utils/generateDocsLink';
import { useStore } from 'vuex';
import { DataSetService } from '@aml-studio/connector/services';
import { slugifyStringByUnderscore } from '@/modules/Formatting/utils';
import { getDefaultDataSource } from '@/modules/DataSources/utils';
import * as Sentry from '@sentry/browser';
import CreateDatasetForm from './CreateDatasetForm.vue';

const store = useStore();
const emits = defineEmits(['created', 'showCreateDataModelScreen']);
const docsUrl = generateDocsLink('/docs/datasets');

const props = withDefaults(defineProps<{
  dataSourceId?: string
}>(), {
  dataSourceId: getDefaultDataSource(),
});

const clonedDataSet = ref({
  joinConfigs: [],
  selectedModelIds: [],
  dataModels: [],
  dataSourceId: props.dataSourceId,
  categoryId: 0,
  title: 'my_first_dataset',
});

const submitting = ref(false);
const triedSubmit = ref(false);
const modelForm = ref<{ valid: boolean } | null>(null);

const currentProjectId = computed(() => store.getters['aml/project/currentProjectId']);
const createDisabled = computed(() => triedSubmit.value && !modelForm.value?.valid);
const createAMlDatasetParams = computed(() => {
  const modelPaths = clonedDataSet.value.dataModels.map((model: {name: string}) => ({
    model_name: model.name,
    file_path: `/models/${model.name}.model.aml`,
  }));
  return {
    dataSourceId: clonedDataSet.value.dataSourceId,
    modelPaths,
    name: slugifyStringByUnderscore(clonedDataSet.value.title), // replace all special characters by underscore
    projectId: currentProjectId.value,
    path: 'datasets',
    label: '',
  };
});

function handleShowCreateDataModelScreen () {
  emits('showCreateDataModelScreen');
}

async function submitAMLDataset () {
  triedSubmit.value = true;
  if (!modelForm.value?.valid) {
    return;
  }
  submitting.value = true;
  try {
    const result = await DataSetService.createDataSet({ ...createAMlDatasetParams.value, targetAmlEnv: 'aml_studio' });
    if (!result.success) throw new Error(result.errors[0]);
    const dataSetPath = `datasets/${createAMlDatasetParams.value.name}.dataset.aml`;
    const amlDatasetLink = `/studio/projects/${currentProjectId.value}/explore/${dataSetPath}`;
    emits('created', {
      dataSetPath,
      projectId: currentProjectId.value,
      dataSetLink: amlDatasetLink,
      modelCount: (createAMlDatasetParams.value.modelPaths || []).length,
    });
  } catch (err) {
    Sentry.captureException(err);
    handleAjaxError(err, 'Failed to create dataset');
  } finally {
    submitting.value = false;
  }
}
</script>

<style lang="postcss" scoped>
.onboarding-progress {
  margin-top: 0 !important;
  min-height: calc(100vh - 124px);
}

.create-data-set-onboarding {
  width: 678px;
  .text-wrapper {
    margin-bottom: 32px;
  }
  .create-data-set-form {
    height: 100%;
    width: 100%;
    /* Using border-radius-sm (3px) from variables */
    border-radius: 0 3px 3px 0;

    .tree-select-wrapper {
      height: 320px;
    }
  }
}
</style>
