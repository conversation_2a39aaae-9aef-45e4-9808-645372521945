# typed: true

module Api::V2
  class GitWebhooksController < ApiController
    skip_before_action(
      :authenticate_user!,
      :verify_authentication!,
      :check_rate_limit,
      :authenticate_user_from_token!,
      only: [:github_event],
    )

    def github_event
      check_token_expiration!
      verify_github_webhook_event!

      messages = ::AmlStudio::PrWorkflow::Events::EventHandler.new(
        git_client_class: git_client_class,
        event_type: request.env['HTTP_X_GITHUB_EVENT'],
        payload: payload,
        external_git_integration: external_git_integration,
      ).call

      render_json(
        { message: messages&.join(', ') || 'Event processed successfully' },
        status: :ok,
      )
    end

    private

    def check_token_expiration!
      if T.must(external_git_integration.token_expiration_date) < Time.now
        raise Holistics::InvalidOperation,
              'Token has expired, PR Workflow is temporarily disabled'
      end
    end

    def verify_github_webhook_event!
      unless webhook_id == external_git_integration.webhook_id
        raise Holistics::InvalidOperation,
              "This webhook is not associated with this repository's PR workflow settings"
      end
      signature = request.env['HTTP_X_HUB_SIGNATURE_256']
      webhook_secret = SourceControl::Backend.passphrase_encryptor.decrypt(T.must(external_git_integration.webhook_secret))

      unless git_client_class.validate_webhook_signature(request.raw_post, signature, webhook_secret)
        raise Holistics::InvalidOperation, 'Invalid webhook signature'
      end
    end

    # TODO: more git providers need to get the webhook id from the request headers differently
    sig { returns(Integer) }
    def webhook_id
      if github_webhook?
        request.env['HTTP_X_GITHUB_HOOK_ID'].to_i
      else
        raise Holistics::InvalidOperation, 'Unsupported Git provider'
      end
    end

    sig { returns(T::Hash[String, T.untyped]) }
    def payload
      @payload ||= JSON.parse(request.raw_post)
    end

    sig { returns(::AmlStudio::ExternalGitIntegration) }
    def external_git_integration
      repo_urls = git_client_class.parse_repo_urls_from_payload(payload)
      string_keys_repo_urls = repo_urls.transform_keys(&:to_s)
      html_url = T.must(string_keys_repo_urls['html_url'])
      @external_git_integration ||= ::AmlStudio::ExternalGitIntegration.find_by!(repo_url: html_url)
    rescue ActiveRecord::RecordNotFound
      raise Holistics::InvalidOperation, 'PR workflow is not enabled on this repository'
    end

    # TODO: make the return to T.any to add more git providers here
    sig { returns(T.class_of(::AmlStudio::GitFlows::GitClient::GithubClient)) }
    def git_client_class
      @git_client_class ||= if github_webhook?
                              ::AmlStudio::GitFlows::GitClient::GithubClient
                            else
                              raise Holistics::InvalidOperation, 'Unsupported Git provider'
                            end
    end

    sig { returns(T::Boolean) }
    def github_webhook?
      request.env['HTTP_X_GITHUB_EVENT'].present?
    end
  end
end
