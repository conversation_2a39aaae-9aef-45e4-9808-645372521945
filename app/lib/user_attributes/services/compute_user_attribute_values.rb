# typed: true
# frozen_string_literal: true

module UserAttributes
  module Services
    class ComputeUserAttributeValues < T::Struct
      include Service
      include ServiceContexts::CurrentUser

      sig { params(subject: UserAttributes::Types::Subject).returns(UserAttributes::Types::ValuesMap) }
      def call(subject)
        return compute_public_user_values(subject) if subject.is_a?(User) && subject.public_user?

        built_in_values = compute_built_in_values(subject)
        entries_values = compute_entries_values(subject)
        user_values = entries_values.merge(built_in_values)
        impersonator_values = compute_impersonator_values(subject)

        if impersonator_values
          intersect_values(user_values, impersonator_values)
        else
          user_values
        end
      end

      private

      sig { params(user: User).returns(UserAttributes::Types::ValuesMap) }
      def compute_public_user_values(user)
        # Embed Portal
        user = T.let(current_user, User) if user.id == current_user&.id
        embed_v3_configs = user.embed_v3_configs

        return embed_v3_configs.user_attributes if embed_v3_configs.is_a? EmbedLinks::Values::EmbedPortalConfigs

        # Public users do not have any Attribute values, use those of the sharer instead
        call(T.must(user.sharer))
      end

      sig { params(subject: UserAttributes::Types::Subject).returns(UserAttributes::Types::ValuesMap) }
      def compute_built_in_values(subject)
        ComputeBuiltInAttributeValues.new.call(subject)
      end

      sig { params(subject: UserAttributes::Types::Subject).returns(UserAttributes::Types::ValuesMap) }
      def compute_entries_values(subject)
        user_attributes = T.let(
          UserAttribute.where(tenant_id: subject.tenant_id).pluck(:id, :name, :attribute_type).to_h do |(id, name, type)|
            [id, [name, type]]
          end,
          T::Hash[Integer, [String, String]],
        )

        entries = UserAttributeEntry.fetch_for_subject(subject).to_a

        group_entries = entries.select { |e| e.subject_type == 'Group' }
        user_entries = entries.select { |e| e.subject_type == 'User' }

        user_values = user_entries.each_with_object({}) do |entry, all_values|
          all_values = T.let(all_values, UserAttributes::Types::ValuesMap)

          attribute_name, attribute_type = user_attributes[entry.user_attribute_id]
          attribute_type = Modeling::Field::Type.deserialize(attribute_type)
          next unless attribute_name

          case entry.entry_type
          when UserAttributeEntry::ENTRY_TYPE_INHERIT
            next
          when UserAttributeEntry::ENTRY_TYPE_MANUAL
            all_values[attribute_name] = [entry.values, attribute_type]
          when UserAttributeEntry::ENTRY_TYPE_ALL
            all_values[attribute_name] = [UserAttributes::Types::ConstantValue::All, attribute_type]
          else
            raise NotImplementedError
          end
        end

        # if admin, use ALL as a default value if not set
        if subject.is_a?(User) && subject.admin?
          user_attributes.each_value do |attribute_name, attribute_type|
            user_values[attribute_name] ||= [UserAttributes::Types::ConstantValue::All, Modeling::Field::Type.deserialize(attribute_type)]
          end
        end

        group_values = group_entries.each_with_object({}) do |entry, all_values|
          all_values = T.let(all_values, UserAttributes::Types::ValuesMap)

          attribute_name, attribute_type = user_attributes[entry.user_attribute_id]
          attribute_type = Modeling::Field::Type.deserialize(attribute_type)
          next unless attribute_name
          next if user_values[attribute_name]

          attribute_values, type = all_values[attribute_name] || [[], attribute_type]
          next if attribute_values == UserAttributes::Types::ConstantValue::All

          attribute_values = [] if attribute_values == UserAttributes::Types::ConstantValue::Unset

          case entry.entry_type
          when UserAttributeEntry::ENTRY_TYPE_MANUAL
            all_values[attribute_name] = [attribute_values + entry.values, attribute_type]
          when UserAttributeEntry::ENTRY_TYPE_ALL
            all_values[attribute_name] = [UserAttributes::Types::ConstantValue::All, attribute_type]
          else
            raise NotImplementedError
          end
        end

        user_attributes.values.each_with_object({}) do |(attribute_name, attribute_type), map|
          map[attribute_name] =
            user_values[attribute_name] ||
            group_values[attribute_name] ||
            [[], Modeling::Field::Type.deserialize(attribute_type)]
        end
      end

      sig { params(subject: UserAttributes::Types::Subject).returns(T.nilable(UserAttributes::Types::ValuesMap)) }
      def compute_impersonator_values(subject)
        return nil unless subject.is_a?(User)
        current_user = ThreadContext.get_or_nil(:current_user)
        if subject.id == current_user&.id
          impersonator = ThreadContext.get_or_nil(:impersonator)
          if impersonator && impersonator.id != current_user.id && !impersonator.super_admin?
            call(impersonator)
          end
        end
      end

      # NOTE: built-in attribute values are kept from primary_values_map
      sig do
        params(
          primary_values_map: UserAttributes::Types::ValuesMap,
          secondary_values_map: UserAttributes::Types::ValuesMap,
        ).returns(UserAttributes::Types::ValuesMap)
      end
      def intersect_values(primary_values_map, secondary_values_map)
        result_values_map = T.let({}, UserAttributes::Types::ValuesMap)

        primary_values_map.each do |key, primary_values|
          if UserAttribute.built_in_name?(key)
            result_values_map[key] = primary_values
            next
          end

          secondary_values = secondary_values_map[key] || [[], primary_values[1]]

          result_values_map[key] =
            case (primary = primary_values[0])
            when UserAttributes::Types::ConstantValue::All, UserAttributes::Types::ConstantValue::Unset
              secondary_values
            when Array
              case (secondary = secondary_values[0])
              when UserAttributes::Types::ConstantValue::All, UserAttributes::Types::ConstantValue::Unset
                primary_values
              when Array
                [primary & secondary, primary_values[1]]
              end
            end
        end

        result_values_map
      end
    end
  end
end
