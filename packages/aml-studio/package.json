{"name": "@holistics/aml-studio", "version": "1.0.0", "main": "index.js", "dependencies": {"@holistics/aml": "4.6.0-alpha.5", "@holistics/aml-editor": "2.16.0-alpha.5", "@holistics/aml-std": "2.62.0", "@holistics/design-system": "2.41.2", "@holistics/ds": "1.0.0", "@holistics/icon": "0.13.5", "@holistics/node-tree": "1.0.0", "@holistics/tree-select": "1.0.0", "@holistics/utils": "1.0.0", "@holistics/web-storage": "1.0.0", "@opentelemetry/api": "^1.4.1", "@vueuse/core": "^10.9.0", "@vueuse/router": "^10.9.0", "dexie": "^3.2.4", "diff2html": "^3.4.35", "js-string-escape": "^1.0.1", "lodash": "^4.17.21", "md5": "^2.3.0", "mermaid": "^11.6.0", "parse-diff": "^0.11.1", "slug": "^10.0.0", "sql-formatter": "https://github.com/holistics/sql-formatter", "uuid": "8.3.2", "vee-validate": "^4.13.2", "vue-router": "4", "vue-virtual-scroller": "^2.0.0-beta.8", "vuex": "4", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@holistics/types": "0.5.11", "@types/js-string-escape": "^1.0.3", "@types/mermaid": "^8.2.9", "@types/slug": "^5.0.9", "@types/uuid": "^9.0.4"}}