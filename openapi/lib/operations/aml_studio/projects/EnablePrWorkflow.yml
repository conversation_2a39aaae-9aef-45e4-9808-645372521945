operationId: AmlStudioProjects_EnablePrWorkflow
summary: Enable PR Workflow
tags:
  - Development

parameters:
  - name: id
    in: path
    required: true
    description: AML Project Id
    schema:
      type: integer
requestBody:
  content:
    application/json:
      schema:
        type: object
        properties:
          token:
            description: new PR token
            type: string
        required: []
responses:
  "200":
    description: Status
    content:
      application/json:
        schema:
          type: object
          properties:
            message:
              type: string
  "422":
    $ref: ../../../components/responses/errors/InvalidOperationError.yml
  "403":
    $ref: ../../../components/responses/errors/PermissionDeniedError.yml
