<script setup lang="ts">
import { <PERSON><PERSON><PERSON><PERSON>, HTooltip } from '@holistics/design-system';
import { computed, toRef } from 'vue';
import type { VizBlock } from '@holistics/aml-std';
import AmlDataset from '@holistics/aml-studio/client/models/Dataset';
import DataSet from '@/modules/DataSets/models/DataSet';
import DataSetExplorer from '@/modules/DataSets/components/DataSetExplorer.vue';
import { buildExplorableVizSetting } from '@/modules/Viz/utils/buildExplorableVizSetting';
import { useDashboardConfigs, type SaveVisualizationParams } from '@/modules/DashboardAsCode/composables/useDashboardConfigs';
import { useDashboardTimezone } from '../../composables/useDashboardTimezone';
import { EXPLORATION_HASH_QUERY_PARAM } from '../../constants/routeQueryParams';
import { useVizBlockStates } from '../../composables/useVizBlockStates';

export interface Props {
  block: VizBlock
}
const props = defineProps<Props>();
const emit = defineEmits<{ dismiss: [] }>();

const {
  currentTimezone,
} = useDashboardTimezone();
const { appliedVizSetting, vizDependenciesReady, dataset } = useVizBlockStates(toRef(() => props.block));
const datasetModel = computed(() => (dataset.value.from_aml ? new AmlDataset(dataset.value) : new DataSet(dataset.value)));

const explorableVizSetting = computed(() => {
  return buildExplorableVizSetting(appliedVizSetting.value);
});

const { saveVisualizationFunc } = useDashboardConfigs();
async function saveVisualization (params: SaveVisualizationParams) {
  if (props.block && saveVisualizationFunc) {
    await saveVisualizationFunc(params);
  }
}
</script>

<template>
  <div
    v-h-loading.body="!vizDependenciesReady"
    class="dac-explore-viz-block-modal relative h-[calc(100vh-50px)] rounded"
    data-ci="explore-viz-block"
  >
    <div class="flex h-full flex-row rounded p-0">
      <DataSetExplorer
        v-if="vizDependenciesReady"
        class="rounded"
        :data-set="datasetModel"
        :data-models="datasetModel.dataModels"
        :joins="datasetModel.relatedJoins"
        :join-configs="datasetModel.joinConfigs"
        :initial-viz-setting="explorableVizSetting"
        :report-title="block?.label"
        :report-description="block?.description"
        :source="{ type: 'DataSet', id: datasetModel.id, action: 'explore', timezone: currentTimezone }"
        :explore-query-param="EXPLORATION_HASH_QUERY_PARAM"
        :additional-viz-options="{ isV4: true }"
      >
        <template
          v-if="saveVisualizationFunc"
          #save-as-viz-dataset="slotProps"
        >
          <HTooltip
            :content="slotProps.tooltipText"
            placement="top"
          >
            <HButton
              icon="save"
              :disabled="slotProps.disabled"
              class="btn btn-text ci-save-explore-results-btn"
              type="tertiary-highlight"
              size="sm"
              @click.prevent="slotProps.disabled ? undefined : saveVisualization(slotProps as any)"
            >
              Save As
            </HButton>
          </HTooltip>
        </template>
      </DataSetExplorer>
    </div>
    <div class="absolute right-2 top-2">
      <HButton
        type="tertiary-default"
        unified
        icon="cancel"
        size="sm"
        data-ci="close-modal"
        @click.prevent="emit('dismiss')"
      />
    </div>
  </div>
</template>

<style lang="postcss">
.dac-explore-viz-block-modal {
  .__header {
    &.subheader {
      .actions {
        @apply mr-5;
      }
    }
  }
}
</style>
