Model users {
  type: 'query'
  label: 'Users'
  description: ''
  data_source_name: 'pg'
  dimension id {
    label: 'User Id'
    type: 'number'
    hidden: false
    definition: @sql {{ #SOURCE.id }};;
  }
  dimension name {
    label: 'User Name'
    type: 'text'
    hidden: false
    definition: @sql {{ #SOURCE.name }};;
  }
  dimension country_id {
    label: 'Country Id'
    type: 'number'
    hidden: false
    definition: @sql {{ #SOURCE.country_id }};;
  }

  owner: '<EMAIL>'
  query: @sql
    with users(
      id,
      name,
      country_id
    ) as (
      values
        (1, 'User A', 1),
        (2, 'User B', 1),
        (3, 'User C', 2),
        (4, 'User D', 2),
        (5, 'User E', 3),
        (6, 'User F', 3),
        (7, 'User G', 4),
        (8, 'User H', 4)
    )
    select
      id,
      name,
      country_id
    from
      users
  ;;

  models: []
}
