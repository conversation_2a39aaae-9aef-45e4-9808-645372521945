# typed: false
# frozen_string_literal: true

require 'rails_helper'

def sign_up_for_trial(submission, success = true)
  visit '/accounts/sign_up'
  wait_for_element_load('#ci-first_name')

  page.find_by_id('ci-first_name').set(submission[:first_name])
  page.find_by_id('ci-last_name').set(submission[:last_name])
  page.find_by_id('ci-company_name').set(submission[:company_name])
  page.find_by_id('ci-email').set(submission[:email])
  page.find_by_id('ci-mobile_number').set(submission[:mobile_number])
  page.find_by_id('ci-know_sql').click
  page.find_by_id('ci-databases').set(submission[:databases])
  page.find_by_id('ci-submit').click

  if success
    wait_for_element_load('.congrate-panel', 10)
  else
    wait_for_element_load('.ci-error', 20)
  end
end

def go_to_activation_link
  confirmation_link = get_last_email.to_s.scan(%r{(/accounts/activate\?code=[a-z0-9]+)}).try(:[], 0).try(:[], 0)
  visit confirmation_link
end

def register_new_user(name, password)
  # test user password
  wait_for_element_load('#activate_trial_form')
  page.find('.ci-name').set(name)
  page.find('.ci-password').set(password)
  page.find('.ci-password-confirmation').set(password)
  safe_click('.ci-next')
  sleep 1 # wait for loading
  # test user title
  safe_click('[data-ci="select-user-title"] > [data-ci="select-Others"]')
  safe_click('[data-ci="select-user-bi-tools"] > [data-ci="select-Others"]')
  send_keys :enter
  safe_click('.ci-submit')
  sleep 1 # wait for loading
end

def connect_to_ds(test_dbconfig)
  page.find('button', text: 'PostgreSQL').click
  wait_for_element_load('.ci-name')
  fill_text('.ci-name', 'new-ds')

  fill_text('.ci-host', test_dbconfig[:host])
  fill_text('.ci-port', test_dbconfig[:port])
  fill_text('.ci-username', test_dbconfig[:user])
  fill_text('.ci-password', test_dbconfig[:password])
  fill_text('.ci-dbname', test_dbconfig[:dbname])
  page.find('.ci-test').click

  wait_for_element_load('[data-ci="ci-test-message"]')
  wait_expect('connected successfully') { page.find('[data-ci="ci-test-message"]').text.downcase }
  page.find('.ci-submit').click

  wait_for { DataSource.count > 0 }
  new_ds = DataSource.last
  %i[host user dbname password port].each do |k|
    expect(new_ds.dbconfig[k].to_s).to eq test_dbconfig[k].to_s
  end
end

def connect_ds
  wait_for_element_load('.ci-connect-ds')
  page.find('.ci-connect-ds').click

  page.find('.ci-test').click
  sleep 0.5
  wait_for_element_load('[data-ci="ci-test-message"]')
  wait_expect('connected successfully') { page.find('[data-ci="ci-test-message"]').text.downcase }

  page.find('.ci-submit').click
  sleep 1
end

def attach_csv(file_name = 'test_import_csv')
  wait_expect(1) { page.all('.ready').count }
  execute_script("document.querySelector('input[name=\"fileInput\"]').classList.remove('hidden')")
  attach_file('fileInput', Rails.root + "spec/fixtures/uploads/#{file_name}.csv")
end

describe 'onboarding 3.0', :js, :stable do
  let(:submission) do
    {
      first_name: 'Napoleon',
      last_name: 'Bonaparte',
      company_name: 'French Empire',
      email: '<EMAIL>',
      mobile_number: 342_424_242,
      important_feature: 'I gonna invade Russia',
      databases: 'PostgreSQL, MongoDB',
      other_solutions: 'I lived in mountain',
      referral: 'Quora',
    }
  end
  let(:user_name) { "#{submission[:first_name]} #{submission[:last_name]}" }
  let(:test_dbconfig) { dbconfig_rails_test_env }
  let(:ds) { DataSource.last }
  let(:sync_job) { Job.where(source: ds, source_method: 'synchronize_schema').last }
  let(:tenant) { Tenant.find_by(name: submission[:company_name]) }
  let(:welcome_modal_selector) { '.onboarding-welcome-modal' }
  let(:welcome_modal) { page.find(welcome_modal_selector) }

  before do
    DataSource.destroy_all

    FeatureToggle.toggle_global('schema_synchronization', true)
    ftg = FeatureToggleGroups::UpsertVersions.new.call!('Version 3.0')

    sign_up_for_trial(submission)
    go_to_activation_link
    ftg.toggle_tenants!([tenant.id], FeatureToggleGroup::TOGGLE_MODE_ADD)
    register_new_user(user_name, 'Zoyoanch0987')
  end

  it 'shows Welcome modal' do
    wait_for_element_load(welcome_modal_selector)

    expect(welcome_modal).to have_content("👋 Welcome to Holistics App, #{user_name}!")
    expect(welcome_modal).to have_content("We are delighted that you chose to come onboard with us and we can't wait for you to\nuncover the full potential of our application. Let's get started right away!")
    expect(welcome_modal).to have_button('Get started')
    expect(welcome_modal).to have_css('.close-btn')
  end

  context 'when closing Welcome modal' do
    before do
      wait_for_element_load(welcome_modal_selector)
      welcome_modal.click_button('Get started')
      sleep(0.5) # TODO(ajax): wait until all ajax calls are responded
    end

    it 'does not show Welcome modal again' do
      page.refresh

      expect do
        wait_for_element_load(welcome_modal_selector, 1)
      end.to raise_error(/Timeout occurred. Failed to wait for (.*)?./)
    end

    it 'shows onboarding screen' do
      wait_for_element_load('.onboarding-welcome-screen')
      page.find('.ci-admin-enter-onboarding').click
      wait_for_element_load('.admin-onboarding-home')
      expect(page.has_content?('do you have any SQL databases?'))
      expect(page.has_content?('Our setup requires a SQL Database to get started.'))
    end

    context 'admin walkthrough onboarding flow normally' do
      before do
        allow_any_instance_of(DataSource).to receive(:current_version).and_call_original
      end

      it 'successfully passes through the normal flow' do
        wait_for_element_load('.onboarding-welcome-screen')
        page.find('.ci-admin-enter-onboarding').click
        wait_for_element_load('.admin-onboarding-home')
        connect_to_ds(test_dbconfig)
        sync_job.update(status: 3)
        sync_job.update(status: 1)
        wait_for_element_load('.add-table-models')
        tree_select_select_option('.tree-select-wrapper', 'users')
        page.find('.create-models').click
        wait_for_element_load('.create-data-set-onboarding')
        fill_text('.ci-dataset-name', 'The First Dataset')
        tree_select_select_option('.model-select-wrapper', 'user')
        page.find('.create-dataset').click

        wait_for_element_load('.data-set-explorer')
        expect(page.find('.ci-data-set-form-title').text).to eq 'The First Dataset'

        wait_for_element_load('.ci-data-set-explore-tour')
        expect(page.find('.ci-data-set-explore-tour').text).to include 'Your Dataset is ready to be explored'
        safe_click('.ci-start-viz')
        expect(page.find('.ci-data-set-explore-tour').text)
          .to match(/Start visualizing by drag-&-drop a field from your Dataset into Visualizations\D+Got it/)

        wait_for_element_load('.ci-data-models-list .ci-tree-select-option-list')
        expect(page).to have_css('.ci-data-model-field')
        page.first('.ci-data-model-field .name').click
        sleep 1

        checklist_selector = '.ci-full-in-app-onboarding-checklist'
        wait_for_element_load(checklist_selector)
        page.find(checklist_selector).click_button('Dismiss')
        page.assert_selector('.onboarding-tutorials-modal')
      end

      context 'when admin chooses to connect to another data source in step 2' do
        it 'opens the popup to select another data source' do
          safe_click('.ci-admin-enter-onboarding')
          wait_for_element_load('.admin-onboarding-home')
          connect_to_ds(test_dbconfig)
          sync_job.update(status: 3)
          sync_job.update(status: 1)
          wait_for_element_load('.add-table-models')

          # select another data source option from the dropdown
          select_h_select_option('.h-data-source-select', label: 'Connect to another SQL Database')

          # should hide the data source select dropdown
          expect(page.should(have_css('.h-data-source-select', visible: false)))

          # should open the modal to connect to another data source
          expect(page).to have_css('.add-data-warehouse-modal', text: 'Connect to Data Warehouse')
        end
      end
    end

    context 'admin switch to another tab in the middle of onboarding flow' do
      before do
        allow_any_instance_of(DataSource).to receive(:current_version).and_call_original
      end

      it 'successfully connect data source, create models from table, create the first dataset' do
        wait_for_element_load('.onboarding-welcome-screen')
        page.find('.ci-admin-enter-onboarding').click
        wait_for_element_load('.admin-onboarding-home')
        connect_to_ds(test_dbconfig)
        sync_job.update(status: 3)
        sync_job.update(status: 1)
        wait_for_element_load('.add-table-models')
        tree_select_select_option('.tree-select-wrapper', 'users')
        page.find('.create-models').click
        wait_for_element_load('.create-data-set-onboarding')
        visit '/data_models/'
        wait_for_element_load('.data-model-header')
        visit '/home'
        wait_for_element_load('.create-data-set-onboarding')
        fill_text('.ci-dataset-name', 'The First Dataset')
        tree_select_select_option('.model-select-wrapper', 'user')
        page.find('.create-dataset').click
        wait_for_element_load('.data-set-explorer')
        expect(page.find('.ci-data-set-form-title').text).to eq 'The First Dataset'
      end
    end
  end
end

describe 'onboarding 4.0', :js do
  include_context 'aml_studio_basic'

  let(:submission) do
    {
      first_name: 'Napoleon',
      last_name: 'Bonapartes',
      company_name: 'French Empires 4.0',
      email: '<EMAIL>',
      mobile_number: 342_424_242,
      important_feature: 'I gonna invade Russia',
      databases: 'PostgreSQL, MongoDB',
      other_solutions: 'I lived in mountain',
      referral: 'Quora',
      dev_mode_enabled: false,
    }
  end
  let(:user_name) { "#{submission[:first_name]} #{submission[:last_name]}" }
  let(:test_dbconfig) { dbconfig_rails_test_env }
  let(:ds) { DataSource.last }
  let(:sync_job) { Job.where(source: ds, source_method: 'synchronize_schema').last }
  let(:tenant) { Tenant.find_by(name: submission[:company_name]) }
  let(:welcome_modal_selector) { '.onboarding-welcome-modal' }
  let(:welcome_modal) { page.find(welcome_modal_selector) }

  def select_table(table_name)
    tree_select_select_option('.tree-select-wrapper', table_name)
    tree_select_clear_input('.tree-select-wrapper')
  end

  def select_model(model_name)
    tree_select_select_option('.model-select-wrapper', model_name)
    tree_select_clear_input('.model-select-wrapper')
  end

  def check_first_relationship_data(type_data, from_data, to_data)
    relationship_elems = page.all('td')
    type = relationship_elems[0]
    from = relationship_elems[1]
    to = relationship_elems[2]

    expect(type).to have_content(type_data)
    expect(from).to have_content(from_data)
    expect(to).to have_content(to_data)
  end

  def click_create_models
    page.find('[data-ci="ci-create-models"]').click
  end

  def click_create_dataset
    page.find('[data-ci="ci-create-dataset"]').click
  end

  def set_dataset_name(name)
    fill_text('[data-ci="ci-dataset-name"]', name)
  end

  def click_next_step
    page.find('[data-ci="ci-next-step-btn"]').click
  end

  def click_next_in_relationship
    page.find('[data-ci="ci-create-relationship-next-step"]').click
  end

  def click_relationship_list_tab
    page.all('.ci-tab-toggle').first.click
  end

  def enter_onboarding
    wait_for_element_load(welcome_modal_selector)
    welcome_modal.click_button('Get started')
    sleep(0.5)
    wait_for_element_load('.onboarding-welcome-screen')
    page.find('.ci-admin-enter-onboarding').click
    wait_for_element_load('.admin-onboarding-home')
  end

  def setup_data_source
    connect_to_ds(test_dbconfig)
    sync_job.update(status: 3)
    sync_job.update(status: 1)
  end

  def check_dataset_has_been_created(file_name)
    expect(page.find('[data-ci="editor-header-dropdown"]').text).to eq file_name
  end

  def enable_dev_mode
    wait_for_element_load('.empty-state-dev-mode')
    expect(page.find('.empty-state-title').text).to eq 'Enable Development Mode'
    page.find('.ci-toggle-dev-mode').click
  end

  def change_editor_mode(mode = 'DATA')
    data_tab_selector = ".ci-toggle-#{mode}-mode"
    wait_for_element_load(data_tab_selector)
    safe_click(data_tab_selector)
  end

  def click_onboarding_tooltip(tooltip_name)
    wait_for_element_load("[data-ci=\"#{tooltip_name}\"]")
    page.find('[data-ci="next-step-btn"]').click
  end

  before do
    DataSource.destroy_all
    AmlStudio::Repository.destroy_all
    AmlStudio::Project.destroy_all
    FeatureToggle.toggle_global('schema_synchronization', true)
    FeatureToggle.toggle_global('data_models:manager', false)
    FeatureToggle.toggle_global('aml_studio:enable', true)
    FeatureToggle.toggle_global('data_models:new_sql_generation', true)

    ftg = FeatureToggleGroups::UpsertVersions.new.call!('Version 4.0')
    sign_up_for_trial(submission)
    go_to_activation_link
    ftg.toggle_tenants!([tenant.id], FeatureToggleGroup::TOGGLE_MODE_ADD)
    register_new_user(user_name, 'Zoyoanch0987')
  end

  context 'Onboarding for Holistics tenant 4.0' do
    before do
      FeatureToggle.toggle_global('onboarding:enable_onboarding_40_tooltips', true)
    end

    it 'go through all steps' do
      enter_onboarding
      setup_data_source
      enable_dev_mode
      sleep 0.5

      click_next_step
      sleep 1

      wait_for_element_load('.add-table-models')

      select_table('activities')
      click_create_models
      sleep 1

      wait_for_element_load('.create-data-set-onboarding')
      expect(page.find('[data-ci="ci-dataset-name"]').value).to eq 'my_first_dataset'
      set_dataset_name('The First Dataset')
      expect(page.find('[data-ci="ci-dataset-name"]').value).to eq 'the_first_dataset'

      wait_for_element_load('.model-select-wrapper')
      select_model('public_activities')
      click_create_dataset

      wait_for_element_load('.ci-switch-looker-migrator')
      safe_click('.ci-switch-looker-migrator')
      sleep 1
      safe_click('.ci-continue-looker-migrator')

      sleep 1
      wait_for_element_load('.aml-editor-container')
      check_dataset_has_been_created('the_first_dataset.dataset.aml')

      page.find('[data-ci="onboarding-hide-checklist"]').click
      click_onboarding_tooltip('onboarding-open-first-data-set-tooltip')
      click_onboarding_tooltip('onboarding-edit-data-set-tooltip')
      click_onboarding_tooltip('onboarding-add-relationship-row-tooltip')
      click_onboarding_tooltip('onboarding-open-first-dashboard-tooltip')
      click_onboarding_tooltip('onboarding-edit-dashboard-tooltip')
      click_onboarding_tooltip('onboarding-deploy-data-set-tooltip')

      expect(page).to have_css('[data-ci="onboarding-finished-checklist"]')
    end
  end

  context 'Onboarding 4.0 + Enable looker assistant' do
    it 'check looker assistant' do
      page.refresh

      enter_onboarding
      setup_data_source
      enable_dev_mode
      sleep 0.5

      click_next_step
      sleep 1

      wait_for_element_load('.add-table-models')

      select_table('activities')
      click_create_models
      sleep 1

      wait_for_element_load('.create-data-set-onboarding')
      expect(page.find('[data-ci="ci-dataset-name"]').value).to eq 'my_first_dataset'

      wait_for_element_load('.model-select-wrapper')
      select_model('public_activities')
      click_create_dataset

      wait_for_element_load('.ci-switch-looker-migrator')
      safe_click('.ci-switch-looker-migrator')
      sleep 1
      safe_click('.ci-continue-looker-migrator')

      wait_for_element_load('.aml-editor-container')

      project = tenant.first_project
      expect(project.settings[:enabled_looker_migrator]).to eq true
    end
  end

  context 'Onboarding 4.0 + checking for special characters' do
    it 'check special characters' do
      enter_onboarding
      setup_data_source
      enable_dev_mode
      sleep 0.5

      click_next_step
      sleep 1

      wait_for_element_load('.add-table-models')

      select_table('activities')
      click_create_models
      sleep 1

      wait_for_element_load('.create-data-set-onboarding')
      expect(page.find('[data-ci="ci-dataset-name"]').value).to eq 'my_first_dataset'

      set_dataset_name('The First Dataset with special characters 😂')

      expect(page.find('[data-ci="ci-dataset-name"]').value).to eq 'the_first_dataset_with_special_characters_'
      click_create_dataset
    end
  end

  context 'Onboarding for Holistics tenant 4.0 has foreign keys in database' do
    before do
      FeatureToggle.toggle_tenant('aml_studio:auto_import_relationship', tenant.id, true)
      FeatureToggle.toggle_tenant(Join::FT_AUTO_SYNC_FROM_FKEYS, tenant.id, true)
    end

    it 'go through all steps and auto import relationships' do
      enter_onboarding
      setup_data_source
      enable_dev_mode
      sleep 0.5

      click_next_step
      sleep 1

      wait_for_element_load('.add-table-models')

      select_table('tenants')
      select_table('personal_items')

      click_create_models
      sleep 1

      wait_for_element_load('.create-data-set-onboarding')
      set_dataset_name('The First Dataset')
      expect(page.find('[data-ci="ci-dataset-name"]').value).to eq 'the_first_dataset'

      wait_for_element_load('.model-select-wrapper')
      select_model('public_tenants')
      select_model('public_personal_items')
      click_create_dataset

      wait_for_element_load('.create-relationship')
      click_relationship_list_tab

      check_first_relationship_data('n - 1', 'public_personal_items.tenant_id', 'public_tenants.id')

      click_next_in_relationship

      wait_for_element_load('.ci-switch-looker-migrator')
      safe_click('.ci-switch-looker-migrator')
      sleep 1
      safe_click('.ci-continue-looker-migrator')

      sleep 2

      wait_for_element_load('.aml-editor-container')
      check_dataset_has_been_created('the_first_dataset.dataset.aml')

      change_editor_mode('CODE')
      sleep 1
      expect(page).to have_content('relationship(public_personal_items.tenant_id > public_tenants.id, true)')
    end
  end
end
