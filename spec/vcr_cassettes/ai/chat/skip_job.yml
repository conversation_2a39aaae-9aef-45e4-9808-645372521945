---
http_interactions:
- request:
    method: post
    uri: http://localhost:8686/chat-messages
    body:
      encoding: UTF-8
      string: '{"agent":"scout","query":"hi","inputs":{"text_before_cursor":"hi","text_after_cursor":"there","data_source_names":["mock_data_source"],"model_names":["mock_model"]},"config":{},"user":"5","sync_mode":true}'
    headers:
      Authorization:
      - "<AUTH>"
      Connection:
      - close
      Content-Type:
      - application/json; charset=utf-8
      Host:
      - localhost:8686
      User-Agent:
      - http.rb/5.2.0
      Traceparent:
      - 00-655368e3c872bbbd2a227e480642aff9-70bdba45d9659fd4-00
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 29 May 2025 07:45:39 GMT
      Server:
      - uvicorn
      Content-Length:
      - '463'
      Content-Type:
      - application/json
      Connection:
      - close
    body:
      encoding: UTF-8
      string: '{"convesation_id":"34acfa17f393400dad8cc84aea534324","answer":"hi there","metadata":{"model":"gpt-4.1-2025-04-14","index":0,"finish_reason":"stop","completion_start_time":"2025-05-29T14:45:40.370080","usage":{"completion_tokens":2,"prompt_tokens":845,"total_tokens":847,"completion_tokens_details":{"accepted_prediction_tokens":0,"audio_tokens":0,"reasoning_tokens":0,"rejected_prediction_tokens":0},"prompt_tokens_details":{"audio_tokens":0,"cached_tokens":0}}}}'
  recorded_at: Thu, 29 May 2025 07:45:40 GMT
recorded_with: VCR 6.2.0
