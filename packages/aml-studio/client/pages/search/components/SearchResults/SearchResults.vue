<template>
  <div class="flex h-full flex-col">
    <template v-if="isSearching">
      <div class="px-3 text-gray-500">
        Searching...
      </div>
    </template>

    <template v-else-if="results.length">
      <HAlert
        v-if="needRefreshImplicitRepo"
        type="info"
        class="mx-3 mb-3 rounded-2xl border border-blue-100 bg-blue-50/30"
        title="Project Updated"
      >
        <div class="">
          <div class="flex flex-col gap-2">
            <span>
              Changes have been made to this project by other users.
              Please refresh first to see the latest content before replacing.
            </span>
            <a
              data-ci="ci-refresh-project"
              class="cursor-pointer"
              @click="refreshProject"
            >
              Refresh
            </a>
          </div>
        </div>
      </HAlert>

      <div class="px-3 pb-1 text-[11px] text-gray-600">
        {{ totalMatches }} {{ totalMatches === 1 ? 'result' : 'results' }} in {{ fileCount }} {{ fileCount === 1 ? 'file' : 'files' }}
      </div>

      <div class="flex-1 overflow-hidden">
        <SearchResultsList
          :items="displayNodes"
          :selected-id="selectedItemId"
          :item-height="itemHeight"
          :search-texts="searchTexts"
          :show-replace="showReplace"
          :replace-value="replaceValue"
          :need-refresh-implicit-repo="needRefreshImplicitRepo"
          @toggle-file="handleToggleFile"
          @open-file="handleOpenFile"
          @open-match="handleOpenMatch"
          @replace-in-file="handleReplaceInFile"
          @dismiss-result="handleDismissResult"
          @dismiss-file="handleDismissFile"
        />
      </div>
    </template>

    <div
      v-else-if="results.length === 0 && searchTexts.length === 0"
      class="px-3 text-gray-500"
    >
      No results found.
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Node, TextNode, SearchResult } from '@aml-studio/client/pages/search/types';
import { computed } from 'vue';
import { HAlert } from '@holistics/design-system';
import SearchResultsList from './SearchResultsList.vue';

interface Props {
  results: SearchResult[];
  displayNodes: Node[];
  selectedItemId: string | null;
  itemHeight: number;
  searchTexts: string[];
  showReplace: boolean;
  replaceValue: string;
  isSearching: boolean;
  totalMatches: number;
  needRefreshImplicitRepo: boolean;
}

const props = defineProps<Props>();

const fileCount = computed(() => props.results.length);

const emit = defineEmits<{(e: 'toggleFile', path: string, isExpanded: boolean): void;
  (e: 'openFile', item: Node): void;
  (e: 'openMatch', item: TextNode): void;
  (e: 'replaceInFile', item: Node): void;
  (e: 'dismissResult', item: TextNode): void;
  (e: 'dismissFile', path: string): void;
  (e: 'refreshProject'): void;
}>();

const handleToggleFile = (path: string, isExpanded: boolean) => {
  emit('toggleFile', path, isExpanded);
};

const handleOpenFile = (item: Node) => {
  emit('openFile', item);
};

const handleOpenMatch = (item: TextNode) => {
  emit('openMatch', item);
};

const handleReplaceInFile = (item: Node) => {
  emit('replaceInFile', item);
};

const handleDismissResult = (item: TextNode) => {
  emit('dismissResult', item);
};

const refreshProject = () => {
  emit('refreshProject');
};

const handleDismissFile = (path: string) => {
  emit('dismissFile', path);
};
</script>
