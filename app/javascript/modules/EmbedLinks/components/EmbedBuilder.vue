<template>
  <div class="embed-builder mr-3 flex flex-col">
    <div class="mb-2 text-xs font-medium">
      Embed Settings
      <HTooltip
        :content="'These settings will help generate the embed code.'"
        placement="right"
      >
        <h-icon
          name="info"
        />
      </HTooltip>
    </div>
    <div class="permission-and-filter min-h-0 min-w-0 flex-1 border">
      <div class="flex h-full flex-col overflow-auto">
        <div
          class="border-b p-4"
        >
          <div class="mb-2 text-xs font-medium">
            General Settings
          </div>
          <div
            v-if="allowDashboardExportSetting"
            class="flex flex-row items-center"
          >
            <h-checkbox
              :model-value="modelValue.settings.enable_dashboard_export"
              @update:model-value="updateEnableDashboardExportSetting"
            >
              <span>Enable export dashboard</span>
            </h-checkbox>
            <HTooltip
              content="By checking this, your dashboard viewers will be able to export the entire dashboard as PDF or PNG."
              placement="right"
            >
              <h-icon
                name="info"
                class="ml-2"
              />
            </HTooltip>
          </div>
          <div class="flex flex-row items-center">
            <h-checkbox
              :model-value="modelValue.settings.enable_export_data"
              @update:model-value="updateEnableExportDataSetting"
            >
              Enable export raw data
            </h-checkbox>
            <HTooltip
              :content="`By checking this, your dashboard viewers will be able to export your raw data as CSV or Excel (only available on ${singleItemText}s).`"
              placement="right"
            >
              <h-icon
                name="info"
                class="ml-2"
              />
            </HTooltip>
          </div>
          <div
            v-if="allowHideHeaderPanel"
            class="flex flex-row items-center"
          >
            <h-checkbox
              :model-value="modelValue.settings.hide_header_panel"
              @update:model-value="updateHideHeaderPanelSetting"
            >
              <span>Hide header panel</span>
            </h-checkbox>
            <HTooltip
              :content="'By checking this, the dashboard\'s header panel will be hidden.'"
              placement="right"
            >
              <h-icon
                name="info"
                class="ml-2"
              />
            </HTooltip>
          </div>
          <div
            v-if="allowHideControlPanel"
            class="flex flex-row items-center"
          >
            <h-checkbox
              :model-value="modelValue.settings.hide_control_panel"
              @update:model-value="updateHideControlPanelSetting"
            >
              <span>Hide control panel</span>
            </h-checkbox>
            <HTooltip
              :content="'By checking this, the dashboard\'s control panel will be hidden.'"
              placement="right"
            >
              <h-icon
                name="info"
                class="ml-2"
              />
            </HTooltip>
          </div>
        </div>
        <div
          v-if="$options.dashboardTimezoneEnabled"
          class="border-b p-4"
        >
          <div class="mb-2 text-xs font-medium">
            Timezone Settings
          </div>
          <div class="mt-2 flex flex-row items-center">
            <span>
              Default timezone
            </span>
            <timezone-select
              :model-value="modelValue.settings.default_timezone"
              :default-timezone="source.timezone"
              :default-timezone-label="'Dashboard timezone'"
              class="ci-default-timezone ml-2 w-72"
              @update:model-value="updateTimezone"
            />
          </div>
          <div class="mt-2 flex flex-row items-center">
            <h-checkbox
              :model-value="modelValue.settings.allow_to_change_timezone"
              class="ci-allow-to-change-timezone"
              @update:model-value="updateAllowToChangeTimezone"
            >
              <span>Allow viewers to change the timezone</span>
            </h-checkbox>
            <HTooltip
              :content="'Viewers can flexibly change the display timezone when viewing this dashboard'"
              placement="right"
            >
              <h-icon
                name="info"
                class="ml-2"
              />
            </HTooltip>
          </div>
        </div>
        <row-based-permissions-form
          :model-value="modelValue.permissions.row_based"
          :source="source"
          :full-row-permission-value="false"
          compact-field-select
          class="permissions-display-container flex flex-col border-b p-4"
          @update:model-value="updateRowBasedPermissions"
        >
          <template #help-text>
            <HTooltip
              :content="`Control which records a user can view from your organization's database`"
              placement="right"
            >
              <h-icon
                class="ml-1"
                name="info"
              />
            </HTooltip>
          </template>
        </row-based-permissions-form>

        <ExplorableDatasetsForm
          v-if="embedExplorationEnabled"
          :source="source"
          :model-value="modelValue.permissions.action_based.explore.datasets"
          class="flex flex-col border-b p-4"
          @update:model-value="updateExplorableDatasets"
        >
          <template #help-text>
            <HTooltip
              :content="`Control which records a user can view from your organization's database`"
              placement="right"
            >
              <h-icon
                class="ml-1"
                name="info"
              />
            </HTooltip>
          </template>
        </ExplorableDatasetsForm>
        <div
          class="filter-display-container flex flex-col p-4"
        >
          <div class="mb-2 text-xs font-medium">
            {{ $options.DYNAMIC_FILTER_FEATURE_NAME }}s Settings
            <HTooltip
              :content="`Pre-define the default filter value that will be automatically applied to the embedded dashboard`"
              placement="right"
            >
              <h-icon
                class="ml-1"
                name="info"
              />
            </HTooltip>
          </div>
          <div class="filter-display min-h-0 min-w-0 flex-1">
            <div
              v-for="(filter, index) in filters"
              :key="index"
            >
              <embed-builder-dynamic-filter
                :filter="filter"
                :model-value="modelValue.filters[filter.uname].default_condition"
                :hidden="modelValue.filters[filter.uname].hidden"
                :ui-scheme="uiSchemes[filter.id]"
                :all-filters="filters"
                :all-filter-conditions="allFilterConditions"
                :allow-hide-control="allowHideControl"
                @update:model-value="value => updateCondition(filter.uname, value)"
                @hidden="(value) => updateFilterHiddenSetting(filter.uname, value)"
              />
            </div>
          </div>
        </div>
        <template v-if="embedDashboardUAEnabled">
          <hr>
          <div class="p-4">
            <div class="font-medium">
              User Attributes
            </div>
            <EmbedAttributesBuilder
              :model-value="modelValue.user_attributes"
              @update:model-value="v => updateUserAttributes(v)"
            />
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { HTooltip } from '@holistics/design-system';
/** @file-tag #dynamic_filter */

import { get, map } from 'lodash';
import { check, check as checkFeatureToggle } from '@/core/services/featureToggle';
import { DYNAMIC_FILTER_FEATURE_NAME } from '@/modules/DynamicFilters/constants';
import { buildFieldPathObject } from '@/modules/DataModels/utils';
import RowBasedPermissionsForm from '@/modules/DynamicFilters/components/ui/RowBasedPermissionsForm.vue';
import TimezoneSelect from '@/core/components/TimezoneSelect.vue';
import EmbedBuilderDynamicFilter from './EmbedBuilderDynamicFilter.vue';
import ExplorableDatasetsForm from './ExplorableDatasetsForm.vue';
import EmbedAttributesBuilder from './EmbedAttributesBuilder.vue';

export default {
  name: 'EmbedBuilder',
  DYNAMIC_FILTER_FEATURE_NAME,
  components: {
    HTooltip,
    EmbedBuilderDynamicFilter,
    RowBasedPermissionsForm,
    TimezoneSelect,
    ExplorableDatasetsForm,
    EmbedAttributesBuilder,
  },
  dashboardTimezoneEnabled: checkFeatureToggle('timezone:dashboard_timezone'),
  props: {
    modelValue: {
      type: Object,
      required: true,
    },
    conditions: {
      type: Object,
      required: true,
    },
    filters: {
      type: Array,
      required: true,
    },
    uiSchemes: {
      type: Object,
      required: true,
    },
    source: {
      type: Object,
      required: true,
    },
    allowHideControl: {
      type: Boolean,
      default: true,
    },
    allowHideHeaderPanel: {
      type: Boolean,
      default: true,
    },
    allowHideControlPanel: {
      type: Boolean,
      default: true,
    },
    embedExplorationEnabled: {
      type: Boolean,
      default: false,
    },
    allowDashboardExportSetting: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['update:modelValue'],
  computed: {
    allFilterConditions () {
      const allFilterConditions = {};
      this.filters.forEach(f => {
        const condition = get(this.modelValue || {}, ['filters', f.uname, 'default_condition']);
        if (condition) allFilterConditions[f.id] = condition;
      });
      return allFilterConditions;
    },
    singleItemText () {
      if (!this.source || this.source.version !== 4) return 'widget';
      return 'block';
    },
    embedDashboardUAEnabled () {
      return check('embed_link:embed_dashboard_user_attributes');
    },
  },
  methods: {
    updateSetting (key, value) {
      const settings = {
        ...this.modelValue,
        settings: {
          ...this.modelValue.settings,
          [key]: value,
        },
      };

      this.$emit('update:modelValue', settings);
    },
    updateExplorableDatasets (newValue) {
      const currentPermissions = this.modelValue.permissions;
      const settings = {
        ...this.modelValue,
        permissions: {
          ...currentPermissions,
          action_based: {
            ...currentPermissions.action_based,
            explore: {
              ...currentPermissions.action_based.explore,
              datasets: newValue,
            },
          },
        },
      };

      this.$emit('update:modelValue', settings);
    },
    updateEnableExportDataSetting (newValue) {
      this.updateSetting('enable_export_data', newValue);
    },
    updateHideHeaderPanelSetting (newValue) {
      this.updateSetting('hide_header_panel', newValue);
    },
    updateEnableDashboardExportSetting (newValue) {
      this.updateSetting('enable_dashboard_export', newValue);
    },
    updateHideControlPanelSetting (newValue) {
      this.updateSetting('hide_control_panel', newValue);
    },
    updateTimezone (newTimezone) {
      this.updateSetting('default_timezone', newTimezone);
    },
    updateAllowToChangeTimezone (newValue) {
      this.updateSetting('allow_to_change_timezone', newValue);
    },
    updateRowBasedPermissions (rowBasedPermissionRules) {
      const settings = {
        ...this.modelValue,
        permissions: this.buildPermissionConfigs(rowBasedPermissionRules),
      };

      this.$emit('update:modelValue', settings);
    },
    updateCondition (uname, value) {
      const settings = {
        ...this.modelValue,
        filters: {
          ...this.modelValue.filters,
          [uname]: {
            ...this.modelValue.filters[uname],
            default_condition: { ...value },
          },
        },
      };
      this.$emit('update:modelValue', settings);
    },
    updateFilterHiddenSetting (uname, hidden) {
      const settings = {
        ...this.modelValue,
        filters: {
          ...this.modelValue.filters,
          [uname]: {
            ...this.modelValue.filters[uname],
            hidden,
          },
        },
      };
      this.$emit('update:modelValue', settings);
    },
    buildPermissionConfigs (rowBasedPermissionRules) {
      return {
        ...this.modelValue.permissions,
        row_based: map(rowBasedPermissionRules, r => ({
          path: buildFieldPathObject(r.selectedDataSet, r.selectedDataModel, r.selectedField),
          ...r.selectedCondition,
        })),
      };
    },
    updateUserAttributes (userAttributes) {
      this.$emit('update:modelValue', {
        ...this.modelValue,
        user_attributes: userAttributes,
      });
    },
  },
};
</script>
<style lang="postcss" scoped>
.embed-builder {
  flex: 1.5 1;
  overflow: auto;

  .permission-and-filter {
    @apply bg-gray-50; /* $color-light-1 -> bg-gray-50 */
  }
}
</style>
