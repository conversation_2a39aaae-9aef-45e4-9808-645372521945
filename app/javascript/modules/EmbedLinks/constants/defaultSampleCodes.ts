import { check } from '@holistics/feature-toggle';

const embedDashboardUAEnabled = check('embed_link:embed_dashboard_user_attributes');

export default {
  ruby: {
    name: '<PERSON>',
    backendCode: `# Example code below uses JWT for encoding
# Execute 'gem install jwt' (or add gem 'jwt' to your GemFile) to install it first

embed_code = '{{ embed_code }}'
secret_key = '{{ secret_key }}'

# Will expire after 1 day, change it to the value you want.
expired_time = Time.now.to_i + 24 * 60 * 60
settings = {{ settings }}
permissions = {{ permissions }}
filters = {{ filters }}
${embedDashboardUAEnabled ? 'user_attributes = {{ user_attributes }}' : ''}
payload = {
  settings: settings,
  permissions: permissions,
  filters: filters,
  ${embedDashboardUAEnabled ? 'user_attributes: user_attributes,' : ''}
  exp: expired_time,
}
token = JWT.encode(payload, secret_key, 'HS256')
`,
    frontendCode: `<!-- Embedded Ruby(erb) template -->
<iframe src="{{ url }}/<%= embed_code %>?_token=<%= token %>"
  id="embedded-iframe"
  style="width: 100%; height: 600px;"
  frameborder="0"
  allowfullscreen>
</iframe>
<script src="main.js"></script>`,
  },
  javascript: {
    name: 'NodeJs',
    backendCode: `// Example code below uses jsonwebtoken for encoding
// Execute 'npm install jsonwebtoken' first to install.

var jwt = require('jsonwebtoken');

var embed_code = "{{ embed_code }}";
var secret_key = "{{ secret_key }}"

// Will expire after 1 day, change it to the value you want
var expired_time = Math.floor(Date.now() / 1000) + (24 * 60 * 60);
var settings = {{ settings }}
var permissions = {{ permissions }}
var filters = {{ filters }}
${embedDashboardUAEnabled ? 'user_attributes = {{ user_attributes }}' : ''}
var payload = {
  settings: settings,
  permissions: permissions,
  filters: filters,
  ${check('embed_link:embed_dashboard_user_attributes') ? 'user_attributes: user_attributes,' : ''}
  exp: expired_time
}
var token = jwt.sign(payload, secret_key);`,
    frontendCode: `<!-- Handlebars.js template -->
<iframe src="{{ url }}/{{embed_code}}?_token={{token}}"
  id="embedded-iframe"
  style="width: 100%; height: 600px;"
  frameborder="0"
  allowfullscreen>
</iframe>
<script src="main.js"></script>`,
  },
  python: {
    name: 'Python',
    backendCode: `# Example code below uses pyJWT for encoding
# Please execute 'pip install PyJWT' to install it first

import time
import jwt
import json

embed_code = "{{ embed_code }}"
secret_key = "{{ secret_key }}"

# Will expire after 1 day, change it to the value that you want
expired_time = int(time.time()) + 24 * 60 * 60
settings = {{ settings }}
permissions = {{ permissions }}
filters = {{ filters }}
${embedDashboardUAEnabled ? 'user_attributes = {{ user_attributes }}' : ''}
payload = {
  "settings": settings,
  "permissions": permissions,
  "filters": filters,
  ${embedDashboardUAEnabled ? '"user_attributes": user_attributes,' : ''}
  "exp": expired_time
}

token = jwt.encode(payload, secret_key, 'HS256')
token = token.decode('utf-8') # This is to remove b-prefix of byte literals`,
    frontendCode: `<!-- Below code uses Jinja (template engine) syntax -->
<iframe src="{{ url }}/<%= embed_code %>?_token=<%= token %>"
  id="embedded-iframe"
  style="width: 100%; height: 600px;"
  frameborder="0"
  allowfullscreen>
</iframe>
<script src="main.js"></script>`,
  },
  elixir: {
    name: 'Elixir',
    backendCode: `"""
Example code below uses JSON Web Token for encoding
Please refer to this link for the installation of JSON Web Token:
https://github.com/garyf/json_web_token_ex
"""

import JsonWebToken

embed_code = "{{ embed_code }}"
secret_key = "{{ secret_key }}"

"""
Will expire after 1 day, change it to the value that you want
"""
expired_time = DateTime.to_unix(DateTime.utc_now) + 24 * 60
settings = {{ settings }}
filters = {{ filters }}
permissions = {{ permissions }}
${embedDashboardUAEnabled ? 'user_attributes = {{ user_attributes }}' : ''}
payload = %{
  settings: Poison.encode!(settings),
  filters: Poison.encode!(filters),
  permissions: Poison.encode!(permissions),
  ${embedDashboardUAEnabled ? 'user_attributes: Poison.encode!(user_attributes),' : ''}
  exp: expired_time
}
token = JsonWebToken.sign(payload, %{key: secret_key})`,
    frontendCode: `<!-- Below code uses Phoenix template syntax -->
<iframe src="{{ url }}/<%= embed_code %>?_token=<%= token %>"
  id="embedded-iframe"
  style="width: 100%; height: 600px;"
  frameborder="0"
  allowfullscreen>
</iframe>
<script src="main.js"></script>`,
  },
  others: {
    name: 'Others',
    backendCode: `
In case you don't use any of our example languages,
you can follow these easy steps to implement it yourself:
* We are using JWT to create a security token, pick your
favourite JWT library, maybe you can find it at https://jwt.io/
* Get encoded \`token\` with these JWT options:
  > Hashing algorithm: HS256
  > Build your filter config following these options:
    const filters = {
      filter_name: {
        hidden: boolean,
        default_condition: {
          operator: 'YOUR_EXPECTED_OPERATOR',
          values: [YOUR_EXPECTED_VALUES],
          modifier: 'YOUR_EXPECTED_MODIFIER'
        }
        ...
      }
    };
    const permissions = {
      "row_based": [
        {
          "path": "\`dataset\`.\`data_model\`.\`field\`",
          "operator": 'YOUR_EXPECTED_OPERATOR',
          "values": [YOUR_EXPECTED_VALUES],
          "modifier": 'YOUR_EXPECTED_MODIFIER'
        }
        ...
      ]
    }
    const settings = {
      "enable_export_data": [BOOLEAN]
    }
    ${embedDashboardUAEnabled ? `const user_attributes = {
      "ATTRIBUTE_NAME": ATTRIBUTE_VALUES
    }` : ''}
  > Payload: Expire time (\`exp\` in Unix Timestamp) example:
    \`\`\`
      {
        "exp": 1537867461,
        "filters": filters,
        "permissions": permissions,
        ${embedDashboardUAEnabled ? '"user_attributes": user_attributes' : ''}
      }
    \`\`\`
  > Sign it with Embed Link's secret key
* Use the encoded \`token\` above and \`embed_code\`
render the iframe below in your web app.`,
    frontendCode: `<!-- Below code using Handlebars.js template -->
<iframe src="{{ url }}/{{embed_code}}?_token={{token}}"
  id="embedded-iframe"
  style="width: 100%; height: 600px;"
  frameborder="0"
  allowfullscreen>
</iframe>
<script src="main.js"></script>`,
  },
};
