import type { AttributeValuesType } from '../EmbedPortal/types';

export interface EmbedLink {
  id: number
  hash_code: string
  secret_key: string
  source_type: string
  source_id: number
  source_title: string
  source_to_param: string
  version: number
  owner: string
  title: string
  filter_values: Array<Record<string, unknown>>
}

export interface ActionBased {
  explore?: {
    datasets: string[],
  };
}

export interface EmbedConfigs {
  settings: {
    enable_export_data: boolean
    default_timezone: string | null
    allow_to_change_timezone: boolean
    hide_header_panel: boolean
    hide_control_panel: boolean
    enable_dashboard_export?: boolean
  },
  permissions: {
    row_based: unknown[],
    action_based?: ActionBased,
  },
  filters: Record<string, Record<any, any>>,
  user_attributes: Record<string, AttributeValuesType>,
}
