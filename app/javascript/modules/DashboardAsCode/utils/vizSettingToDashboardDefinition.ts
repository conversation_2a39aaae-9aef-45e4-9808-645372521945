import type VizSetting from '@/modules/Viz/models/VizSetting';
import { generateNameFromLabel } from '@aml-studio/client/modules/common/utils/generateNameFromLabel';
import type { DashboardDefinition } from '@holistics/aml-std';
import { DEFAULT_THEME } from '@/modules/DynamicDashboards/utils/genAMLCode';
import { DEFAULT_CANVAS_LAYOUT } from '../constants/defaultDashboard';
import generateVizBlock from './generateVizBlock';
import { generateDefaultBlockPosition } from './layouts';

type VizSettingToDashboardDefinitionParams = {
  vizSetting: VizSetting,
  blockTitle: string,
  dashboardTitle: string,
  blockDescription?: string,
  datasetId: number,
}

export const vizSettingToDashboardDefinition = ({
  vizSetting, blockTitle, dashboardTitle, blockDescription, datasetId,
}: VizSettingToDashboardDefinitionParams): DashboardDefinition => {
  const vizBlock = generateVizBlock({
    existingUnames: [],
    label: blockTitle,
    description: blockDescription || '',
    datasetId,
    vizSetting,
  });

  const layout = DEFAULT_CANVAS_LAYOUT;
  const blockPosition = generateDefaultBlockPosition(layout, vizBlock);
  blockPosition.position.x += 20;
  blockPosition.position.y += 20;

  const uname = generateNameFromLabel(dashboardTitle);

  return {
    uname,
    title: dashboardTitle,
    description: '',
    blocks: [vizBlock],
    interactions: [],
    views: [{
      ...layout,
      blocks: {
        [vizBlock.uname]: blockPosition,
      },
    }],
    settings: { autorun: true, allow_timezone_change: false },
    theme: DEFAULT_THEME,
  };
};
