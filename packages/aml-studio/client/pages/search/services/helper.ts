import type {
  SearchMatch,
  SearchResult,
} from '@aml-studio/client/pages/search/types';
import {
  PropertyType,
  FileType,
  FieldType,
} from '../constants';
import { mergeHashesWithCommonKeys } from '../../../utils/hash';

function getTruncatedContent (content: string, matchStart: number, matchEnd: number) {
  const contextLength = 12;

  const start = Math.max(0, matchStart - contextLength);
  const end = Math.min(content.length, matchEnd + contextLength);

  const prefix = start > 0 ? '... ' : '';
  const suffix = end < content.length ? ' ...' : '';

  return prefix + content.substring(start, end) + suffix;
}

function removeQuotes (text: string): string {
  return text.replace(/^['"](.*)['"]$/, '$1');
}

function containsQuotes (token: string): boolean {
  return token.startsWith('"') || token.startsWith("'");
}

function containsColon (token: string): boolean {
  return token.includes(':');
}

function isPropertyType (filterValue: string): filterValue is PropertyType {
  return Object.values(PropertyType).includes(filterValue as any);
}

function isFileType (value: string): value is FileType {
  return Object.values(FileType).includes(value as any);
}

function isAMLFieldFilter (value: string): value is FieldType {
  return [
    FieldType.OWNER,
    FieldType.DATASOURCE,
  ].includes(value as any);
}

function isAMLObjectType (value: string): value is PropertyType {
  return [
    PropertyType.Dimension,
    PropertyType.Measure,
    PropertyType.Metric,
    FileType.Model,
    FileType.Dataset,
    FileType.Dashboard,
  ].includes(value as any);
}

function isFileObjectType (value: string): value is FileType {
  return [
    FileType.Model,
    FileType.Dataset,
    FileType.Dashboard,
  ].includes(value as any);
}

// Add this new function to escape special regex characters
function escapeRegExp (text: string) {
  return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function mergeTextMatches (matches1: Record<string, SearchMatch[]>, matches2: Record<string, SearchMatch[]>): Record<string, SearchMatch[]> {
  return mergeHashesWithCommonKeys(matches1, matches2);
}

function searchMatchesToSearchResult (data: Record<string, SearchMatch[]>): SearchResult[] {
  return Object.entries(data)
    .map(([filePath, matches]) => ({
      filePath,
      matches,
      hasMatch: matches.length > 0,
    }));
}

function buildRegexPatternFromTextMatches (matches: SearchMatch[]) {
  return matches.map(match => escapeRegExp(match.text)).join('|');
}

export {
  searchMatchesToSearchResult,
  getTruncatedContent,
  removeQuotes,
  containsQuotes,
  containsColon,
  isPropertyType,
  escapeRegExp,
  mergeHashesWithCommonKeys,
  mergeTextMatches,
  isFileType,
  isAMLObjectType,
  isFileObjectType,
  isAMLFieldFilter,
  buildRegexPatternFromTextMatches,
};
