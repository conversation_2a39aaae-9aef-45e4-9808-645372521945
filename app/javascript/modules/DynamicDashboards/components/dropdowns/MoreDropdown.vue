<!--<docs>
  The more dropdown for the dashboard control
</docs>-->
<template>
  <div class="relative">
    <HDropdown
      :options="options"
      placement="bottom-end"
      :disabled="disabled"
    >
      <HButton
        :disabled="disabled"
        unified
        icon="ellipsis-horizontal"
        class="ci-preferences-toggle relative"
        type="tertiary-default"
        size="sm"
      />

      <template #performance-analytics>
        <!-- TODO: Restyle links for Dropdown Option -->
        <router-link
          class="flex cursor-pointer items-start justify-center space-x-1 rounded p-2 hover:bg-gray-100 active:bg-gray-400"
          :to="{ name: $options.jobMonitoringDashboardRoute, query: { filter_dashboard: `${$_dashboard.title} | ${$_dashboard.id}` } }"
          target="_blank"
        >
          <h-icon
            name="chart/gauge"
          />
          <span>Performance Analytics</span>
          <FeatureTag
            inline
            class="!px-1 !py-0"
          />
        </router-link>
      </template>
    </HDropdown>
    <OnboardingQueue v-if="isOnboardingReady" />
  </div>
</template>
<script>
import { HDropdown, HButton, useModal } from '@holistics/design-system';
import { success } from '@/core/services/notifier';
import { check as checkFeatureToggle } from '@/core/services/featureToggle';
import { handleAjaxError } from '@/core/services/ajax';
import FeatureTag from '@/core/components/ui/FeatureTag.vue';
import { lock as lockObject, unlock as unlockObject } from '@/core/services/objectLocks.ajax';
import PinnedItems from '@/modules/PinnedItems/services/pinnedItems.ajax';
import moveDashboardModal from '@/modules/Dashboards/services/modals/moveDashboard.modal';
import copyDashboardModal from '@/modules/Dashboards/services/modals/copyDashboard.modal';
import { batchDeleteModal } from '@holistics/node-tree';
import preferencesModal, { Tabs as PreferenceTabs } from '@/modules/Preferences/services/modals/preferences.modal';
import { SECTIONS } from '@/modules/Management/components/jobs/constants/jobMonitoringSections';
import { mapActions } from 'vuex';
import {
  pick, isNil, get, delay,
} from 'lodash';
import { CATEGORIES, ITEMS } from '@holistics/node-tree/helpers/constants';
import { trackPinDashboardUsage } from '@/modules/DashboardAsCode/utils/pinDashboardUsageTracking';

import SuggestEditUserAccessModal from '../modals/SuggestEditUserAccessModal.vue';
import dashboardInjection from '../../mixins/dashboardInjection';
import { convertToV1 } from '../../services/dashboards.ajax';
import { generateDashboardAsCode } from '../../services/modals/generateDashboardAsCode';
import { alreadySharedWithAllActors } from '../../utils/fetchActorsAccess';
import OnboardingQueue from './OnboardingQueue.vue';

export default {
  name: 'MoreDropdown',
  components: {
    HDropdown,
    HButton,
    FeatureTag,
    OnboardingQueue,
  },
  setup () {
    const { open: openHModal } = useModal();
    return { openHModal };
  },
  PreferenceTabs,
  codeGenEnabled: checkFeatureToggle('dashboards_v4:codegen'),
  jobMonitoringDashboardEnabled: checkFeatureToggle('tenant:job_monitoring_dashboard'),
  jobMonitoringDashboardRoute: SECTIONS.JobDashboard.key,
  mixins: [dashboardInjection],
  props: {
    resource: {
      type: Object,
      required: true,
      validator (value) {
        return !isNil(value.id);
      },
    },
    resourceType: {
      type: String,
      required: true,
    },
    dashboardFilterConditions: {
      type: Object,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data () {
    return {
      isOnboardingReady: false,
    };
  },
  computed: {
    options () {
      return [
        this.$_permissions.can_update && {
          key: 'dashboard-preferences',
          label: 'Dashboard Preferences',
          icons: 'configuration',
          class: 'ci-preference',
          action: () => this.openPreferences(),
        },
        (this.$_permissions.can_share && this.dashboardV3EmbedEnabled) && { type: 'divider' },
        (this.$_permissions.can_share && this.dashboardV3EmbedEnabled) && {
          key: 'embedded-analytics',
          label: 'Embedded Analytics',
          icons: 'code',
          class: 'ci-embedded-analytics',
          action: () => this.openShareModal(PreferenceTabs.EmbeddedAnalytics),
        },
        { type: 'divider' },
        (checkFeatureToggle('dashboards_v3:conversion') && this.$_permissions.can_update) && {
          key: 'convert-to-dashboard-v1',
          label: 'Convert to Dashboard V1',
          icons: 'chevron-down',
          class: 'ci-convert-to-v1',
          action: () => this.convertToV1(),
        },
        (this.$options.codeGenEnabled && this.$_permissions.can_update) && {
          key: 'generate-dashboard-code',
          label: 'Generate Canvas Dashboard',
          icons: 'canvas',
          class: 'ci-gen-dashboard-code',
          action: () => this.generateDashboardAsCode(),
        },
        (this.$user.isAdmin && this.$options.jobMonitoringDashboardEnabled) && {
          key: 'performance-analytics',
          slot: 'performance-analytics',
        },
        (checkFeatureToggle('dashboards:pin') && this.$_permissions.can_pin) && {
          key: 'pin',
          label: !this.$_isPinned ? 'Pin to Org Homepage' : 'Unpin from Org Homepage',
          icons: !this.$_isPinned ? 'pin' : 'unpin',
          class: 'ci-toggle-pin-dashboard',
          action: () => this.togglePin(),
        },
        this.$_permissions.can_lock && {
          key: 'lock',
          label: !this.$_isLocked ? 'Lock' : 'Unlock',
          icons: !this.$_isLocked ? 'lock' : 'unlock',
          action: () => (!this.$_isLocked ? this.lock() : this.unLock()),
        },
        this.$_permissions.can_read && {
          key: 'copy',
          label: 'Copy to...',
          icons: 'clone',
          class: 'ci-copy',
          action: () => this.copy(),
        },
        this.$_permissions.can_crud && {
          key: 'move',
          label: 'Move to...',
          icons: 'folder',
          class: 'ci-move',
          action: () => this.move(),
        },
        this.$_permissions.can_destroy && {
          key: 'delete',
          label: 'Delete',
          icons: 'delete',
          class: 'ci-remove text-red-500',
          action: () => this.destroy(),
        },
      ].filter(Boolean);
    },
    dashboardV3EmbedEnabled () {
      return !checkFeatureToggle('embedded_analytics:hide') && this.resource.is_v3 && this.$user.isAdmin;
    },
  },
  created () {
    this.$_events.$on('openDashboardPreferences', this.openPreferences);
  },
  async mounted () {
    // wait for next tick and delay before checking delay to popover mount last the position of icon
    await this.$nextTick();
    delay(() => {
      this.isOnboardingReady = true;
    }, 300);
  },
  beforeUnmount () {
    this.$_events.$off('openDashboardPreferences', this.openPreferences);
  },
  methods: {
    ...mapActions(
      'tree',
      {
        handleDeletedNode: 'handleDeletedNode',
      },
    ),
    openShareModal (initialTab = PreferenceTabs.UserAccess) {
      preferencesModal(
        this.resourceType,
        this.resource,
        initialTab,
        this.dashboardFilterConditions,
      );
    },
    async convertToV1 () {
      try {
        const confirmed = await this.$modal.confirm('Convert to v1', 'Existing filters (3.0) will be missing. To reuse them, convert back to v3');
        if (!confirmed) {
          return;
        }
        await convertToV1(this.$_dashboard.id);
        success('Converted dashboard to v1');
        await this.$router.replace(`/dashboards/${this.$route.params.slug}`);
      } catch (err) {
        handleAjaxError(err, 'Error converting dashboard');
      }
    },
    generateDashboardAsCode () {
      return generateDashboardAsCode({
        dashboard: this.$_dashboard,
        filters: this.$_filters,
        widgetModelingSources: this.$_modelingSources,
      });
    },
    async lock () {
      try {
        await lockObject('Dashboard', this.$_dashboard.id);
        this.$_events.$emit('update:isLocked', true);
        success('Dashboard locked');
      } catch (error) {
        handleAjaxError(error, 'Error locking dashboard');
      }
    },
    async unLock () {
      try {
        await unlockObject('Dashboard', this.$_dashboard.id);
        this.$_events.$emit('update:isLocked', false);
        success('Dashboard unlocked');
      } catch (error) {
        handleAjaxError(error, 'Error unlocking dashboard');
      }
    },
    async togglePin () {
      try {
        await PinnedItems.toggle(this.$_dashboard.id, 'Dashboard');
        this.$_events.$emit('update:isPinned', !this.$_isPinned);
        success(`Dashboard ${this.$_isPinned ? 'pinned' : 'unpinned'}`);
        if (this.$_isPinned) {
          trackPinDashboardUsage(this.$_dashboard.id, this.$user.id);

          this.$_setIsLoading(true);
          const isSharedAll = await alreadySharedWithAllActors(this.$_dashboard);
          this.$_setIsLoading(false);
          if (!isSharedAll) {
            await this.openHModal(SuggestEditUserAccessModal, {
              funcOpenUserAccessModal: this.openShareModal,
            });
          }
        }
      } catch (error) {
        handleAjaxError(error, `Error ${!this.$_isPinned ? 'pinning' : 'unpinning'} dashboard`);
      }
    },
    async copy () {
      await copyDashboardModal(
        pick(this.$_dashboard, ['id', 'title']),
      );
    },
    async move () {
      await moveDashboardModal(
        pick(this.$_dashboard, ['id', 'category_id', 'personal_item']),
      );
    },
    destroy () {
      const db = this.$_dashboard;
      const isPersonal = !!db.personal_item;
      const item = isPersonal ? { id: db.personal_item.id, type: ITEMS.PersonalItem } : { id: db.id, type: CATEGORIES.Dashboard };
      const parentType = isPersonal ? CATEGORIES.PersonalCategory : CATEGORIES.ReportCategory;

      batchDeleteModal(parentType, [item]);
    },
    async openPreferences () {
      const { data, status } = await preferencesModal(
        'Dashboard',
        this.$_dashboard,
        PreferenceTabs.GeneralV3,
        this.$_filters.conditions,
      );
      const db = get(data, 'source');

      if (status !== 'resolved' || !db) { return; }

      // TODO: refactor this since more timezone logic is mixed here
      // constraints:
      // - need to check for change before update to store
      // - only trigger rerun after update to store to make sure widgets rerun in new timezone
      const hasTimezoneChanged = db.settings.timezone !== this.$_dashboard.settings.timezone;
      const disableAllowToChangeTimezone = !db.settings.allow_to_change_timezone && this.$_dashboard.settings.allow_to_change_timezone;

      // update to store
      this.$_updateDashboardMetadata(db);

      if (hasTimezoneChanged || disableAllowToChangeTimezone) {
        // after change timezone or disable allow to change timezone
        // re-run widget to avoid showing wrong data
        this.$_events.$emit('refresh:run');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.h-analyst-nux {
  position: absolute;
  top: -8px;
  right: -6px;
}
</style>
