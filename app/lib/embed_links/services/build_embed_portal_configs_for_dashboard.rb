# frozen_string_literal: true
# typed: true

module EmbedLinks::Services
  class BuildEmbedPortalConfigsForDashboard < T::Struct
    const :user, User
    const :dashboard, Dashboard

    def embed_link
      user.public_link
    end

    sig { params(embed_payload: Hash).returns(EmbedLinks::Values::EmbedPortalConfigs) }
    def call(embed_payload)
      settings = build_settings(embed_payload[:settings] || {})
      permissions = DataModeling::Services::BuildPermissionRulesFromHash.new(user: user).call(embed_payload[:permissions] || {})
      drillthroughs = EmbedLinks::Services::BuildDrillthroughConfigsFromHash.new(embed_link: embed_link).call(embed_payload[:drillthroughs] || {})

      embed_dashboard = EmbedLinks::Values::EmbedDashboardConfigs.new(
        id: T.must(dashboard.id),
        filters: build_filter_configs(embed_payload[:filters] || {}),
        settings: EmbedLinks::Values::EmbedDashboardConfigs::Settings.from_hash(settings.to_h),
      )

      user_attributes = {}

      if FeatureToggle.active?(EmbedLink::FT_EMBED_DASHBOARD_USER_ATTRIBUTES, user.tenant_id)
        raw_user_attributes = embed_payload[:user_attributes] || {}
        user_attributes =
          DataModeling::Services::BuildEmbedUserAttributesFromHash
          .new(user: user)
          .call(raw_user_attributes)
      end

      EmbedLinks::Values::EmbedPortalConfigs.new(
        embed_objects: [embed_dashboard],
        permissions: permissions,
        user_attributes: user_attributes,
        settings: settings,
        drillthroughs: drillthroughs,
        expires_at: embed_payload[:exp] ? Time.at(embed_payload[:exp]) : nil,
        project_id: dashboard.project_id,
        source_type: EmbedLinks::Values::EmbedPortalConfigs::SourceType::Dashboard,
        source_name: dashboard.uname,
        source_id: dashboard.id,
        embed_link_id: user.public_link.id,
      )
    end

    private

    lazy; sig { returns(T::Array[DynamicFilter::TypeDynamicFilter]) }
    def dynamic_filters
      if dashboard.is_v4?
        dashboard.filter_blocks
      else
        # includes definition to get the labels
        dashboard.dynamic_filters.includes(:dynamic_filter_definition).all.to_a
      end
    end

    lazy
    sig { returns(T::Hash[String, DynamicFilter::TypeDynamicFilter]) }
    def dynamic_filters_by_uname
      unames_map = DynamicFilters::Services::MapUnames.new.call(dynamic_filters)
      dynamic_filters.to_h do |df|
        [unames_map[df.id.to_s], df]
      end
    end

    sig { params(filters_hash: T.untyped).returns(DynamicFilters::Values::FilterUiConfigsMapping) }
    def build_filter_configs(filters_hash)
      ::Utils.assert_type(filters_hash, Hash, '`filters`', user_input: true)

      return {} if filters_hash.blank?

      filters_hash.to_h do |filter_name, config_hash|
        df = dynamic_filters_by_uname[filter_name]

        unless df
          raise Holistics::InvalidParameter,
                "Filter `#{filter_name}` not found in this #{dashboard.friendly_type}. Please contact your site admin to recheck configurations"
        end

        raise Holistics::InvalidParameter, "Invalid configs for filter `#{filter_name}`" unless config_hash.is_a?(Hash)

        config = EmbedLinks::Services::BuildFilterUiConfigs.new(dynamic_filter: df).call(config_hash)

        [df.id.to_s, config]
      end
    end

    sig { params(settings_hash: T.untyped).returns(EmbedLinks::Values::Settings) }
    def build_settings(settings_hash)
      ::Utils.assert_type(settings_hash, Hash, '`settings`', user_input: true)
      TypeCoerce[EmbedLinks::Values::Settings].new.from(settings_hash, *{})
    end
  end
end
