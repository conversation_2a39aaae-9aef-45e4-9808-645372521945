import {
  ref, reactive, computed, type Ref, watch,
} from 'vue';
import {
  useEventListener, useDebounceFn, useThrottleFn, type Fn,
} from '@vueuse/core';
import {
  cloneDeep, isEqual, uniq,
} from 'lodash';
import { hideAllPoppers } from '@holistics/floating-vue';
// import { hideAllPoppers as hap } from '@holistics/design-system';
import { isEditableElement } from '@holistics/utils/ui';
import type { BlockPosition, Position } from '@holistics/aml-std';
import eventBus from '@/core/services/eventBus';
import { hideAllDatePickers } from '@/core/components/ui/DatePickers/utils/hideAllDatePickers';
import { useCanvasAddons } from './useCanvasAddons';
import { useAutoScroll } from './useAutoScroll';
import {
  CANVAS_UNAME, ITEMS_GROUP_UNAME, LEFT_MOUSE_BUTTON, ITEM_MOVE_HANDLE_CLASS,
} from './constants';
import { isOverlapped } from '../../utils/layouts';

interface UseCanvasPositionOptions {
  editMode: Ref<boolean>
  canvasEl: Ref<HTMLElement | undefined>
  scrollBody: Ref<HTMLElement | undefined>
  zoomLevel: Ref<number>
  smartMode: Ref<boolean>
  snapToGrid: Ref<boolean>
  gridSize: Ref<number>
  ignoreSelectors?: string[]
  ignoreMovingSelectors?: string[]
  dragAndDropDisabledItems: Ref<string[]>
  onDragEnd: (params: { type: 'items', position: Record<string, BlockPosition> } | { type: 'canvas', size: { w: number, h: number } }) => void
  onDelete: (items: string[]) => void
  onSelect: (items: string[]) => void
}

export interface SelectRect {
  start?: {
    top: number
    left: number
  }
  end?: {
    top: number
    left: number
  }
}

export function useCanvasPosition (width: Ref<number>, height: Ref<number>, items: Ref<Record<string, BlockPosition>>, {
  editMode, canvasEl, scrollBody, zoomLevel, smartMode,
  snapToGrid, gridSize, ignoreSelectors,
  ignoreMovingSelectors,
  dragAndDropDisabledItems,
  onDragEnd, onDelete, onSelect,
}: UseCanvasPositionOptions) {
  const canvasPosition = reactive<BlockPosition>({
    layer: 0,
    position: {
      x: 0,
      y: 0,
      w: width.value,
      h: height.value,
    },
  });
  const itemsPositions = ref(cloneDeep(items.value));
  const ghostItemsPosition = ref<Record<string, BlockPosition>>({});
  const lastPositions = ref<Record<string, BlockPosition>>({});
  const activeItems = ref<string[]>([]);

  const resizeDirections = reactive({
    top: false,
    bottom: false,
    left: false,
    right: false,
  });

  // blocks
  const moving = ref(false);
  const resizing = ref(false);
  const working = computed(() => moving.value || resizing.value);
  const lastX = ref(0);
  const lastY = ref(0);
  const mouseMoving = ref(false);
  const deltaX = ref(0);
  const deltaY = ref(0);

  // select rect
  const lastActiveItems = ref<string[]>([]);
  const scrollBodyToCanvasShift = ref<{ top: number, left: number }>({ top: 0, left: 0 });
  const selectRect = reactive<SelectRect>({});

  function clearSelectRect () {
    lastActiveItems.value = [];
    scrollBodyToCanvasShift.value = { top: 0, left: 0 };
    selectRect.start = undefined;
    selectRect.end = undefined;
  }

  const {
    onItemVisibilityChange,
    overlappedItems,
    alignedItems,
    alignmentLines,
    selectedItemsGroupPosition,
  } = useCanvasAddons(itemsPositions, ghostItemsPosition, {
    activeItems,
    working,
    mouseMoving,
  });

  const { autoScroll, reset: resetAutoScroll } = useAutoScroll(scrollBody, {
    horizontalBuffer: Math.round(window.document.documentElement.clientWidth / 10),
    verticalBuffer: Math.round(window.document.documentElement.clientHeight / 10),
    step: 50,
  });

  function getBodyScrollX () {
    if (scrollBody.value) {
      return scrollBody.value.scrollLeft;
    }
    return window.scrollX;
  }

  function getBodyScrollY () {
    if (scrollBody.value) {
      return scrollBody.value.scrollTop;
    }
    return window.scrollY;
  }

  function snapValueIfNeeded (value: number) {
    if (!snapToGrid.value || !gridSize.value) {
      return Math.round(value);
    }
    return Math.round(value / gridSize.value) * gridSize.value;
  }

  function resetLastData (e?: MouseEvent) {
    mouseMoving.value = false;
    lastX.value = (e?.clientX ?? 0) + getBodyScrollX();
    lastY.value = (e?.clientY ?? 0) + getBodyScrollY();
    deltaX.value = 0;
    deltaY.value = 0;

    lastPositions.value = activeItems.value.reduce<Record<string, BlockPosition>>((acc, uname) => {
      if (uname === CANVAS_UNAME) {
        acc[uname] = cloneDeep(canvasPosition);
      } else {
        acc[uname] = cloneDeep(itemsPositions.value[uname]);
      }
      return acc;
    }, {});

    // NOTE: don't clone the selected items group position
    ghostItemsPosition.value = cloneDeep(lastPositions.value);

    if (selectedItemsGroupPosition.value) {
      lastPositions.value[ITEMS_GROUP_UNAME] = cloneDeep(selectedItemsGroupPosition.value);
    }
  }

  function calculateDeltaXY (e: MouseEvent) {
    mouseMoving.value = true;
    const currentX = e.clientX + getBodyScrollX();
    const currentY = e.clientY + getBodyScrollY();
    deltaX.value = (currentX - lastX.value) / zoomLevel.value;
    deltaY.value = (currentY - lastY.value) / zoomLevel.value;
  }

  const hideAllPopovers = useThrottleFn(() => {
    hideAllDatePickers();
    hideAllPoppers();
    // hap();
  }, 1000);

  function handleMove () {
    activeItems.value.forEach((uname) => {
      const lastPos = lastPositions.value[uname]?.position;
      const currPos = itemsPositions.value[uname]?.position;
      if (lastPos && currPos) {
        currPos.x = lastPos.x + deltaX.value;
        currPos.y = lastPos.y + deltaY.value;
      }
    });
  }

  // NOTE: this function modifies `currPos`
  function updateCurrentPosition (currPos: Position, lastPos: Position) {
    if (resizeDirections.top) {
      if (deltaY.value > lastPos.h) {
        currPos.y = lastPos.y + lastPos.h;
        currPos.h = 0;
      } else {
        currPos.y = lastPos.y + deltaY.value;
        currPos.h = Math.max(lastPos.h - (currPos.y - lastPos.y), 0);
      }
    }
    if (resizeDirections.left) {
      if (deltaX.value > lastPos.w) {
        currPos.x = lastPos.x + lastPos.w;
        currPos.w = 0;
      } else {
        currPos.x = lastPos.x + deltaX.value;
        currPos.w = Math.max(lastPos.w - (currPos.x - lastPos.x), 0);
      }
    }
    if (resizeDirections.bottom) {
      const newH = Math.max(lastPos.h + deltaY.value, 0);
      currPos.h = newH;
    }
    if (resizeDirections.right) {
      const newW = Math.max(lastPos.w + deltaX.value, 0);
      currPos.w = newW;
    }
  }

  function resizeItemsGroup (itemsGroup: string[]) {
    const currGroupPos = cloneDeep(selectedItemsGroupPosition.value?.position);
    const lastGroupPos = lastPositions.value[ITEMS_GROUP_UNAME]?.position;
    if (!currGroupPos || !lastGroupPos) {
      return;
    }
    updateCurrentPosition(currGroupPos, lastGroupPos);
    const scaleX = currGroupPos.w / lastGroupPos.w;
    const scaleY = currGroupPos.h / lastGroupPos.h;

    itemsGroup.forEach(uname => {
      const lastPos = lastPositions.value[uname]?.position;
      const currPos = itemsPositions.value[uname]?.position;
      if (lastPos && currPos) {
        let newX = lastPos.x;
        let newY = lastPos.y;
        const newW = lastPos.w * scaleX;
        const newH = lastPos.h * scaleY;
        // x
        if (resizeDirections.left) {
          const refX = lastGroupPos.x + lastGroupPos.w;
          newX = refX + (lastPos.x - refX) * scaleX;
        } else if (resizeDirections.right) {
          const refX = lastGroupPos.x;
          newX = refX + (lastPos.x - refX) * scaleX;
        }
        // y
        if (resizeDirections.top) {
          const refY = lastGroupPos.y + lastGroupPos.h;
          newY = refY + (lastPos.y - refY) * scaleY;
        } else if (resizeDirections.bottom) {
          const refY = lastGroupPos.y;
          newY = refY + (lastPos.y - refY) * scaleY;
        }

        currPos.x = newX;
        currPos.y = newY;
        currPos.h = newH;
        currPos.w = newW;
      }
    });
  }

  function resizeSingleItem (uname: string) {
    const lastPos = lastPositions.value[uname]?.position;
    const currPos = uname === CANVAS_UNAME ? canvasPosition.position : itemsPositions.value[uname]?.position;
    if (!lastPos || !currPos) {
      return;
    }
    updateCurrentPosition(currPos, lastPos);
  }

  function handleResize () {
    if (activeItems.value.length > 1) {
      resizeItemsGroup(activeItems.value);
    } else if (activeItems.value.length === 1) {
      resizeSingleItem(activeItems.value[0]);
    }
  }

  // for using with keyboard events
  const debouncedOnEnd = useDebounceFn(onDragEnd, 500);

  function shouldIgnoreInputEvent (e: MouseEvent | KeyboardEvent) {
    const target = e.target as HTMLElement | null;
    if (target && ignoreSelectors?.find(selector => target.closest(selector))) {
      return true;
    }
    return false;
  }

  function shouldIgnoreMovingEvent (e: MouseEvent) {
    const target = e.target as HTMLElement | null;
    if (target && ignoreMovingSelectors?.find(selector => target.closest(selector))) {
      return true;
    }
    return false;
  }

  // watchers for edit mode
  // for better performance, these watchers are only registered when editMode is true, and are canceled when editMode turns into false
  function handleMouseDown (e: MouseEvent) {
    if ((e.target as HTMLElement)?.closest(`.${ITEM_MOVE_HANDLE_CLASS}`) && activeItems.value.length) {
      // special case, this handle element is rendered inside a popover
      resetLastData(e);
      moving.value = true;

      // clear select rect
      clearSelectRect();

      return;
    }
    if (shouldIgnoreInputEvent(e)) {
      return;
    }
    resetAutoScroll();
    if (e.button !== LEFT_MOUSE_BUTTON) {
      activeItems.value = [];
      return;
    }

    const target = e.target as HTMLElement | null;
    const targetItem = target?.closest<HTMLElement>('.h-canvas-item');
    const targetUname = targetItem?.dataset.uname;

    if (target && scrollBody.value?.contains(target)) {
      const scrollingRect = scrollBody.value.getBoundingClientRect();
      const canvasRect = canvasEl.value?.getBoundingClientRect();
      // start drawing select rect, will reset later if needed
      if (e.shiftKey) {
        lastActiveItems.value = [...activeItems.value];
      }
      scrollBodyToCanvasShift.value = {
        top: Math.round((canvasRect?.top ?? 0) - scrollingRect.top + getBodyScrollY()),
        left: Math.round((canvasRect?.left ?? 0) - scrollingRect.left + getBodyScrollX()),
      };
      const point = {
        top: e.clientY - scrollingRect.top + getBodyScrollY(),
        left: e.clientX - scrollingRect.left + getBodyScrollX(),
      };
      selectRect.start = {
        ...point,
      };
      selectRect.end = {
        ...point,
      };
    }

    if (!targetItem || !targetUname) {
      activeItems.value = [];

      return;
    }

    if (targetUname === CANVAS_UNAME) {
      activeItems.value = [CANVAS_UNAME];
    } else if (targetUname === ITEMS_GROUP_UNAME) {
      // targeting the empty spaces inside selected items group box => do nothing
    } else if (e.shiftKey) {
      e.preventDefault();
      // multi select
      if (!activeItems.value.includes(targetUname)) {
        activeItems.value = [...activeItems.value, targetUname];
      } else {
        // deselect
        activeItems.value = activeItems.value.filter(u => u !== targetUname);
        // return immediately
        return;
      }
    } else if (!activeItems.value.includes(targetUname)) {
      // single select
      activeItems.value = [targetUname];
    }

    const targetClassList = Array.from(target?.classList ?? []);

    if (activeItems.value.some(item => dragAndDropDisabledItems.value.includes(item)) || shouldIgnoreMovingEvent(e)) {
      // drag and drop disabled, do nothing
      clearSelectRect();
    } else if (targetClassList.includes('__canvas-resize-handle')) {
      // resizing
      resetLastData(e);

      if ((activeItems.value.length > 1 || activeItems.value[0] !== targetUname) && targetUname !== ITEMS_GROUP_UNAME) {
        // currently, can resize a single item only, except resizing the whole selected group
        activeItems.value = [targetUname];
      }
      resizing.value = true;
      if (targetClassList.includes('b')) {
        resizeDirections.bottom = true;
      }
      // prevent resizing top & bottom at the same time (e.g when the box is too short)
      if (targetClassList.includes('t') && !resizeDirections.bottom) {
        resizeDirections.top = true;
      }
      if (targetClassList.includes('r')) {
        resizeDirections.right = true;
      }
      // prevent resizing left & right at the same time (e.g when the box is too narrow)
      if (targetClassList.includes('l') && !resizeDirections.right) {
        resizeDirections.left = true;
      }

      // clear select rect
      clearSelectRect();
    } else if (activeItems.value.includes(CANVAS_UNAME)) {
      // move: can't move canvas, reset
      activeItems.value = [];
    } else {
      // move: other items
      resetLastData(e);
      moving.value = true;

      // clear select rect
      clearSelectRect();
    }
  }

  const updateActiveItemsBySelectRect = useThrottleFn((additive = false) => {
    if (!selectRect.start || !selectRect.end) {
      return;
    }
    const selectRectInCanvas = {
      start: {
        top: (selectRect.start.top - scrollBodyToCanvasShift.value.top) / zoomLevel.value,
        left: (selectRect.start.left - scrollBodyToCanvasShift.value.left) / zoomLevel.value,
      },
      end: {
        top: (selectRect.end.top - scrollBodyToCanvasShift.value.top) / zoomLevel.value,
        left: (selectRect.end.left - scrollBodyToCanvasShift.value.left) / zoomLevel.value,
      },
    };

    const selectRectPos: BlockPosition['position'] = {
      x: Math.min(selectRectInCanvas.start.left, selectRectInCanvas.end.left),
      y: Math.min(selectRectInCanvas.start.top, selectRectInCanvas.end.top),
      w: Math.abs(selectRectInCanvas.end.left - selectRectInCanvas.start.left),
      h: Math.abs(selectRectInCanvas.end.top - selectRectInCanvas.start.top),
    };

    const newActiveItems = Object.keys(itemsPositions.value).filter(uname => isOverlapped(selectRectPos, itemsPositions.value[uname].position));

    if (additive) {
      activeItems.value = uniq([...lastActiveItems.value, ...newActiveItems]);
    } else {
      activeItems.value = newActiveItems;
    }
  }, 200, true);

  function areGhostItemsOverlappedWithOtherItems (ghostItemsPos: Record<string, BlockPosition>) {
    const ghostItems = Object.keys(ghostItemsPos);
    if (ghostItems.length === 0) {
      return false;
    }
    const otherItems = Object.keys(itemsPositions.value).filter(uname => !ghostItems.includes(uname));
    return ghostItems.some((uname) => {
      const ghostPos = ghostItemsPos[uname].position;
      return otherItems.some((otherUname) => isOverlapped(ghostPos, itemsPositions.value[otherUname].position, true));
    });
  }

  function calculateMovingGhostItemsPosition () {
    // Use current ghost position as the last position snapshot
    const backtrackUnit = snapToGrid.value ? gridSize.value : 5;
    const lastPosSnapshot = cloneDeep(ghostItemsPosition.value); // Use ghost as base
    let bestPosition = lastPosSnapshot;
    let minDist = Number.POSITIVE_INFINITY;

    // Calculate force vector for items
    // since we are moving items in group, they share the same force vector
    const sampleItem = activeItems.value.find(uname => uname !== CANVAS_UNAME && uname !== ITEMS_GROUP_UNAME);
    if (!sampleItem) {
      // no item to move => skip
      return;
    }
    const sampleItemPos = itemsPositions.value[sampleItem]?.position;
    const sampleItemGhostPos = ghostItemsPosition.value[sampleItem]?.position;
    if (!sampleItemPos || !sampleItemGhostPos) {
      // can't calculate force vector => skip
      return;
    }
    const forceVector = {
      dx: sampleItemPos.x - sampleItemGhostPos.x,
      dy: sampleItemPos.y - sampleItemGhostPos.y,
    };

    // Helper to compute distance to intended destination
    const getDist = (pos: Record<string, BlockPosition>) => {
      let sum = 0;
      const p = pos[sampleItem]?.position;
      if (p) {
        const tx = p.x + forceVector.dx;
        const ty = p.y + forceVector.dy;
        sum += Math.abs(p.x - tx) + Math.abs(p.y - ty);
      }
      return sum;
    };

    // Try to pull ghost position toward current item position, step by step
    const maxXSteps = Math.floor(Math.abs(forceVector.dx) / backtrackUnit);
    const maxYSteps = Math.floor(Math.abs(forceVector.dy) / backtrackUnit);

    let backtrack = lastPosSnapshot;
    // slide along x-axis
    for (let xStep = 0; xStep <= maxXSteps; xStep++) {
      for (let yStep = 0; yStep <= maxYSteps; yStep++) {
        const test: Record<string, BlockPosition> = {};
        activeItems.value.forEach((uname) => {
          const lastPos = lastPosSnapshot[uname];
          if (lastPos && uname !== ITEMS_GROUP_UNAME && uname !== CANVAS_UNAME) {
            test[uname] = {
              layer: lastPos.layer,
              position: {
                x: snapValueIfNeeded(lastPos.position.x + Math.sign(forceVector.dx) * xStep * backtrackUnit),
                y: snapValueIfNeeded(lastPos.position.y + Math.sign(forceVector.dy) * yStep * backtrackUnit),
                w: lastPos.position.w,
                h: lastPos.position.h,
              },
            };
          }
        });
        if (!areGhostItemsOverlappedWithOtherItems(test)) {
          backtrack = test;
        } else {
          break;
        }
      }
    }
    // Pick the closest to intended destination
    const slideXDist = getDist(backtrack);
    if (slideXDist < minDist) {
      minDist = slideXDist;
      bestPosition = backtrack;
    }
    // slide along y-axis
    for (let yStep = 0; yStep <= maxYSteps; yStep++) {
      for (let xStep = 0; xStep <= maxXSteps; xStep++) {
        const test: Record<string, BlockPosition> = {};
        activeItems.value.forEach((uname) => {
          const lastPos = lastPosSnapshot[uname];
          if (lastPos && uname !== ITEMS_GROUP_UNAME && uname !== CANVAS_UNAME) {
            test[uname] = {
              layer: lastPos.layer,
              position: {
                x: snapValueIfNeeded(lastPos.position.x + Math.sign(forceVector.dx) * xStep * backtrackUnit),
                y: snapValueIfNeeded(lastPos.position.y + Math.sign(forceVector.dy) * yStep * backtrackUnit),
                w: lastPos.position.w,
                h: lastPos.position.h,
              },
            };
          }
        });
        if (!areGhostItemsOverlappedWithOtherItems(test)) {
          backtrack = test;
        } else {
          break;
        }
      }
    }
    // Pick the closest to intended destination
    const slideYDist = getDist(backtrack);
    if (slideYDist < minDist) {
      minDist = slideYDist;
      bestPosition = backtrack;
    }

    ghostItemsPosition.value = bestPosition;
  }

  function calculateGhostItemsPosition () {
    const newGhostItemsPosition: Record<string, BlockPosition> = {};

    activeItems.value.forEach((uname) => {
      const currPos = uname === CANVAS_UNAME ? canvasPosition : itemsPositions.value[uname];
      if (currPos) {
        newGhostItemsPosition[uname] = {
          layer: currPos.layer,
          position: {
            x: snapValueIfNeeded(currPos.position.x),
            y: snapValueIfNeeded(currPos.position.y),
            w: snapValueIfNeeded(currPos.position.w),
            h: snapValueIfNeeded(currPos.position.h),
          },
        };
      }
    });

    // no smart mode, or updating canvas size => just update the ghost items position
    if (!smartMode.value || activeItems.value.includes(CANVAS_UNAME)) {
      ghostItemsPosition.value = newGhostItemsPosition;
      return;
    }

    // smart mode => check if the ghost items are overlapped with other items
    if (!areGhostItemsOverlappedWithOtherItems(newGhostItemsPosition)) {
      ghostItemsPosition.value = newGhostItemsPosition;
    } else if (moving.value) {
      // in case of moving items, we need to calculate the best position for the ghost items
      // in the case of resizing, we don't need to do this, just use the last suitable ghost position
      calculateMovingGhostItemsPosition();
    }
  }

  function handleMouseMove (e: MouseEvent) {
    if (working.value) {
      e.preventDefault();
      calculateDeltaXY(e);
      hideAllPopovers();
      // TODO: control state properly
      eventBus.$emit('canvasMouseMoveAndWorking');

      autoScroll(e);

      if (moving.value) {
        handleMove();
      } else if (resizing.value) {
        handleResize();
      }

      calculateGhostItemsPosition();
    } else if (selectRect.end) {
      const scrollingRect = scrollBody.value?.getBoundingClientRect();
      selectRect.end.top = Math.min(e.clientY - (scrollingRect?.top ?? 0) + getBodyScrollY(), scrollBody.value?.scrollHeight ?? 0);
      selectRect.end.left = Math.min(e.clientX - (scrollingRect?.left ?? 0) + getBodyScrollX(), scrollBody.value?.scrollWidth ?? 0);

      updateActiveItemsBySelectRect(e.shiftKey);
    }
  }

  function handleMouseUp (e: MouseEvent) {
    if (working.value) {
      e.preventDefault();
      // TODO: control state properly
      eventBus.$emit('canvasMouseUpAndWorking');
      moving.value = false;
      resizing.value = false;
      resizeDirections.top = false;
      resizeDirections.left = false;
      resizeDirections.bottom = false;
      resizeDirections.right = false;

      // only trigger `onDragEnd` if the sizes/positions really changed
      if (activeItems.value.includes(CANVAS_UNAME)) {
        // updated the canvas
        activeItems.value = [];

        const canvasGhostPos = ghostItemsPosition.value[CANVAS_UNAME]?.position;
        if (canvasGhostPos) {
          // set back canvasPosition to the ghost position
          canvasPosition.position.w = canvasGhostPos.w;
          canvasPosition.position.h = canvasGhostPos.h;
        }
        const newCanvasSize = { w: canvasPosition.position.w, h: canvasPosition.position.h };
        if (newCanvasSize.w !== width.value || newCanvasSize.h !== height.value) {
          onDragEnd({ type: 'canvas', size: newCanvasSize });
        }
      } else {
        // updated the items
        const updatedPositions = activeItems.value.reduce<Record<string, BlockPosition>>((acc, uname) => {
          if (uname === ITEMS_GROUP_UNAME) {
            // skip
            return acc;
          }
          const pos = itemsPositions.value[uname];
          const ghostPos = ghostItemsPosition.value[uname];
          if (pos && ghostPos) {
            // set back the position to the ghost position
            pos.position.x = ghostPos.position.x;
            pos.position.y = ghostPos.position.y;
            pos.position.w = ghostPos.position.w;
            pos.position.h = ghostPos.position.h;
          }
          if (!isEqual(pos, items.value[uname])) {
            acc[uname] = cloneDeep(pos);
          }
          return acc;
        }, {});

        if (Object.keys(updatedPositions).length > 0) {
          onDragEnd({ type: 'items', position: updatedPositions });

          if (smartMode.value) {
            // update canvas size if needed
            const maxX = Math.max(...Object.values(updatedPositions).map(({ position }) => position.x + position.w));
            const maxY = Math.max(...Object.values(updatedPositions).map(({ position }) => position.y + position.h));
            if (maxX + gridSize.value > canvasPosition.position.w || maxY + gridSize.value > canvasPosition.position.h) {
              const newCanvasSize = {
                w: Math.max(canvasPosition.position.w, maxX) + gridSize.value,
                h: Math.max(canvasPosition.position.h, maxY) + gridSize.value,
              };
              if (newCanvasSize.w !== width.value || newCanvasSize.h !== height.value) {
                onDragEnd({ type: 'canvas', size: newCanvasSize });
              }
            }
          }
        }
      }
    }

    ghostItemsPosition.value = {};

    if (selectRect.start && selectRect.end) {
      // drawing select rect => clear it
      clearSelectRect();
      resetLastData();
    }
  }

  function selectItems (unames: string[]) {
    if (!isEqual(unames, activeItems.value)) {
      activeItems.value = unames;
      resetLastData();
    }
  }

  function handleKeyDown (e: KeyboardEvent) {
    // for keyboard events, we should ignore when focusing on editable elements (input, textarea, etc...)
    if (shouldIgnoreInputEvent(e) || isEditableElement(document.activeElement)) {
      return;
    }
    // handle blocks select
    if (e.key === 'a' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      // select all blocks
      selectItems(Object.keys(itemsPositions.value));
    } else if (activeItems.value.length > 0) {
      // handle blocks movements
      const deltaUnit = gridSize.value || 5;
      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          deltaY.value -= deltaUnit;
          handleMove();
          debouncedOnEnd({ type: 'items', position: cloneDeep(itemsPositions.value) });
          break;
        case 'ArrowDown':
          e.preventDefault();
          deltaY.value += deltaUnit;
          handleMove();
          debouncedOnEnd({ type: 'items', position: cloneDeep(itemsPositions.value) });
          break;
        case 'ArrowLeft':
          e.preventDefault();
          deltaX.value -= deltaUnit;
          handleMove();
          debouncedOnEnd({ type: 'items', position: cloneDeep(itemsPositions.value) });
          break;
        case 'ArrowRight':
          e.preventDefault();
          deltaX.value += deltaUnit;
          handleMove();
          debouncedOnEnd({ type: 'items', position: cloneDeep(itemsPositions.value) });
          break;
        case 'Delete':
        case 'Backspace':
          e.preventDefault();
          onDelete(activeItems.value);
          break;
        case 'Escape':
          e.preventDefault();
          // deselecte all items
          activeItems.value = [];
          break;
        default:
      }
    }
  }

  function handleItemsChange (newItems: Record<string, BlockPosition>, oldItems: Record<string, BlockPosition>) {
    if (!isEqual(newItems, oldItems)) {
      itemsPositions.value = cloneDeep(newItems);
    }
  }
  function handleWidthChange (val: number) {
    canvasPosition.position.w = val;
  }
  function handleHeightChange (val: number) {
    canvasPosition.position.h = val;
  }
  function handleActiveItemsChange (unames: string[], oldUnames: string[]) {
    if (!isEqual(unames, oldUnames)) {
      onSelect(unames.filter(u => u !== CANVAS_UNAME));
    }
  }
  function handleZoomLevelChange () {
    // clear select rect
    clearSelectRect();
  }

  const cancelWatcherFns = ref<Fn[]>([]);
  function cancelWatchers () {
    cancelWatcherFns.value.forEach(cancelFn => {
      cancelFn();
    });

    cancelWatcherFns.value = [];
  }
  function registerWatchers () {
    if (cancelWatcherFns.value.length > 0) {
      cancelWatchers();
    }

    cancelWatcherFns.value.push(useEventListener('mousedown', handleMouseDown));
    cancelWatcherFns.value.push(useEventListener('mousemove', handleMouseMove));
    cancelWatcherFns.value.push(useEventListener('mouseup', handleMouseUp));
    cancelWatcherFns.value.push(useEventListener('keydown', handleKeyDown));
    cancelWatcherFns.value.push(watch(() => items.value, handleItemsChange));
    cancelWatcherFns.value.push(watch(() => width.value, handleWidthChange));
    cancelWatcherFns.value.push(watch(() => height.value, handleHeightChange));
    cancelWatcherFns.value.push(watch(() => activeItems.value, handleActiveItemsChange));
    cancelWatcherFns.value.push(watch(() => zoomLevel.value, handleZoomLevelChange));
  }

  watch(editMode, (value) => {
    if (value) {
      registerWatchers();
    } else {
      cancelWatchers();
    }
  }, {
    immediate: true,
  });

  function resizeToFit (side: 'vertical' | 'horizontal') {
    if (side === 'horizontal') {
      const maxX = Math.max(...Object.values(itemsPositions.value).map(({ position }) => position.x + position.w));
      onDragEnd({
        type: 'canvas',
        size: {
          w: maxX + gridSize.value,
          h: canvasPosition.position.h,
        },
      });
    } else {
      const maxY = Math.max(...Object.values(itemsPositions.value).map(({ position }) => position.y + position.h));
      onDragEnd({
        type: 'canvas',
        size: {
          w: canvasPosition.position.w,
          h: maxY + gridSize.value,
        },
      });
    }
  }

  return {
    canvasWidth: computed(() => canvasPosition.position.w),
    canvasHeight: computed(() => canvasPosition.position.h),
    itemsPositions,
    ghostItemsPosition,
    activeItems,
    overlappedItems,
    alignedItems,
    alignmentLines,
    selectItems,
    working,
    mouseMoving,
    onItemVisibilityChange,
    selectRect,
    selectedItemsGroupPosition,
    resizeToFit,
  };
}
