operationId: Ai_Feedback
summary: Feedback AI message
tags:
  - AI
requestBody:
  content:
    application/json:
      schema:
        type: object
        properties:
          conversation_id:
            type: string
          message:
            type: string
          feedback:
            type: string
            enum:
              - like
              - dislike
        required:
          - conversation_id
          - message
          - feedback
responses:
  "200":
    description: Feedback status
    content:
      application/json:
        schema:
          type: object
          properties:
            status:
              type: string