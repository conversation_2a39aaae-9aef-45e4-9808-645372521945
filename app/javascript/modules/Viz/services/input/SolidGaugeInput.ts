import { formatValue } from '@/modules/Formatting/utils';
import {
  escape, get, merge, debounce,
} from 'lodash';
import { extractDataPointStruct } from '@/modules/Drills/services/extractDataPointStruct';
import VizInput from './VizInput';
import { dataLabelStyle } from './commons';

/**
 * Solid gauge chart input
 * @link https://www.highcharts.com/demo/gauge-solid
 */

function applyDrillthroughHandler ({ options, pivoted, handler }) {
  let hoverField: 'series' | 'yAxis' | null = null;

  const handleMouseOver = debounce((e) => {
    if (hoverField === 'series') {
      const values = {
        rows: [],
        columns: [],
        dataPoint: extractDataPointStruct({
          measure: pivoted.exploreOpts.measures[0],
          offset: { x: 0, y: 0 },
          color: e.target.color,
          value: pivoted.pivotedData?.[0][0],
        }),
      };
      handler(values);
    } else if (hoverField === 'yAxis') {
      const measure = pivoted.exploreOpts.measures[1];
      const values = {
        rows: [],
        columns: [],
        dataPoint: extractDataPointStruct({
          measure,
          color: options.chart?.pane?.background?.backGroundColor,
          value: pivoted.pivotedData?.[0][1],
          offset: { x: 1, y: 0 },
        }),
      };
      handler(values);
    }
  }, 100);
  options.chart.events.load = function () {
    const yAxis = this.yAxis[0].axisGroup;
    yAxis.on('mouseover', (e) => {
      hoverField = 'yAxis';
      handleMouseOver(e);
    }).on('mouseout', () => {
      hoverField = null;
      handler({});
    });
  };
  options.plotOptions.series.point.events.mouseOver = (e) => {
    hoverField = 'series';
    handleMouseOver(e);
  };
  options.plotOptions.series.point.events.mouseOut = () => {
    hoverField = null;
    handler({});
  };
}

export default class SolidGaugeInput extends VizInput {
  async generate (generatorOptions = {}) {
    const { vizSetting } = this;
    const {
      data: { formats },
      data: pivoted, chartTitle, query, meta, executedAql,
    } = await this.pivot(generatorOptions);

    const options = this.generateOptions(pivoted, formats, vizSetting, meta);
    if (this.drillthroughHandler) {
      applyDrillthroughHandler({ options, pivoted, handler: this.drillthroughHandler });
    }

    return {
      options,
      message: null,
      pivotRenderer: pivoted,
      chartTitle,
      query,
      executedAql,
    };
  }

  generateOptions (pivoted, formats, vizSetting, meta) {
    const title = get(vizSetting, 'settings.solid_gauge.title');
    const suffix = get(vizSetting, 'settings.solid_gauge.suffix');

    const currentValue = get(pivoted.pivotedData, [0, 0]);
    const currentValueFormat = formats.measures[0];

    const compareValue = get(pivoted.pivotedData, [0, 1]);
    const compareValueFormat = formats.measures[1];

    const options = {
      yAxis: {
        min: 0,
        max: compareValue,
        title: {
          text: title,
        },
        tickAmount: 2,
        tickPositions: [0, compareValue],
        tickWidth: 0,
        labels: {
          formatter (data) {
            return formatValue(data.value, compareValueFormat);
          },
        },
        className: this.crossFilterHandler ? 'gauge-chart-hover-yaxis' : '',
      },
      series: [{
        data: [currentValue],
        dataLabels: {
          y: -30,
          formatter () {
            return `${'<div style="text-align:center; line-height: 1.5; white-space: nowrap">'
                    + '<span style="font-size: 1.5rem; color: #666">'}${
              formatValue(currentValue, currentValueFormat)
            }</span><br/>`
                    + `<span style="font-size: 1rem; color:silver">${suffix ? escape(suffix) : ' '}</span>`
                  + '</div>';
          },
        },
      }],
      meta: {
        lastCacheUpdated: meta.last_cache_updated,
      },
    };
    return merge(this.defaultOptions, options);
  }

  get defaultOptions () {
    return {
      chart: {
        type: 'solidgauge',
        spacingBottom: 0,
        events: {},
      },
      pane: {
        center: ['50%', '75%'],
        startAngle: -90,
        endAngle: 90,
        size: '100%',
        background: {
          innerRadius: '60%',
          outerRadius: '100%',
          shape: 'arc',
        },
      },
      tooltip: {
        hideDelay: 100,
        enabled: false,
        ...this.tooltipOptions(),
      },
      yAxis: {
        stops: [
          [0, '#EA7A81'], // red
          [0.2, '#FD9D76'], // orange
          [0.4, '#FDE9A3'], // yellow
          [0.6, '#8DD6C3'], // green
        ],
        lineWidth: 0,
        minorTickInterval: null,
        title: {
          y: -15,
          align: 'high',
          textAlign: 'center',
          style: {
            color: '#777',
            fontWeight: 600,
            fontSize: '1.25rem',
          },
        },
        labels: {
          y: 15,
        },
      },
      plotOptions: {
        solidgauge: {
          cursor: this.crossFilterHandler ? 'pointer' : 'auto',
          dataLabels: {
            y: 5,
            borderWidth: 0,
            useHTML: true,
            style: dataLabelStyle,
          },
        },
        series: {
          point: {
            events: {},
          },
          states: {},
        },
      },
    };
  }
}
