<template>
  <div class="flex flex-col">
    <div
      v-if="isEmbedPortalEnabled"
      class="panel panel-default"
    >
      <div
        class="panel-heading rounded-t"
        data-ci="embed-portal-list"
      >
        <div class="text-xs font-semibold">
          Embed Portals
          <FeatureTag inline />
        </div>
        <div class="mt-0.5 text-2xs">
          Embed Portal allows developers to embed and manage dashboards, datasets within their application
        </div>
      </div>
      <div class="panel-body rounded-b pt-0">
        <EmbedPortalTable />
      </div>
    </div>
    <div
      class="panel panel-default"
      data-ci="embed-dashboard-list"
    >
      <div class="panel-heading rounded-t">
        <div class="text-xs font-semibold">
          Embed Dashboards
        </div>
        <span class="mt-0.5 text-2xs">
          List of dashboards that contain embed links
        </span>
      </div>
      <div class="panel-body rounded-b pt-0">
        <EmbedLinkTable source-type="Dashboard" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import EmbedPortalTable from '@/modules/EmbedLinks/components/Management/EmbedObjects/EmbedPortalTable.vue';
import EmbedLinkTable from '@/modules/EmbedLinks/components/Management/EmbedObjects/EmbedLinkTable.vue';
import * as FeatureToggle from '@/core/services/featureToggle';
import FeatureTag from '@/core/components/ui/FeatureTag.vue';

const isEmbedPortalEnabled = FeatureToggle.check('embed_portal:enabled');
</script>
