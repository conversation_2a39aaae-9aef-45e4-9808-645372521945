# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe Api::V2::AiController, :api do
  let(:admin) { get_test_admin }

  before do
    request.headers['Accept'] = 'application/json'
    request.headers['Content-Type'] = 'application/json'
    set_token_header(admin)
  end

  describe '#chat' do
    before do
      FeatureToggle.toggle_global(Ai::Constants::FT_SCOUT, true)
      FeatureToggle.toggle_global(Ai::Constants::FT_PHOEBE, true)
      FeatureToggle.toggle_global(Ai::Constants::FT_DOCSEARCH, true)
    end

    it 'returns 400 if agent does not exist', skip_schema_conform: true do
        post :chat, params: { agent: { _name: 'mock_agent' }, query: 'hi' }
        assert_response_status!(400)
        json_response = Oj.load(response.body, mode: :json)
        expect(json_response['type']).to eq('InvalidParameterError')
        expect(json_response['message']).to eq('Unknown agent')
    end

    it 'returns 422 if agent ft is disabled' do
      FeatureToggle.toggle_global(Ai::Constants::FT_PHOEBE, false)
      post :chat, params: { agent: { _name: 'phoebe' }, query: 'hi' }
      assert_response_status!(422)
      json_response = Oj.load(response.body, mode: :json)
      expect(json_response['type']).to eq('BaseError')
      expect(json_response['message']).to eq('Holistics::FeatureDisabled')
    end

    it 'returns 422 if admin disable agent' do
      FeatureToggle.toggle_global(Ai::Constants::FT_PHOEBE, true)
      admin.tenant.set_setting('ai', {agents: { phoebe: { enabled: false }}})
      post :chat, params: { agent: { _name: 'phoebe' }, query: 'hi' }
      assert_response_status!(422)
      json_response = Oj.load(response.body, mode: :json)
      expect(json_response['type']).to eq('BaseError')
      expect(json_response['message']).to eq('phoebe is currently disabled. Please contact your admin.')
    end

    describe 'when skip job' do
      it 'returns answer' do
        VCR.use_cassette('ai/chat/skip_job') do
          params = {
            agent: {
              _name: 'scout',
              text_before_cursor: 'hi',
              text_after_cursor: 'there',
              data_source_names: ['mock_data_source'],
              model_names: ['mock_model'],
            },
            skip_job: true,
            query: 'hi',
          }
          post :chat, params: params
          assert_response_status!(200)
          json_response = Oj.load(response.body, mode: :json)
          expect(json_response['answer']).to eq('hi there')
        end
      end

      it 'returns 422 if agent is not job skippable' do
        params = {
          agent: {
            _name: 'docsearch',
          },
          skip_job: true,
          query: 'hi',
        }
        post :chat, params: params
        assert_response_status!(422)
        json_response = Oj.load(response.body, mode: :json)
        expect(json_response['type']).to eq('BaseError')
        expect(json_response['message']).to eq('docsearch is not job skippable')
      end
    end

    describe 'when not skip job' do
      it 'returns async job' do
        VCR.use_cassette('ai/chat/not_skip_job') do
          params = {
            agent: {
              _name: 'docsearch',
            },
            query: 'hi',
          }
          post :chat, params: params
          job = assert_async_response!(['job', 'id'])
          expect(job.status).to eq('success')
          answer = Ai::Store.new.fetch_answer(job)
          expect(answer).to be_present
        end
      end
    end
  end

  describe '#answer' do
    it 'returns the answer' do
      job = create(:job, user: admin)
      Ai::Store.new.save_answer_chunk('mock_answer', job: job)
      get :answer, params: { job_id: job.id }
      assert_response_status!(200)
      expect(response.body).to eq(Oj.dump({answer: 'mock_answer'}, mode: :json))
    end

    it 'returns 404 if job is not found' do
      get :answer, params: { job_id: 123 }
      assert_response_status!(404)
      json_response = Oj.load(response.body, mode: :json)
      expect(json_response['type']).to eq('BaseError')
      expect(json_response['message']).to match(/Couldn't find Job/)
    end

    it 'returns 403 if user is not authorized to read the job' do
      job = create(:job, user: create(:user), tenant_id: create(:tenant).id)
      get :answer, params: { job_id: job.id }
      assert_response_status!(403)
    end

    it 'returns empty answer if redis is empty' do
      job = create(:job, user: admin)
      get :answer, params: { job_id: job.id }
      assert_response_status!(200)
      json_response = Oj.load(response.body, mode: :json)
      expect(json_response['answer']).to eq('')
    end
  end

  describe '#feedback' do
    it 'always returns ok and stores the feedback in HOtel', otel: true, type: :request do
      params = {
        conversation_id: 'mock_conversation_id',
        message: 'mock_message',
        feedback: 'like',
      }
      headers = {
        'Content-Type' => 'application/json',
        HolisticsSetting::API_KEY_HEADER => admin.generate_authentication_token,
      }
      post '/api/v2/ai/feedback', params: Oj.dump(params, mode: :json), headers: headers
      assert_response_status!(200)
      span = otel_finished_spans.find { |s| s.name == 'Api::V2::AiController#feedback' }
      span_parmas = Oj.load(span.attributes['h.params'], mode: :json)
      expect(span_parmas['conversation_id']).to eq(params[:conversation_id])
      expect(span_parmas['message']).to eq(params[:message])
      expect(span_parmas['feedback']).to eq(params[:feedback])
    end

    it 'raise error when params are invalid', skip_schema_conform: true do
      params_list = [
        # wrong feedback type
        {
          conversation_id: 'mock_conversation_id',
          message: 'mock_message',
          feedback: 'hello_world',
        },
        # missing conversation_id
        {
          message: 'mock_message',
          feedback: 'like',
        },
        # missing message
        {
          conversation_id: 'mock_conversation_id',
          feedback: 'like',
        },
        # missing feedback
        {
          conversation_id: 'mock_conversation_id',
          message: 'mock_message',
        },
      ]
      params_list.each do |params|
        expect do
          post :feedback, params: params
          assert_schema_conform
        end.to raise_error Committee::InvalidRequest
      end
    end
  end
end

