# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe Api::V2::AmlStudio::RepositoriesController, :api do
  let(:user) { get_test_admin }

  before do
    request.headers['Accept'] = 'application/json'
    request.headers['Content-Type'] = 'application/json'
    FeatureToggle.toggle_global(AmlStudio::Project::FT_AUTO_IMPORT_RELATIONSHIP, true)
  end

  context 'with valid api token' do
    before do
      set_request_aml_env(:aml_studio)
      user.update(allow_authentication_token: true)
      set_token_header(user)
    end

    def response_result
      JSON.parse(response.body)
    end

    describe 'POST #related_relationships' do
      include_context 'aml_models_with_database_constraints'

      let(:data_source) { ds }
      let(:work_flow) { proj_work_flow }
      let(:file_paths) { ["/#{aml_source_model_name}.model.aml", "/#{aml_dest_model_name}.model.aml"] }

      # simulate FE compiling
      let(:model_bindings) do
        bindings = work_flow.compile_to_jsons(file_paths)

        bindings.map do |_file_path, blob|
          blob['bindings']
        end.flatten.flatten
      end

      it 'works' do
        params = {
          project_id: project.id,
          model_bindings: model_bindings,
        }

        post :related_relationships, params: params

        expect(response).to have_http_status(:ok)
        expect(response_result['relationships'].to_json).to eq([
          {
            type: 'many_to_one',
            from: {
              model: aml_source_model_name,
              field: source_col_fk,
            },
            to: {
              model: aml_dest_model_name,
              field: dest_col_fk,
            },
          },
        ].to_json)
      end

      it 'return empty when find no models' do
        params = {
          project_id: project.id,
          model_bindings: [],
        }

        post :related_relationships, params: params

        expect(response).to have_http_status(:ok)
        expect(response_result['relationships']).to eq([])
      end

      context 'models belong to different data sources' do
        let(:ds_2) { get_data_source('pg_2', tenant_name = 'test') }
        let(:new_table) { FQName.new schema_name: 'public', table_name: 'new_table' }
        let(:new_model) do
          DataModel.create_from_ds(ds_2, fqnames: [new_table.to_s])[:explore_models].first
        end
        let(:file_paths) do
          ["/#{aml_source_model_name}.model.aml", "/#{new_table.schema_name}_#{new_table.table_name}.model.aml"]
        end

        # simulate FE compiling
        let(:model_bindings) do
          bindings = work_flow.compile_to_jsons(file_paths)

          bindings.map do |_file_path, blob|
            blob['bindings']
          end.flatten.flatten
        end

        before do
          create_test_data_modeling_table(new_table)
          DataSourceVersions::SchemaSynchronizationService.new(ds_2).execute
          create_test_aml_table_models(admin, ds_2, [new_table.to_s], proj_repo, '')
        end

        it 'fails' do
          params = {
            project_id: project.id,
            model_bindings: model_bindings,
          }

          post :related_relationships, params: params

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response_result['message']).to match(/Models belong to different data sources/)
        end
      end

      it 'return empty when find no constraints' do
        DataSourceConstraint.where(data_source_id: data_source.id).destroy_all

        params = {
          project_id: project.id,
          model_bindings: model_bindings,
        }

        post :related_relationships, params: params

        expect(response).to have_http_status(:ok)
        expect(response_result['relationships']).to eq([])
      end

      context 'cannot find project' do
        it 'raise 422' do
          params = {
            project_id: 0,
            model_bindings: model_bindings,
          }

          post :related_relationships, params: params
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context 'user has no permission' do
        let(:tenant_2) { create(:tenant) }
        let(:user) { create(:analyst, tenant: tenant_2) }

        it 'raise 403' do
          params = {
            project_id: project.id,
            model_bindings: model_bindings,
          }

          post :related_relationships, params: params
          expect(response).to have_http_status(:forbidden)
        end
      end
    end

    describe 'POST #submit_init_binding_cache' do
      include_context 'aml_studio_basic'

      it 'submits init binding cache request' do
        params = {
          project_id: project.id,
        }
        post :submit_init_binding_cache, params: params
        job = assert_success_async_response!(['job', 'id'])
        assert_success_job!(job)
      end
    end

    describe 'POST #submit_deploy' do
      include_context 'aml_studio_dataset'
      include_context 'aml_studio_dev_mode'
      it 'generates job for deploy' do
        post :submit_deploy, params: { project_id: project.id }
        assert_success_async_response!(['job', 'id'])
        job = assert_success_async_response!(['job', 'id'])
        result = job.fetch_cache_data
        expect(result['data']).to have_key 'preparing_production_job'
        # should cache datasets when deploy successfully
        expect(Cache.keys("aml_objects:1:dataset:*:#{proj_repo.reload.version}").length).to eq(12)
      end

      it 'raise error if repo did not initialized' do
        allow_any_instance_of(AmlStudio::Project).to receive(:project_repository).and_return(nil)
        post :submit_deploy, params: { project_id: project.id }
        expect(response).to have_http_status :unprocessable_entity
      end

      it 'requests dataset mapping' do
        orphan_dataset = data_set.dup
        orphan_dataset.uname = 'orphan'
        orphan_dataset.save!
        post :submit_deploy, params: { project_id: project.id }
        job = assert_success_async_response!(['job', 'id'])
        result = job.fetch_cache_data
        expect(result['error_type']).to eq 'dataset_mapping'
        expect(result['error_details']['message']).to eq 'Failed to publish: Need to map production dataset to publishing dataset'
        expect(result['error_details']['deploying_datasets']).to contain_exactly('a.module_dataset',
                                                                                 'a.module_dataset2', 'a.module_dataset_3', 'dynamic_modeling_dataset', 'ecommerce', 'ecommerce_2', 'test', 'test2', 'test3', 'ecommerce_aql', 'extend_base_dataset', 'extend_sub_dataset',)
      end

      it 'deploys with dataset mapping' do
        proj_work_flow.remove('datasets/test.dataset.aml')
        content = <<-HEREDOC
        Dataset new_test {
          label: 'Test data set'
          description: 'ahihi'
          data_source_name: 'pg'
          models: []
          relationships: []
          owner: '<EMAIL>'
        }
        HEREDOC
        proj_work_flow.write_file('datasets/new_test.dataset.aml', content)
        params = {
          project_id: project.id,
          mapping: {
            data_set.id.to_s => 'new_test',
          },
        }
        post :submit_deploy, params: params
        job = assert_success_async_response!(['job', 'id'])
      end

      it 'deploys with dataset mapping description' do
        proj_work_flow.remove('datasets/test2.dataset.aml')
        # Create new dataset
        content = <<-HEREDOC
        Dataset new_test_description {
          label: 'Test data set'
          description: 'This is description'
          data_source_name: 'pg'
          models: []
          relationships: []
          owner: '<EMAIL>'
        }
        HEREDOC
        # Update exist dataset
        content1 = <<-HEREDOC
        Dataset test2 {
          label: 'Test data set 2'
          description: 'This is description lala'
          data_source_name: 'pg'
          models: [
            data_modeling_orders,
            data_modeling_products,
          ]
          relationships: [
            RelationshipConfig {
              rel: Relationship {
                type: 'many_to_one'
                from: FieldRef {
                  model: 'data_modeling_orders'
                  field: 'product_id'
                }
                to: FieldRef {
                  model: 'data_modeling_products'
                  field: 'id'
                }
              }
              active: false
              direction: 'two_way'
            }
          ]
          owner: '<EMAIL>'
        }
        HEREDOC

        proj_work_flow.write_file('datasets/new_test.dataset.aml', content)
        proj_work_flow.write_file('datasets/test2.dataset.aml', content1)
        params = {
          project_id: project.id,
        }
        post :submit_deploy, params: params
        job = assert_success_async_response!(['job', 'id'])
        data_set = DataSet.find_by(uname: 'new_test_description')
        data_set1 = DataSet.find_by(uname: 'test2')
        expect(data_set.description).to eq('This is description')
        expect(data_set1.description).to eq('This is description lala')
      end

      it 'shows error if there is error in aml code' do
        content = <<-HEREDOC
        Dataset new_test {
          label: 'Test data set'
          description: 'ahihi'
          data_source_name: 'pg'
          models: [
          relationships: [
          owner: '<EMAIL>'
        }
        HEREDOC
        proj_work_flow.write_file('datasets/new_test.dataset.aml', content)
        post :submit_deploy, params: { project_id: project.id }
        job = assert_success_async_response!(['job', 'id'])
        expect(job.fetch_cache_data['error_type']).to eq 'syntax_error'
        expect(job.fetch_cache_data['error_details']['diagnostics'].length).to be >= 1
      end
    end

    describe 'POST #submit_validate_field' do
      include_context 'aml_studio_dataset'
      include_context 'aml_studio_dev_mode'
      before { set_request_aml_env(:aml_studio) }

      shared_examples 'utilize FE processing with custom dimension' do
        before do
          FeatureToggle.toggle_global(AmlStudio::Project::FT_UTILIZE_FE_PROCESSING, true)
        end

        after do
          FeatureToggle.toggle_global(AmlStudio::Project::FT_UTILIZE_FE_PROCESSING, false)
        end

        it 'works when add new dimension without adding binding_json field' do
          post :submit_validate_field, params: custom_dimension_params, as: :json
          job = assert_success_async_response!(['job', 'id'])
          expect(job.status).to eq('success')
        end

        it 'works when add new dimension with binding_json field' do
          expect_any_instance_of(Aml::Client).not_to receive(:get_bindings_by_fqns_from_wd)
          expect_any_instance_of(Aml::Client).not_to receive(:get_bindings_by_fqns)
          post :submit_validate_field, params: custom_dimension_params_with_binding_json, as: :json
          job = assert_success_async_response!(['job', 'id'])
          expect(job.status).to eq('success')
        end

        it 'works when update dimension with binding_json field' do
          expect_any_instance_of(Aml::Client).not_to receive(:get_bindings_by_fqns_from_wd)
          expect_any_instance_of(Aml::Client).not_to receive(:get_bindings_by_fqns)
          post :submit_validate_field, params: update_dimension_params_with_binding_json, as: :json
          job = assert_success_async_response!(['job', 'id'])
          expect(job.status).to eq('success')
        end
      end

      shared_examples 'utilize FE processing with custom measure' do
        before do
          FeatureToggle.toggle_global(AmlStudio::Project::FT_UTILIZE_FE_PROCESSING, true)
        end

        after do
          FeatureToggle.toggle_global(AmlStudio::Project::FT_UTILIZE_FE_PROCESSING, false)
        end

        it 'works when add new measure with binding_json field' do
          post :submit_validate_field, params: custom_measure_params_with_binding_json, as: :json
          job = assert_success_async_response!(['job', 'id'])
          expect(job.status).to eq('success')
        end

        it 'works when update measure with binding_json field' do
          post :submit_validate_field, params: update_measure_params_with_binding_json, as: :json
          job = assert_success_async_response!(['job', 'id'])
          expect(job.status).to eq('success')
        end
      end

      shared_examples 'works with custom dimension' do
        it 'works when add new dimension' do
          post :submit_validate_field, params: custom_dimension_params, as: :json
          job = assert_success_async_response!(['job', 'id'])
          expect(job.status).to eq('success')
        end

        it 'works when update dimension' do
          post :submit_validate_field, params: update_dimension_params, as: :json
          job = assert_success_async_response!(['job', 'id'])
          expect(job.status).to eq('success')
        end
      end

      shared_examples 'works with custom measure' do
        it 'works when add new measure' do
          post :submit_validate_field, params: custom_measure_params, as: :json
          job = assert_success_async_response!(['job', 'id'])
          expect(job.status).to eq('success')
        end

        it 'works when update measure' do
          post :submit_validate_field, params: update_measure_params, as: :json
          job = assert_success_async_response!(['job', 'id'])
          expect(job.status).to eq('success')
        end
      end

      shared_examples 'should validate correct type or syntax' do
        it 'shows error if there is aggregate function in custom dimesion' do
          new_params = custom_dimension_params.deep_dup.deep_merge({ field: { sql: 'COUNT({{ name }})' } })
          post :submit_validate_field, params: new_params, as: :json
          job = assert_failure_async_response!(['job', 'id'])
          expect(job.error_msg).to eq 'A Dimension cannot have aggregate function'
        end

        it 'shows error if there is no aggregate function in custom measure' do
          new_params = custom_measure_params.deep_dup.deep_merge({ field: { sql: '{{ name }}',
                                                                            aggregation_type: 'custom', } })
          post :submit_validate_field, params: new_params, as: :json
          job = assert_failure_async_response!(['job', 'id'])
          expect(job.error_msg).to eq 'A Measure must have aggregate function'
        end

        it 'shows error if there is a cyclic reference' do
          existed_field_name = 'reference_dimension'
          new_params = update_dimension_params.deep_dup.deep_merge({ field: { sql: "{{ #{existed_field_name} }}" } })
          post :submit_validate_field, params: new_params, as: :json
          job = assert_failure_async_response!(['job', 'id'])
          expect(job.error_msg).to include 'Internal execution error'
        end

        it 'shows error if sql is invalid' do
          new_params = custom_dimension_params.deep_dup.deep_merge({ field: { sql: 'abc' } })
          post :submit_validate_field, params: new_params, as: :json
          job = assert_failure_async_response!(['job', 'id'])
          expect(job.error_msg).to include 'column "abc" does not exist'
        end

        it 'shows error if incorrect data model name or path' do
          new_params = custom_dimension_params.deep_dup.deep_merge({ model: { name: 'abc' } })
          post :submit_validate_field, params: new_params, as: :json
          job = assert_failure_async_response!(['job', 'id'])
          expect(job.error_msg).to eq 'Cannot find model with name abc'

          new_params = custom_dimension_params.deep_dup.deep_merge({ model: { path: 'abc' } })
          post :submit_validate_field, params: new_params, as: :json
          job = assert_failure_async_response!(['job', 'id'])
          expect(job.error_msg).to eq 'Cannot find model with name tenant'
        end
      end

      context 'with table model' do
        let(:custom_dimension_params) do
          {
            project_id: project.id,
            model: {
              path: 'models/holistics_model/tenant.model.aml',
              name: 'tenant',
            },
            field: {
              description: 'custom dimension',
              label: 'custom dimesion',
              name: 'custom_dimension',
              sql: '{{ name }}',
              syntax: 'sql',
              type: 'text',
              aggregation_type: nil,
            },
          }
        end

        let(:binding_json) do
          {
            name: 'tenant',
            _type: 'Model',
            value: {
              label: 'Tenants',
              description: '',
              owner: '<EMAIL>',
              data_source_name: 'pg',
              dimension: {
                id: {
                  name: 'id',
                  hidden: false,
                  definition: {
                    __type__: 'Heredoc',
                    name: 'sql',
                    content: '{{ #SOURCE.id }}',
                  },
                  label: 'Id',
                  type: 'number',
                  __childIdx__: 4,
                  __doc__: '',
                },
                name: {
                  name: 'name',
                  hidden: false,
                  definition: {
                    __type__: 'Heredoc',
                    name: 'sql',
                    content: '{{ #SOURCE.name }}',
                  },
                  label: 'Name',
                  type: 'text',
                  __childIdx__: 5,
                  __doc__: '',
                },
                created_at: {
                  name: 'created_at',
                  hidden: false,
                  definition: {
                    __type__: 'Heredoc',
                    name: 'sql',
                    content: '{{ #SOURCE.created_at }}',
                  },
                  label: 'Created At',
                  type: 'datetime',
                  __childIdx__: 6,
                  __doc__: '',
                },
                reference_dimension: {
                  name: 'reference_dimension',
                  hidden: false,
                  definition: {
                    __type__: 'Heredoc',
                    name: 'sql',
                    content: '{{ name }}',
                  },
                  label: 'Reference_dimension',
                  type: 'text',
                  __childIdx__: 7,
                  __doc__: '',
                },
              },
              measure: {
                custom_measure: {
                  name: 'custom_measure',
                  hidden: false,
                  aggregation_type: 'count distinct',
                  label: 'custom_measure',
                  type: 'number',
                  definition: {
                    __type__: 'Heredoc',
                    name: 'sql',
                    content: '{{ created_at }}',
                  },
                  __childIdx__: 8,
                  __doc__: '',
                },
              },
              __fqn__: 'tenant',
              type: 'table',
              table_name: '"public"."tenants"',
              __doc__: '',
              name: 'tenant',
              __type__: 'TableModel',
            },
          }
        end

        let(:custom_dimension_params_with_binding_json) do
          custom_dimension_params.deep_dup.deep_merge({ binding_json: binding_json })
        end

        let(:custom_measure_params) do
          {
            project_id: project.id,
            model: {
              path: 'models/holistics_model/tenant.model.aml',
              name: 'tenant',
            },
            field: {
              description: 'test_custom_measure',
              label: 'test_custom_measure',
              name: 'test_custom_measure',
              sql: '{{ name }}',
              syntax: 'sql',
              type: 'text',
              aggregation_type: 'count',
            },
          }
        end

        let(:custom_measure_params_with_binding_json) do
          custom_measure_params.deep_dup.deep_merge({ binding_json: binding_json })
        end

        let(:update_dimension_params) do
          custom_dimension_params.deep_dup.deep_merge({ field: { name: 'name', sql: '{{ created_at }}' } })
        end

        let(:update_dimension_params_with_binding_json) do
          custom_dimension_params_with_binding_json.deep_dup.deep_merge({ field: { name: 'name',
                                                                                   sql: '{{ created_at }}', } })
        end

        let(:update_measure_params) do
          custom_measure_params.deep_dup.deep_merge({ field: { name: 'custom_measure', sql: 'COUNT({{ created_at }})',
                                                               aggregation_type: 'custom', } })
        end
        let(:update_measure_params_with_binding_json) do
          custom_measure_params_with_binding_json.deep_dup.deep_merge({ field: { name: 'custom_measure', sql: 'COUNT({{ created_at }})',
                                                                                 aggregation_type: 'custom', } })
        end

        it_behaves_like 'works with custom dimension'
        it_behaves_like 'works with custom measure'
        it_behaves_like 'should validate correct type or syntax'
        it_behaves_like 'utilize FE processing with custom dimension'
        it_behaves_like 'utilize FE processing with custom measure'
      end

      context 'with query model' do
        let(:custom_dimension_params) do
          {
            project_id: project.id,
            model: {
              path: 'models/holistics_model/tenant_query.model.aml',
              name: 'tenant_query_model',
            },
            field: {
              description: 'custom dimension',
              label: 'custom dimesion',
              name: 'custom_dimension',
              sql: '{{ name }}',
              syntax: 'sql',
              type: 'text',
              aggregation_type: nil,
            },
          }
        end

        let(:update_dimension_params) do
          custom_dimension_params.deep_dup.deep_merge({ field: { name: 'name', sql: '{{ #SOURCE.created_at }}' } })
        end

        it_behaves_like 'works with custom dimension'
      end

      # v2 should handle all cases in v1 + edge cases
      context 'with validation v2' do
        let(:custom_dimension_params) do
          {
            project_id: project.id,
            model: {
              path: 'models/holistics_model/tenant.model.aml',
              name: 'tenant',
            },
            field: {
              description: 'custom dimension',
              label: 'custom dimesion',
              name: 'custom_dimension',
              sql: '{{ name }}',
              syntax: 'sql',
              type: 'text',
              aggregation_type: nil,
            },
          }
        end

        let(:custom_measure_params) do
          {
            project_id: project.id,
            model: {
              path: 'models/holistics_model/tenant.model.aml',
              name: 'tenant',
            },
            field: {
              description: 'test_custom_measure',
              label: 'test_custom_measure',
              name: 'test_custom_measure',
              sql: '{{ name }}',
              syntax: 'sql',
              type: 'text',
              aggregation_type: 'count',
            },
          }
        end

        let(:update_dimension_params) do
          custom_dimension_params.deep_dup.deep_merge({ field: { name: 'name', sql: '{{ created_at }}' } })
        end

        let(:update_measure_params) do
          custom_measure_params.deep_dup.deep_merge({ field: { name: 'custom_measure', sql: 'COUNT({{ created_at }})',
                                                               aggregation_type: 'custom', } })
        end

        before do
          FeatureToggle.toggle_global(
            AmlStudio::DataModels::ValidateField::FT_VALIDATE_CUSTOM_AGGREGATION_MEASURE_V2, true,
          )
        end

        it_behaves_like 'works with custom dimension'
        it_behaves_like 'works with custom measure'
        it_behaves_like 'should validate correct type or syntax'

        # this case wasn't handled in v1
        it 'passes if there is custom measure reference in another measure' do
          new_params = custom_measure_params.deep_dup.deep_merge({
                                                                   field: {
                                                                     sql: '{{ custom_measure }}',
                                                                     aggregation_type: 'custom',
                                                                   },
                                                                 })
          post :submit_validate_field, params: new_params, as: :json
          job = assert_success_async_response!(['job', 'id'])
          expect(job.status).to eq('success')
        end
      end
    end

    describe 'POST #submit_restore_to_commit' do
      context 'enabled FT' do
        before do
          FeatureToggle.toggle_global(AmlStudio::Project::FT_GIT_RESTORE, true)
        end

        after do
          FeatureToggle.toggle_global(AmlStudio::Project::FT_GIT_RESTORE, false)
        end

        context 'with explicit git' do
          let(:work_flow) { user_repo.work_flow(admin) }
          let(:client) { user_repo.source }

          include_context 'aml_studio_explicit'

          it 'restores current repo to commit' do
            client.write_file('test.aml', 'This is the first version')
            client.commit_from_wd([], 'First commit')

            first_commit_oid = client.current_commit_hash

            client.write_file('test2.aml', 'This is the second version')
            client.commit_from_wd([], 'Second commit')

            params = {
              project_id: project.id,
              commit_oid: first_commit_oid,
              message: 'Test ABC',
            }

            post :submit_restore_to_commit, params: params, as: :json
            job = assert_success_async_response!(['job', 'id'])
            result = job.fetch_cache_data

            expect(result[:status]).to be_present
            expect(result[:restore_commit_oid]).to be_present
          end
        end

        context 'with implicit git' do
          include_context 'aml_studio_basic'
          let(:client) { proj_repo.source }

          it 'restores current repo to commit' do
            client.commit_files(
              client.current_commit_hash,
              { name: 'test', email: '<EMAIL>', message: 'First Commit' },
              { 'test.aml' => 'This is the first version' },
            )

            first_commit_oid = client.current_commit_hash

            client.commit_files(
              client.current_commit_hash,
              { name: 'test', email: '<EMAIL>', message: 'Second Commit' },
              { 'test.aml' => 'This is the second version' },
            )

            params = {
              project_id: project.id,
              commit_oid: first_commit_oid,
              message: 'Test ABC',
            }

            post :submit_restore_to_commit, params: params, as: :json
            job = assert_success_async_response!(['job', 'id'])
            result = job.fetch_cache_data

            expect(result[:status]).to be_present
            expect(result[:restore_commit_oid]).to be_present
          end
        end
      end

      context 'disabled FT' do
        include_context 'aml_studio_basic'
        let(:client) { proj_repo.source }

        before do
          FeatureToggle.toggle_global(AmlStudio::Project::FT_GIT_RESTORE, false)
        end

        it 'restores current repo to commit', :skip_schema_conform do
          client.commit_files(
            client.current_commit_hash,
            { name: 'test', email: '<EMAIL>', message: 'First Commit' },
            { 'test.aml' => 'This is the first version' },
          )

          first_commit_oid = client.current_commit_hash

          client.commit_files(
            client.current_commit_hash,
            { name: 'test', email: '<EMAIL>', message: 'Second Commit' },
            { 'test.aml' => 'This is the second version' },
          )

          params = {
            project_id: project.id,
            commit_oid: first_commit_oid,
            message: 'Test ABC',
          }

          post :submit_restore_to_commit, params: params, as: :json

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response_result['message']).to eq('This feature is not available for this tenant')
        end
      end
    end

    describe 'POST #submit_sync_remote' do
      describe 'on explicit project' do
        include_context 'aml_studio_explicit'

        it 'generate job for remote sync on explicit project' do
          post :submit_sync_remote, params: { project_id: project.id, perform_fetch: false }
          assert_success_async_response!(['job', 'id'])

          job = assert_success_async_response!(['job', 'id'])
          result = job.fetch_cache_data

          expect(result['data']).to have_key 'previous_repo_state'
          expect(result['data']).to have_key 'state'
        end
      end

      describe 'on implicit project' do
        include_context 'aml_studio_basic'

        it 'raise error' do
          post :submit_sync_remote, params: { project_id: project.id, perform_fetch: false }
          expect(response).to have_http_status(:unprocessable_entity)
          expect(response.body).to match(/This operation is for explicit project only/)
        end
      end
    end

    describe 'POST #submit_commit' do
      describe 'on explicit project' do
        include_context 'aml_studio_explicit'

        let(:client) { current_repo.source }

        it 'generate job for commit on explicit project' do
          client.write_file('test.aml', 'test 1')

          post :submit_commit, params: { project_id: project.id, message: 'Test commit' }
          assert_success_async_response!(['job', 'id'])

          job = assert_success_async_response!(['job', 'id'])
          result = job.fetch_cache_data

          expect(result['data']).to have_key 'previous_repo_state'
          expect(result['data']).to have_key 'state'
        end
      end

      describe 'on implicit project' do
        include_context 'aml_studio_basic'

        it 'raise error' do
          post :submit_commit, params: { project_id: project.id, message: 'Test commit' }
          expect(response).to have_http_status(:unprocessable_entity)
          expect(response.body).to match(/This operation is for explicit project only/)
        end
      end
    end

    describe 'POST #resolve_conflicts' do
      describe 'on explicit project' do
        include_context 'aml_studio_explicit_with_conflicts'

        it 'resolves conflicts for a file' do
          post :resolve_conflicts,
               params: { project_id: project.id, resolve_data: [{ file_path: 'both_modified.aml', action: 'modify',
                                                                  content: '[ours] test both_modified', }], }
          assert_success_response!

          expect(client.read_file('both_modified.aml')[:content]).to eq('[ours] test both_modified')
        end

        it 'resolves conflicts for multiple files' do
          post :resolve_conflicts,
               params: { project_id: project.id, resolve_data: [{ file_path: 'both_modified.aml', action: 'modify',
                                                                  content: '[ours] test both_modified', },
                                                                { file_path: 'deleted_by_them.aml', action: 'modify',
                                                                  content: '[ours] test deleted_by_them', },
                                                                { file_path: 'deleted_by_us.aml',
                                                                  action: 'remove', },], }
          assert_success_response!

          expect(client.read_file('both_modified.aml')[:content]).to eq('[ours] test both_modified')
          expect(client.read_file('deleted_by_them.aml')[:content]).to eq('[ours] test deleted_by_them')
          expect(client.path_exists?('deleted_by_us.aml')).to be(false)
        end
      end
    end

    describe 'POST #write_files_with_status' do
      shared_examples 'happy writes' do
        it 'write then overwrite' do
          test_files = [
            { file_path: 'test.aml', content: 'Hello world' },
            { file_path: 'test2.aml', content: 'Hello world 2' },
          ]
          post :write_files_with_status, params: { project_id: project.id, files: test_files },
                                         format: :json

          expect(response).to be_successful

          expect(workflow.read_file('test.aml')[:content]).to eq('Hello world')
          expect(workflow.read_file('test2.aml')[:content]).to eq('Hello world 2')

          # overwrite by default
          test_files = [
            { file_path: 'test.aml', content: 'Hello world 3' },
          ]
          post :write_files_with_status, params: { project_id: project.id, files: test_files },
                                         format: :json

          expect(response).to be_successful

          expect(workflow.read_file('test.aml')[:content]).to eq('Hello world 3')
          expect(workflow.read_file('test2.aml')[:content]).to eq('Hello world 2')
        end
      end

      context 'explicit git' do
        let(:workflow) { user_repo.work_flow(admin) }

        include_context 'aml_studio_explicit'

        it_behaves_like 'happy writes'

        it 'report correct success and failure status' do
          workflow.write_file('test.aml', 'Hello world')

          test_files = [
            { file_path: 'test.aml', content: 'Hello world 2' },
            { file_path: '../../test2.aml', content: 'Hello world 3' }, # invalid path
          ]

          post :write_files_with_status, params: { project_id: project.id, files: test_files },
                                         format: :json

          expect(response).to have_http_status(:ok)

          result = response_result['data']

          expect(result['success'][0]['path']).to eq('test.aml')
          expect(result['success'][0]['content']).to eq('Hello world 2')

          expect(result['failure'][0]['path']).to eq('../../test2.aml')
          expect(result['failure'][0]['error'].downcase).to include('tried to access')
        end
      end

      context 'implicit git' do
        let(:workflow) { proj_work_flow }

        include_context 'aml_studio_basic'

        it_behaves_like 'happy writes'

        it 'report correct success and failure status' do
          workflow.write_file('test.aml', 'Hello world')

          test_files = [
            { file_path: 'test.aml', content: 'Hello world 2' },
            { file_path: '../../test2.aml', content: 'Hello world 3' }, # invalid path
          ]

          post :write_files_with_status, params: { project_id: project.id, files: test_files },
                                         format: :json

          expect(response).to have_http_status(:ok)

          result = response_result['data']

          expect(result['success'].size).to eq(0)

          expect(result['failure'].size).to eq(2)
          expect(result['failure'][0]['path']).to eq('test.aml')
          expect(result['failure'][0]['error'].downcase).to include('tried to access')
          expect(result['failure'][1]['path']).to eq('../../test2.aml')
          expect(result['failure'][1]['error'].downcase).to include('tried to access')
        end
      end
    end

    describe 'get latest pr status for current workflow' do
      include_context 'aml_studio_explicit'

      before do
        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, true)
        FeatureToggle.toggle_global(AmlStudio::Project::FT_EXTERNAL_GIT, true)
        FeatureToggle.toggle_global(AmlStudio::Project::FT_EXPLICIT_GIT, true)
        project.update!(settings: project.settings.merge(is_external_integrated: true, enabled_pr_workflow: true),
                        remote_url_origin: '**************:holistics/git_repo.git',)

        # Create the integration directly instead of using the removed method
        git_provider = AmlStudio::ExternalGit::ParseRemoteUrl.new.call('**************:holistics/git_repo.git')
        integration = AmlStudio::ExternalGitIntegration.create!(
          provider: git_provider.provider,
          repo_url: git_provider.normalize_git_url_to_https,
        )
        project.update!(external_git_integration: integration)
      end

      after do
        FeatureToggle.toggle_global(AmlStudio::Project::FT_PR_WORKFLOW, false)
        FeatureToggle.toggle_global(AmlStudio::Project::FT_EXTERNAL_GIT, false)
        FeatureToggle.toggle_global(AmlStudio::Project::FT_EXPLICIT_GIT, false)
      end

      context 'get latest pr for current workflow' do
        let(:external_git_integration) { project.external_git_integration }

        it 'get latest pr for current workflow' do
          external_git_integration.update!(token: SourceControl::Backend.passphrase_encryptor.encrypt('test_token'))
          AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:fetch_latest_open_pr).and_return(nil)
          get :get_latest_pull_request, params: {
            project_id: project.id,
          }
          expect(response).to have_http_status(:ok)
          expect(response_result['data']).to be_nil
          assert_response_status!(200)

          pr_data = {
            'number' => 2,
            'title' => 'Test PR',
            'state' => 'open',
            'html_url' => 'https://github.com/holistics/git_repo/pull/2',
          }
          AmlStudio::GitFlows::GitClient::GithubClient.any_instance.stub(:fetch_latest_open_pr).and_return(pr_data)
          get :get_latest_pull_request, params: {
            project_id: project.id,
          }

          expect(response).to have_http_status(:ok)
          response_data = response_result['data'].except('created_at', 'updated_at')
          expect(response_data).to eq(pr_data)
        end

        it 'raise error when pr workflow is not enabled for this project' do
          project.update!(settings: project.settings.merge(enabled_pr_workflow: false))
          get :get_latest_pull_request, params: {
            project_id: project.id,
          }
          expect(response).to have_http_status(:unprocessable_entity)
          expect(response_result['message']).to eq('PR Workflow is not enabled')
        end
      end
    end
  end
end
