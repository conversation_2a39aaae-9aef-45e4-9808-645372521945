// refactor from mixin app/javascript/modules/DataModels/mixins/dataExplorer.js
// please update from this file and mixin file for backward compatible

import { createInjectionState } from '@vueuse/core';
import type {
  Adhoc<PERSON>ield,
  AmqlAdhocField,
  DataSet,
  VizSetting as IViz,
} from '@holistics/types';
import {
  computed, getCurrentInstance, nextTick, onBeforeUnmount, onMounted, ref, Ref, watch,
} from 'vue';
import pThrottle from 'p-throttle';
import { check as checkFeatureToggle } from '@holistics/feature-toggle';
import {
  cloneDeep, debounce, find, get, isEqual, mapValues, noop, set, every, isNil,
} from 'lodash';
import ReportSort from '@/modules/QueryReports/services/ReportSort';
import Model from '@/modules/DataModels/models/Model';
import type DataSetClass from '@/modules/DataSets/models/DataSet';
import AmlModel from '@aml-studio/client/models/Model';
import AmlDataset from '@aml-studio/client/models/Dataset';
import VizSettingClass from '@/modules/Viz/models/VizSetting';
import generateMapOfSelectedFields from '@/modules/Viz/utils/generateMapOfSelectedFields';
import DataModelsList from '@/modules/DataSets/components/explorer/DataModelsList.vue';
import ExplorationHistory from '@/modules/DataSets/services/ExplorationHistory';
import { defaultLimit } from '@/modules/Viz/services/getDefaultRecordLimit';
import VizSettingBuilder from '@/modules/Viz/services/VizSettingBuilder';
import {
  RouteLocationNormalizedLoaded, RouteRecordName, useRoute, useRouter,
} from 'vue-router';
import * as Sentry from '@sentry/browser';
import { handleAjaxError } from '@/core/services/ajax';
import ExplorerControls from '@/modules/DataModels/components/explorer/ExplorerControls.vue';
import VizResult from '@/modules/Viz/components/VizResult.vue';
import TabularData from '@/modules/Viz/components/result/TabularData.vue';
import { User } from '@/core/plugins/user';
import { DATASET_EXPLORE_TOUR_STEPS, TOUR_NAMES } from '@/modules/Home/components/onboarding/InAppOnboarding/constants';
import { AdditionalVizOptions } from '@/modules/Viz/services/types';
import type { PivotedData } from '@/modules/Table/types/propData';
import { VizSettingTransformation } from '@/modules/Viz/types/vizSettingTransformation';
import { transformVizSetting } from '@/modules/Viz/services/transformVizSetting';
import { useExplorerRunnerOpts } from './useExplorerRunnerOpts';
import { useExplorerCustomChart } from './useExplorerCustomChart';

export type VizSetting = VizSettingClass | IViz;

// TODO: update later at the call side
export interface Source {
  id: string | number
  type: 'DataSet' | 'DataModel',
  action: 'preview' | 'edit',
}

type WarningFields = Record<string, any>;

// not used: joins, joinConfigs => to be defined as prop only

// should use this composable for 3.0/4.0 dataSet only => dataSet is required
// TODO: should we remove dataModels & rootModelId from this interface ???
interface UseDataExplorerOptions {
  // define as props
  // TODO: define models, not sure it's a 3.0/ 4.0 models ???
  dataModels: Ref<(Model | AmlModel)[]>
  rootModelId?: Ref<number | string | undefined>,
  dataSet: Ref<DataSet | AmlDataset | DataSetClass | undefined>,
  initialVizSetting: Ref<VizSetting | undefined>,
  source?: Ref<Source | undefined>,
  exploreRouteParam?: Ref<string | undefined>,
  exploreQueryParam?: Ref<string | undefined>,
  additionalVizOptions?: Ref<AdditionalVizOptions>,
  // end props
  emit: { (event: 'onExport', jobId: number): void, (event: 'vizChanged', viz: VizSetting): void },
  // this.$refs['data-models-list']
  dataModelsListRef?: Ref<InstanceType<typeof DataModelsList> | undefined>
  // this.$refs.controls
  // @ts-ignore
  controlsRef: Ref<InstanceType<typeof ExplorerControls> | undefined>
  // this.$refs.viz
  // @ts-ignore
  vizResultRef: Ref<InstanceType<typeof VizResult> | undefined>;
  // this.$refs['tabular-data']
  // @ts-ignore
  tabularDataRef?: Ref<InstanceType<typeof TabularData> | undefined>
  getDefaultVizSettingValueFn?: () => Record<string, any>,
}

const [useProvidedDataExplorer, _useDataExplorer] = createInjectionState(({
  dataModels = ref([]), rootModelId = ref(), dataSet,
  initialVizSetting, additionalVizOptions = ref({}),
  source,
  exploreRouteParam = ref(), exploreQueryParam = ref(),
  emit,
  dataModelsListRef = ref(), controlsRef, vizResultRef, tabularDataRef = ref(),
  getDefaultVizSettingValueFn,
}: UseDataExplorerOptions) => {
  // data
  const state = ref('none');
  const initialized = ref(false);
  const vizSetting = ref<VizSetting>();
  const appliedVizSetting = ref<VizSetting>();
  const pivotedData = ref<PivotedData>();
  const isRendering = ref(false);
  const isVizDataOutdated = ref(false);
  const showUpdateTooltip = ref(false);
  const showUpdateTooltipTimeout = ref<NodeJS.Timeout>();
  const sql = ref<string>();
  const aql = ref<string>();
  const lastCacheUpdated = ref<Date>();
  const vizNoData = ref(true);
  const vizUpdatedAt = ref(new Date());
  const explorationHistory = ref<ExplorationHistory>();
  const exploreHash = ref<string>();
  const warningFields = ref<WarningFields>({});
  const vizSettingHasNoFields = ref<boolean>(true);
  const jobPoller = ref<Record<string, any> | undefined>(undefined);
  const showOutOfSyncBanner = ref(false);

  // mixin
  const {
    autorun, runnerOpts, limit, controlsEnabled,
  } = useExplorerRunnerOpts();
  const { customChart, refreshCustomChart } = useExplorerCustomChart({ vizSetting });

  // Onboarding
  const app = getCurrentInstance();
  const tours = app?.appContext.config.globalProperties.$tours;

  const user = new User();
  const isBizUserOrViewer = user.isBizUser || user.isViewer;
  // computed
  const rootModel = computed(() => find(dataModels.value, { id: rootModelId.value }));
  // @ts-ignore
  const isCache = computed(() => rootModel.value?.backend_type === 'PgcacheModel');
  const useCachingV2 = computed(() => !isCache.value && checkFeatureToggle('dataset_explorer:viz_caching_v2'));
  const forceCachedOnFrontend = computed(() => {
    return !autorun.value || runnerOpts.value.disabled;
  });
  const shouldLimitPreview = computed(() => {
    if (['DashboardWidget', 'QueryReport'].includes(source?.value?.type || '')) {
      return source?.value?.action === 'edit';
    }
    return true;
  });
  const vizOptions = computed(() => {
    let sort;
    if (dataSet.value) {
      sort = new ReportSort(`ds-${dataSet.value.id}`);
    } else if (rootModel.value) {
      sort = new ReportSort(`dm-${rootModel.value.id}`);
    } else {
      return additionalVizOptions.value;
    }
    return {
      container: '.__viz-container',
      sort,
      exploring: shouldLimitPreview.value,
      tableNoStretch: false,
      ...additionalVizOptions.value,
    };
  });
  const shouldRenderOnInit = computed(() => {
    return !!(initialVizSetting.value || exploreHash.value);
  });

  const showTabularData = computed(() => !!(vizSetting.value && vizSetting.value?.viz_type));
  const disableLimit = computed(() => {
    const vizType = vizSetting.value?.viz_type;
    return vizType && vizType === 'metric_sheet';
  });
  const adhocFields = computed<AdhocField[]>(() => vizSetting.value?.adhoc_fields || []);
  const adhocAqlMetrics = computed(() => adhocFields.value.filter(f => !!f.aggregation_type && f.syntax === 'aql'));
  const adhocAqlDimensions = computed<AmqlAdhocField[]>(() => vizSetting.value?.amql?.adhoc_fields || []);

  const vizSelectedFieldsMap = computed(() => {
    if (!vizSetting.value) return {};
    return generateMapOfSelectedFields(vizSetting.value, { customChart: customChart.value });
  });
  // only support dataSet.views[0)
  const hasDatasetView = computed(() => checkFeatureToggle('data_sets:views') && !!get(dataSet.value, 'views[0]'));

  const enableRenderOutOfSyncBanner = computed(() => {
    const showBannerToggle = checkFeatureToggle('out-of-sync:show-banner');
    const isVizNotRendering = !isRendering.value;
    const isAutoRunDisabled = !autorun.value;
    const hasVizSettingFields = !vizSettingHasNoFields.value;

    return every([
      showBannerToggle,
      isVizNotRendering,
      isAutoRunDisabled,
      hasVizSettingFields,
    ]);
  });

  // methods
  function handleWarningFields (newFields: WarningFields) {
    warningFields.value = newFields;
  }

  async function renderViz (options = {}) {
    isRendering.value = true;
    try {
      showUpdateTooltip.value = false;
      if (showUpdateTooltipTimeout.value) {
        clearTimeout(showUpdateTooltipTimeout.value);
      }
      if (vizSetting.value) {
        emit('vizChanged', vizSetting.value);
      }
      await nextTick();
      const promises = [];
      if (vizResultRef.value) {
        promises.push(vizResultRef.value.update(options));
      }
      if (tabularDataRef.value) {
        promises.push(tabularDataRef.value.update(options));
      }
      await Promise.all(promises);

      const vTour = tours[TOUR_NAMES.DataSetExploreTour];
      if (vTour && vTour.isRunning && vTour.step.name === DATASET_EXPLORE_TOUR_STEPS.WaitForGettingResult) {
        tours[TOUR_NAMES.DataSetExploreTour].nextStep();
      }
    } finally {
      isRendering.value = false;
    }
  }

  const currentRoute = useRoute();
  const router = useRouter();
  const syncingRoute = ref(false);
  const canUndo: Ref<boolean | undefined> = ref(false);
  const canRedo: Ref<boolean | undefined> = ref(false);

  function onPopState (e: PopStateEvent) {
    if (!explorationHistory.value) return;
    canUndo.value = e.state.position + 1 > explorationHistory.value.initialHistoryPage && !!e.state.back;
    canRedo.value = !!e.state.forward;
  }
  window.addEventListener('popstate', onPopState);

  onBeforeUnmount(() => {
    window.removeEventListener('popstate', onPopState);
  });

  function routeVizSettingHash (route = currentRoute): string | null {
    let value: (string | null)[] | string | null = null;
    if (exploreRouteParam.value) {
      value = route.params[exploreRouteParam.value] || '';
    } else if (exploreQueryParam.value) {
      value = route.query[exploreQueryParam.value] || '';
    }
    if (Array.isArray(value)) {
      return value.slice(-1)[0];
    }
    return value;
  }

  async function setExploreHashParam (hash: string) {
    if (exploreRouteParam.value) {
      const params = { ...currentRoute.params };
      if (hash) {
        params[exploreRouteParam.value] = hash;
      } else {
        delete params[exploreRouteParam.value];
      }
      await router.push({
        name: currentRoute.name as RouteRecordName | undefined,
        params,
      });
    } else if (exploreQueryParam.value) {
      await router.push({
        query: {
          ...currentRoute.query,
          [exploreQueryParam.value]: hash || undefined,
        },
      });
    }
    // explorationHistory.value?.trackNewPage();
    canUndo.value = true;
    canRedo.value = false;
  }

  async function _setRouteFromExplore (viz: VizSetting) {
    if (!explorationHistory.value) return;

    let newHash;
    try {
      newHash = await explorationHistory.value.addVizSetting(viz) || '';
    } catch (err) {
      Sentry.captureException(err);
      return;
    }

    const currentHash = routeVizSettingHash();
    if (!newHash && !currentHash) return;
    if (newHash === currentHash) return;

    // ignore if vizsetting has been changed while we were generating new hash
    if (!isEqual(vizSetting.value, viz)) return;

    exploreHash.value = newHash;
    setExploreHashParam(newHash);
  }

  // since the function is debounced, if we want to wait for the async result, we need to use a callback
  const setRouteFromExplore = debounce(async (viz: VizSetting, callback = noop) => {
    try {
      await _setRouteFromExplore(viz);
    } finally {
      syncingRoute.value = false;
    }
    callback();
  }, 1000);

  async function setExploreFromRoute (route: RouteLocationNormalizedLoaded) {
    if (!explorationHistory.value) return;

    const hashFromRoute = routeVizSettingHash(route);
    if (hashFromRoute === exploreHash.value) return;

    try {
      const newViz = await explorationHistory.value.getVizSetting(hashFromRoute);
      if (hashFromRoute !== routeVizSettingHash(currentRoute)) return; // ignore if route has changed while we were fetching VizSetting
      vizSetting.value = newViz;
      exploreHash.value = hashFromRoute as string | undefined;
    } catch (err) {
      handleAjaxError(err, 'Could not restore exploration state', 'warning');
    }
  }

  function undo () {
    return router.back();
  }
  function redo () {
    return router.forward();
  }

  /**
   * NOTE: only expand the selected Models on initialization,
   * so that this automatic expansion doesn't mess with the user's manual expansions
   */
  function expandVizSelectedModels () {
    if (!dataModelsListRef.value) return;

    if (hasDatasetView.value) {
      dataModelsListRef.value.expandDatasetView();
    } else {
      const modelExpansionMap = mapValues(vizSelectedFieldsMap.value, () => true);
      dataModelsListRef.value.expandModels(modelExpansionMap);
    }
  }

  function initExplorationHistory (initialRoute: RouteLocationNormalizedLoaded) {
    if (!exploreRouteParam.value && !exploreQueryParam.value) return;

    const currentDataSet = dataSet.value;
    if (!currentDataSet) return;
    if (!('addVizSetting' in currentDataSet)) return;

    const hashFromRoute = routeVizSettingHash(initialRoute);
    explorationHistory.value = new ExplorationHistory(currentDataSet, {
      initialHash: hashFromRoute,
    });
  }

  function getDefaultVizSettingValue () {
    if (getDefaultVizSettingValueFn) return getDefaultVizSettingValueFn();

    // Set default limit value
    return {
      settings: {
        misc: {
          row_limit: defaultLimit,
        },
      },
    };
  }

  function buildVizSetting (vizSettingValue: Partial<VizSetting>) {
    if (vizSettingValue && vizSettingValue.viz_type === 'custom') {
      vizSetting.value = vizSettingValue as VizSetting;
    } else {
      vizSetting.value = (new VizSettingBuilder(rootModel.value, vizSettingValue, [])).values;
    }
  }

  function initVizSetting () {
    // from report cache explorer
    if (initialVizSetting.value) {
      buildVizSetting(initialVizSetting.value);
    } else {
      buildVizSetting(getDefaultVizSettingValue());
    }
    if (explorationHistory.value && vizSetting.value) {
      // associate initial VizSetting with empty hash
      explorationHistory.value.setBaseVizSetting('', vizSetting.value);
    }
  }

  function onVizDataOutdated () {
    if (controlsRef.value) {
      controlsRef.value.setUncancellable();
    }
    isVizDataOutdated.value = true;
    showUpdateTooltip.value = true;
    if (showUpdateTooltipTimeout.value) {
      clearTimeout(showUpdateTooltipTimeout.value);
    }
    showUpdateTooltipTimeout.value = setTimeout(() => {
      showUpdateTooltip.value = false;
    }, 2000);
    showOutOfSyncBanner.value = enableRenderOutOfSyncBanner.value;
  }

  function onVizDataUpdated (payload: Record<any, any>) {
    isVizDataOutdated.value = false;
    showUpdateTooltip.value = false;
    vizNoData.value = !!get(payload, 'noData');
    if (useCachingV2.value) {
      lastCacheUpdated.value = get(payload, 'lastCacheUpdated');
    }
    if (controlsRef.value) {
      controlsRef.value.setUncancellable();
    }
    showOutOfSyncBanner.value = false;
    appliedVizSetting.value = cloneDeep(vizSetting.value);
  }

  function onVizUpdated (payload: Record<string, any>) {
    vizUpdatedAt.value = payload.updatedAt;
  }

  function onPivoted (val: PivotedData) {
    pivotedData.value = val;
  }

  function onExport (jobId: number) {
    emit('onExport', jobId);
  }

  function setExploreSql ({ sql: exploredSql, aql: exploredAql }: { sql: string, aql?: string }) {
    sql.value = exploredSql;
    aql.value = exploredAql ?? '';
  }

  function setLimit (value: number) {
    if (value === get(vizSetting.value, 'settings.misc.row_limit')) return;

    const clonedVizSetting = cloneDeep(vizSetting.value) as VizSetting;
    set(clonedVizSetting, 'settings.misc.row_limit', value);
    vizSetting.value = clonedVizSetting;

    if (autorun.value && initialized.value) {
      renderViz();
    }
  }

  async function init () {
    initExplorationHistory(currentRoute);
    initVizSetting();
    await setExploreFromRoute(currentRoute);
    runnerOpts.value.limit = get(vizSetting.value, 'settings.misc.row_limit', runnerOpts.value.limit);
    state.value = 'success';
    await nextTick();
    await refreshCustomChart(); // make sure custom chart is fetched before expandVizSelectedModels
    expandVizSelectedModels();
    if (shouldRenderOnInit.value) {
      await renderViz({ forceCachedOnBackend: true, forceCachedOnFrontend: false });
    }
    initialized.value = true;
  }

  async function modelChanged () {
    initialized.value = false;
    vizSetting.value = undefined;
    await init();
  }

  function handleNoFields (value: boolean) {
    vizSettingHasNoFields.value = value;
  }

  function setJobPoller (poller: Record<string, any>) {
    jobPoller.value = poller;
  }

  watch(() => rootModel.value?.id, () => modelChanged());
  watch(() => vizSetting.value?.settings?.misc?.row_limit, (vizSettingLimit) => {
    // if limit is not defined. e.g. when changing viz type, vizSchema will set limit to null/undefined
    // we want to keep the current limit, which is either defaultLimit or the limit set by user
    if (isNil(vizSettingLimit)) return;

    if (vizSettingLimit !== limit.value) {
      runnerOpts.value.limit = vizSettingLimit;
    }
  });
  watch(limit, (newVal) => setLimit(newVal));
  watch(currentRoute, (newVal) => setExploreFromRoute(newVal));

  const forceRender = pThrottle({ limit: 1, interval: 1000 })((options = {}) => {
    return renderViz({
      bustBackendCache: get(options, 'bustBackendCache', !useCachingV2.value),
      bustFrontendCache: get(options, 'bustFrontendCache', true),
      forceCachedOnBackend: false,
      hOtelContext: get(options, 'hOtelContext'),
    });
  });

  onMounted(() => {
    if (isCache.value) runnerOpts.value.autorun = true;

    init();
  });

  function onVizSettingChange (vs: VizSetting, forceReRender = false) {
    if (isEqual(vs, vizSetting.value)) return false;

    lastCacheUpdated.value = undefined;
    vizSetting.value = vs;
    if (initialized.value) {
      syncingRoute.value = true;
      setRouteFromExplore(vs);
      if (autorun.value || forceReRender) {
        forceRender();
      }
    }
    return true;
  }
  function onVizSettingTransform (transformVizSettingPayload: VizSettingTransformation, options: { forceRender?: boolean } = {}) {
    const vs = transformVizSetting(vizSetting.value, transformVizSettingPayload);
    if (!vs) return false;

    return onVizSettingChange(vs, options.forceRender);
  }

  return {
    initialized,
    forceRender,
    state,
    rootModel,
    vizSetting,
    adhocFields,
    adhocAqlMetrics,
    adhocAqlDimensions,
    appliedVizSetting,
    vizOptions,
    pivotedData,
    vizSelectedFieldsMap,
    onPivoted,
    onVizDataOutdated,
    onVizDataUpdated,
    handleWarningFields,
    showTabularData,
    isBizUserOrViewer,
    showUpdateTooltip,
    runnerOpts,
    forceCachedOnFrontend,
    controlsEnabled,
    disableLimit,
    aql,
    sql,
    setExploreSql,
    isRendering,
    autorun,
    lastCacheUpdated,
    canUndo: computed(() => {
      if (syncingRoute.value) return 'pending';
      return canUndo.value;
    }),
    canRedo: computed(() => {
      if (syncingRoute.value) {
        /**
         * 1. redo can use existing viz cache most of the time -> fast -> no need to show pending
         * 2. showing pending on undo alone is enough anyway
         */
        return false;
      }
      return canRedo.value;
    }),
    syncingRoute,
    explorationHistory,
    undo,
    redo,
    setRouteFromExplore,
    vizNoData,
    onExport,
    onVizUpdated,
    limit,
    vizUpdatedAt,
    warningFields,
    buildVizSetting,
    renderViz,
    vizSettingHasNoFields,
    handleNoFields,
    jobPoller,
    setJobPoller,
    isVizDataOutdated,
    showOutOfSyncBanner,
    onVizSettingTransform,
    onVizSettingChange,
  };
});

export { useProvidedDataExplorer };

export function useDataExplorer () {
  const dataExplorerStore = _useDataExplorer();
  if (!dataExplorerStore) throw new Error('Please call `useProvidedDataExplorer` on the appropriate parent component');

  return dataExplorerStore;
}
