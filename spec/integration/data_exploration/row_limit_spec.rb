# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'row limit', js: true do
  include_context 'data_set'
  let(:ds_report) do
    create :products_modeling_report, owner: admin, data_set: data_set
  end

  before do
    FeatureToggle.toggle_global(Tenant::FT_TENANT_VISUALIZATION_SETTING_V2, true)
  end

  def expect_limit(limit)
    expect(page).to have_css('.ci-limit-select-toggle', text: limit)

    safe_click '.ci-viz-type-metric_kpi'
    expect(page).to have_css('.ci-limit-select-toggle', text: limit)

    safe_click '.ci-viz-type-data_table'
    expect(page).to have_css('.ci-limit-select-toggle', text: limit)
  end

  context 'when changing viz type' do
    context 'limit is not set' do
      it 'should keep the current limit' do
        safe_login admin, data_set_path(data_set)

        wait_for_element_load('.ci-explorer-control-get-results')
        expect_limit('5000')
      end
    end

    context 'when limit is set' do
      let(:viz_setting) do
        create(:viz_setting,
              viz_type: 'line_chart',
              source_id: data_set.id,
              source_type: 'DataSet',
              fields: {
                x_axis: {},
                series: {
                  path_hash: nil,
                  format: nil,
                  custom_label: nil,
                },
                y_axes: [],
              },
              settings: { misc: { row_limit: 1 } },
              format: {},
              filters: [],
              adhoc_fields: [],)
      end

      it 'should keep the current limit' do
        safe_login(admin, "#{data_set_path(data_set)}/#{viz_setting.hashid}")
        wait_for_element_load('.ci-explorer-control-get-results')

        expect_limit('1')
      end

      it 'should keep the current limit when changing limit' do
        safe_login(admin, "#{data_set_path(data_set)}/#{viz_setting.hashid}")
        wait_for_element_load('.ci-explorer-control-get-results')

        safe_click('.ci-limit-select-toggle')
        safe_click('.ci-custom-limit-input')
        fill_text('.ci-custom-limit-input', '125')
        safe_click('.ci-custom-limit-btn')

        expect_limit('125')
      end
    end
  end
end
