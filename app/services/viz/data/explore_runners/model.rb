# typed: true
# frozen_string_literal: true

module Viz::Data::ExploreRunners
  class Model < Base
    extend T::Sig

    sig { returns(VizSetting) }
    attr_reader :viz_setting

    sig { returns(Modeling::Models::DataModel) }
    attr_reader :explore

    sig { returns(Hash) }
    attr_reader :generator_options

    sig { returns(T.nilable(Viz::Data::Vl::GeneratorMetadata)) }
    attr_reader :generator_metadata

    sig do
      params(
        viz_setting: VizSetting,
        explore: Modeling::Models::DataModel,
        generator_options: Hash,
        generator_metadata: T.nilable(Viz::Data::Vl::GeneratorMetadata),
      ).void
    end
    def initialize(
      viz_setting:,
      explore:,
      generator_options:,
      generator_metadata: nil
    )
      super()
      @viz_setting = viz_setting
      @explore = explore
      @generator_options = generator_options
      @generator_metadata = generator_metadata
    end

    def execute(user:)
      cached =
        if generator_options[:bust_cache]
          false
        elsif Canal.canal_cache_key?(cache_key)
          Canal::Cache.exist(cache_key)
        else
          PostgresCache.exist(cache_key)
        end

      if cached
        sync_run_on_pg_cache
      elsif generator_options[:force_cached]
        raise Holistics::InvalidOperation, 'OUTDATED_VIZ_DATA_CACHE'
      else
        async_run_on_customer_database(user)
      end
    end

    def execute_forced_sync
      cached =
        if generator_options[:bust_cache]
          false
        elsif Canal.canal_cache_key?(cache_key)
          Canal::Cache.exist(cache_key)
        else
          PostgresCache.exist(cache_key)
        end

      sync_run_to_pg_cache unless cached

      sync_run_on_pg_cache
    end

    def cache_key
      @cache_key ||= Viz::Caching::Keys.generate_for(explore)
    end

    def cache_model
      self.class.get_cache_model(cache_key)
    end

    def sync_run_to_pg_cache
      DataModel.execute_model_query(
        explore,
        cache_key,
        explore.data_source_id,
        cache_duration: generator_options[:cache_duration],
        week_start_day: viz_setting.week_start_day,
      )
    end

    def self.get_cache_model(cache_key)
      cache_model =
        if Canal.canal_cache_key?(cache_key)
          cache_entry = Canal::Cache.fetch_entry(cache_key)
          cache_entry ? Canal::Cache.model(cache_entry) : nil
        else
          cache_metadata = PostgresCache.fetch_metadata(cache_key)
          cache_metadata.present? ? DataModel.build_sql_gen_cache_model(cache_metadata) : nil
        end

      raise Holistics::DataExpired, 'Cache expired. Please re-run your query/report' unless cache_model

      cache_model
    end

    def self.pg_cache_explore_result(cache_key, viz_setting, generator_options, meta: {})
      if Canal.canal_cache_key?(cache_key) && (cache_entry = Canal::Cache.fetch_entry(cache_key))
        cache_metadata = cache_entry.metadata
        cache_model = Canal::Cache.model(cache_entry)
      else
        cache_metadata = PostgresCache.fetch_metadata(cache_key)
        cache_model = get_cache_model(cache_key)
      end

      log_pg_cache_explore_result_inputs(cache_key: cache_key, cache_model: cache_model, viz_setting: viz_setting,
                                         generator_options: generator_options,)

      result =
        if (new_result = new_result_from_cache(cache_model, viz_setting, generator_options, cache_metadata: cache_metadata))
          new_result
        elsif Canal.canal_cache_key?(cache_key)
          # canal does not work with old flow
          raise 'Something went wrong. Try checking pivot_v2 and table_v2 FTs.'
        else
          generator_service = Viz::Data::GeneratorService.new(viz_setting: viz_setting, root_model: cache_model)
          T.cast(generator_service.explore(generator_options, nil, true).result, ::Viz::Data::Vl::ExploreResult)
        end

      if result.is_a?(::Viz::Data::Vl::ExplorePaginateResult) ||
         result.is_a?(::Viz::Data::Vl::PivotPaginateResult)
        result = result.merge(
          meta: result[:meta].merge(
            parsed_query: cache_metadata['parsed_query'],
            **meta,
            last_cache_updated: cache_metadata['saved_at']&.utc,
            cache_key: cache_key,
          ),
        )
        result[:meta][:parsed_query] = '' unless ThreadContext.get_or_nil(:current_user)&.can_view_generated_sql?
        result[:meta][:executed_aql] = cache_metadata['executed_aql'] if cache_metadata['executed_aql']
      end
      result
    end
    HOtel::Wrapper.wrap(Model.singleton_class, :pg_cache_explore_result)

    sig do
      params(
        cache_model: Modeling::Models::DataModel,
        viz_setting: VizSetting,
        generator_options: T.untyped,
        cache_metadata: T::Hash[T.untyped, T.untyped],
      ).returns(T.nilable(::Viz::Data::Vl::ExploreResult)).checked(:tests)
    end
    def self.new_result_from_cache(cache_model, viz_setting, generator_options, cache_metadata: {})
      pivot_setting = Viz::Values::PivotSetting.from_viz_setting(viz_setting)
      if pivot_setting
        return unless FeatureToggle.active?(Viz::Constants::FT_PIVOT_V2, viz_setting.tenant_id)
      else
        return unless FeatureToggle.active?(Viz::Constants::FT_TABLE_V2, viz_setting.tenant_id)
      end

      explore_settings_generator = Viz::Data::ExploreSettingGenerator.new(
        viz_setting,
        cache_model,
        render_all_fields: !!pivot_setting, # TableResultToPivotResult will build the Viz fields correctly later
      )
      explore_settings, = explore_settings_generator.explore(generator_options.except(:sort))
      result_model = explore_settings[:explore].merge(order_by: nil, limit: nil, offset: 0)

      row_limit = viz_setting.settings&.dig(:misc, :row_limit) || -1
      raw_pagination = {
        sorts: generator_options[:sort],
        page: generator_options[:page] || 1,
        page_size: generator_options[:page_size] || -1,
        col_page: 1,
        col_page_size: col_page_size_for(viz_setting),
        row_limit: row_limit,
      }
      pagination = Viz::Values::Pagination.normalize(**raw_pagination)

      result_model = analyze(result_model, viz_setting, from_aql_engine: cache_metadata[:from_aql_engine])
      values_model = Viz::Data::V3::PaginateModel.new(pivot_setting: pivot_setting).call(result_model, pagination)
      count_model = Viz::Data::V3::CountModel.new(pivot_setting: pivot_setting).call(result_model, limit: row_limit)

      sql_gen_options = values_model.build_default_options
      if FeatureToggle.active?(::Tenant::FT_NEW_TIMEZONE_CONFIG, viz_setting.tenant_id)
        sql_gen_options = sql_gen_options.merge(query_processing_timezone: ::Timezone::Helper::PGCACHE_QUERY_PROCESSING_TIMEZONE)
      end

      fields, values = Queries::Services::ExecuteSql.new(data_source: data_source_for(cache_model)).call(values_model.to_sql(sql_gen_options))
      _, count_values = Queries::Services::ExecuteSql.new(data_source: data_source_for(cache_model)).call(count_model.to_sql)
      # NOTE: to_i to remove fractional digits (.0) from pivot row counts that use _numeric_ data type
      # NOTE: assuming we don't support bigint number of rows. (Otherwise, we should cast this to string to preserve its value in javascript)
      count = count_values.first&.first&.to_i

      if !pivot_setting && viz_setting.show_aggregated?
        aggregated = Viz::Data::V3::SummarizeTable.new.call(result_model)
      end

      result = {
        fields: fields,
        values: values,
        column_types: values_model.fields.map do |field|
          # map to old viz types. see Caching::PostgresCache::SqlRunner::DataExplore#map_type
          case values_model.find_field!(field.name).result_type
          when 'datetime'
            'timestamp'
          when 'date'
            'date'
          when 'number'
            'number'
          else
            'string'
          end
        end,
        meta: {
          **raw_pagination.except(:sorts),
          sort: raw_pagination[:sorts],
          num_rows: count,
          viz_type: viz_setting.viz_type,
        },
        aggregated: aggregated,
      }
      result = ::Viz::Data::Vl::ExplorePaginateResult.new(
        fields: result[:fields],
        values: result[:values],
        meta: result[:meta],
        column_types: result[:column_types],
        aggregated: result[:aggregated],
      )
      if pivot_setting
        # TODO: this is very inefficient, should let the renderer handle the pivoting instead
        # TODO: more efficient processing for Arrow?
        result =
          if result.fields.any? { |f| f == Viz::Data::Amql::Helpers::Field::HEADER_MARKER_FIELD_NAME }
            ::Viz::Data::Amql::TableResultToPivotResult.new(
              pivot_setting: pivot_setting,
              pagination: pagination,
              include_column_total: !!viz_setting.settings&.dig(:others, :column_total),
              include_row_total: !!viz_setting.settings&.dig(:others, :row_total),
              include_sub_total: !!viz_setting.settings&.dig(:others, :sub_total),
            ).call(result)
          else
            ::Viz::Data::V3::TableResultToPivotResult.new(
              pivot_setting: pivot_setting,
              include_column_total: !!viz_setting.settings&.dig(:others, :column_total),
              include_row_total: !!viz_setting.settings&.dig(:others, :row_total),
              include_sub_total: !!viz_setting.settings&.dig(:others, :sub_total),
            ).call(result)
          end
      end
      result
    rescue *Viz::Data::PgcacheErrorHandler::PGCACHE_QUERY_ERRORS => e
      raise Viz::Data::PgcacheErrorHandler.new.better_query_error(T.cast(e, Viz::Data::PgcacheErrorHandler::PgcacheQueryError))
    end

    sig { params(cache_model: Modeling::Models::DataModel).returns(DataSource) }
    def self.data_source_for(cache_model)
      Viz::Data::Explorable.fetch_data_source(cache_model)
    end

    sig do
      params(
        cache_key: String,
        cache_model: Modeling::Model,
        viz_setting: VizSetting,
        generator_options: T.untyped,
      ).void
    end
    def self.log_pg_cache_explore_result_inputs(cache_key:, cache_model:, viz_setting:, generator_options:)
      HOtel::HSpan.current_span.add_attributes(
        {
          'h.pgcache_key' => cache_key,
          'h.pgcache_model' => cache_model.to_json,
          'h.viz_setting' => viz_setting.to_json,
          'h.generator_options' => generator_options.to_json,
        },
      )
    end

    sig { params(viz_setting: VizSetting).returns(Integer) }
    def self.col_page_size_for(viz_setting)
      if viz_setting.limit_100_pivot_columns?
        # 101 so that frontend knows that there are **more** than 100 columns
        # TODO: actually count the total number of columns and return it to frontend (along with num_rows)?
        101
      else
        -1
      end
    end

    sig do
      params(
        model: Modeling::Models::DataModel,
        explore_viz_setting: VizSetting,
        from_aql_engine: T.nilable(T::Boolean),
      ).returns(T.untyped)
    end
    def self.analyze(model, explore_viz_setting, from_aql_engine: false)
      extractor = Viz::Data::VizFieldExtractor.new(viz_setting: explore_viz_setting).call
      has_reg = T.let(false, T::Boolean)
      has_ref = T.let(false, T::Boolean)

      extractor[:analytics]&.each do |dim|
        if dim.dig(:analytic, :type) == 'reference'
          has_ref = true
        elsif dim.dig(:analytic, :type) == 'trend'
          has_reg = true
        end
      end
      if has_reg
        model = Viz::Data::V3::RegressionModel.new(
          viz_setting: explore_viz_setting,
          from_aql_engine: !!from_aql_engine,
        ).call(model)
      end
      if has_ref
        model = Viz::Data::V3::ReferenceModel.new(
          viz_setting: explore_viz_setting,
          from_aql_engine: !!from_aql_engine,
        ).call(model)
      end

      model
    end

    private

    def async_run_on_customer_database(user)
      data_source_id = explore.data_source_id

      querylike =
        # NOTE: cannot generate_payload for non-aql explores because they require resolving TopN conditions by executing separate queries
        if explore.for_aql_engine && FeatureToggle.active?(Viz::Constants::FT_NEW_JOB_EXPLORE_PAYLOAD, ThreadContext.tenant_id)
          Viz::Data::Explorable.generate_payload(
            explore,
            data_source: DataSource.find_by!(id: explore.data_source_id),
            week_start_day: viz_setting.week_start_day,
          )
        else
          explore
        end

      async_options = generate_async_options(
        user: user,
        cache_key: cache_key,
        data_source_id: data_source_id,
        running_data: {
          viz_setting: viz_setting,
          generator_options: generator_options,
          executed_aql: generator_metadata&.executed_aql,
        },
        generator_metadata: generator_metadata,
      )

      job = DataModel.async(async_options).execute_model_query(
        querylike,
        cache_key,
        data_source_id,
        {
          cache_duration: generator_options[:cache_duration],
          week_start_day: viz_setting.week_start_day,
        },
      )

      ::Viz::Data::Vl::AsyncJob.new(job_id: job.id)
    end

    def sync_run_on_pg_cache
      meta = {}

      # Re-generate parsed_query if we are using cache key v2
      if Viz::Caching::Keys.use_cache_key_v2?(explore) &&
         explore.data_source_id && (data_source = DataSource.find_by(id: explore.data_source_id))

        # NOTE: the params should match #async_run_on_customer_database
        # NOTE2: assuming aql model does not need to run extra queries to generate the SQL
        meta[:parsed_query] = Viz::Data::Explorable.generate_sql(explore, data_source: data_source, week_start_day: viz_setting.week_start_day)
      end

      Viz::Data::ExploreRunners::Model.pg_cache_explore_result(
        cache_key,
        viz_setting,
        generator_options,
        meta: meta,
      )
    end

    def sync_result(result)
      ::Viz::Data::Vl::ExplorePaginateResult.new(result.slice(:fields, :values, :meta, :column_types, :aggregated))
    end
  end
end
