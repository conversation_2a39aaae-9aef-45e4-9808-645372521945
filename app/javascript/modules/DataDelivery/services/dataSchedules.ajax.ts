import { fetchDashboard } from '@/modules/DashboardAsCode/services/dashboards.ajax';
import {
  standardGet, standardPost, standardPut, standardDelete,
} from '@/core/services/ajax';
import { DataDeliverySource, DataSchedule } from '../types';

export async function fetchSource (source: DataDeliverySource) {
  if (source.sourceType === 'Dashboard') {
    const { dashboard } = await fetchDashboard(source.data.id);

    return dashboard;
  }
  throw new Error(`Unsupported source type: ${source.sourceType}`);
}

export function fetchDataSchedules ({ sourceId, sourceType } : { sourceId: number, sourceType: 'Dashboard' }) {
  return standardGet('/api/v2/data_schedules', { source_id: sourceId, source_type: sourceType });
}

export function getDataSchedule (id: number) {
  return standardGet(`/api/v2/data_schedules/${id}`);
}

export const executeDataSchedule = ({ testDataSchedule, id }: { testDataSchedule?: DataSchedule, id?: number }) => {
  return standardPost('/api/v2/data_schedules/submit_execute', id ? { id } : { test_data_schedule: testDataSchedule });
};

export const updateDataSchedule = (dataSchedule: DataSchedule) => {
  return standardPut(`/api/v2/data_schedules/${dataSchedule.id}`, { data_schedule: dataSchedule });
};

export const fetchDataSchedule = (id: number) => {
  return standardGet(`/api/v2/data_schedules/${id}`);
};

export const createDataSchedule = (dataSchedule: DataSchedule) => {
  return standardPost('/api/v2/data_schedules', { data_schedule: dataSchedule });
};

export function executeAll (source: DataDeliverySource) {
  return standardPost('/email_schedules/send_all.json', {
    source_id: source.data.id,
    source_type: source.sourceType,
  });
}

export function destroyDataSchedule (id: number) {
  return standardDelete(`/api/v2/data_schedules/${id}`);
}
