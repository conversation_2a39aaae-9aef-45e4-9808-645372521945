<template>
  <div
    ref="inputEl"
    class="h-highlightable-input"
    :contenteditable="!disabled"
    :placeholder="placeholder"
    @input="handleInput"
    @keydown="handleKeydown"
    @paste="handlePaste"
    @focus="handleFocus"
    @blur="handleBlur"
  />
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import KEY_CODES from '@holistics/ds/constants/keyCodes';
import { isEqual } from 'lodash';
import {
  highlightText,
  highlightTextWithRegex,
  updateTextAndHighlight,
  updateTextAndHighlightWithRegex,
} from './utils/highlight';

interface Props {
  modelValue: string;
  textToHighlight?: string;
  highlightRegex?: RegExp;
  highlightClass?: string;
  disabled?: boolean;
  autofocus?: boolean;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  highlightClass: 'active',
  disabled: false,
  autofocus: false,
  placeholder: '',
});

const emit = defineEmits(['update:modelValue', 'focus', 'blur']);
const inputEl = ref<HTMLDivElement | null>(null);
const newInputText = ref('');

const updateNewTextAndHighlight = (value: string) => {
  if (!inputEl.value) {
    return;
  }

  if (!props.textToHighlight && !props.highlightRegex) {
    return;
  }

  if (props.textToHighlight) {
    updateTextAndHighlight({
      el: inputEl.value,
      textToInsert: value,
      textToHighlight: props.textToHighlight,
      highlightClass: props.highlightClass || '',
    });
  } else if (props.highlightRegex) {
    updateTextAndHighlightWithRegex({
      el: inputEl.value,
      textToInsert: value,
      highlightRegex: props.highlightRegex,
      highlightClass: props.highlightClass || '',
    });
  }
};

const highlightInputText = (restoreSelection = true) => {
  const el = inputEl.value;

  if (!el) {
    return '';
  }

  if (!props.textToHighlight && !props.highlightRegex) {
    return el.textContent || '';
  }

  if (props.textToHighlight) {
    return highlightText({
      el,
      textToHighlight: props.textToHighlight,
      highlightClass: props.highlightClass || '',
      restoreSelectionAfterHighlighting: restoreSelection,
    });
  }

  if (props.highlightRegex) {
    return highlightTextWithRegex({
      el,
      regexToHighlight: props.highlightRegex,
      highlightClass: props.highlightClass || '',
      restoreSelectionAfterHighlighting: restoreSelection,
    });
  }

  return el.textContent || '';
};

const handleInput = () => {
  const rawText = highlightInputText();
  newInputText.value = rawText;
  emit('update:modelValue', rawText);
};

const handleKeydown = (e: KeyboardEvent) => {
  switch (e.keyCode) {
    case KEY_CODES.enter:
    case KEY_CODES.esc:
      e.preventDefault(); // prevent line-break
      break;
    default:
    // do nothing
  }
};

const handlePaste = (e: ClipboardEvent) => {
  e.preventDefault();
  const text = (e.originalEvent || e).clipboardData.getData('text/plain');
  // insert text manually
  document.execCommand('insertHTML', false, text);
};

const handleFocus = (e: FocusEvent) => {
  emit('focus', e);
};

const handleBlur = (e: FocusEvent) => {
  emit('blur', e);
};

// Watchers
watch(() => props.modelValue, (val, oldVal) => {
  // Only trigger when modelValue is not the inputText we just updated from handleInput()
  if (!isEqual(val, oldVal) && val !== newInputText.value) {
    updateNewTextAndHighlight(val);
  }
});

watch(() => props.textToHighlight, () => {
  highlightInputText();
});

// Lifecycle hooks
onMounted(() => {
  if (inputEl.value) {
    inputEl.value.innerHTML = props.modelValue || '';
    highlightInputText(false);
    if (props.autofocus) {
      inputEl.value.focus();
    }
  }
});
</script>

<style lang="postcss">
.h-highlightable-input {
  @apply cursor-text truncate;
  color: var(--h-input-text-color, theme('colors.gray.800'));

  &:empty:before{
    content: attr(placeholder);
    display: block;
    color: var(--h-input-placeholder-color, theme('colors.gray.600'));
  }
  mark {
    @apply px-1 py-0 rounded bg-blue-50;
    color: var(--h-input-text-color, theme('colors.gray.800'));
  }
}
</style>
