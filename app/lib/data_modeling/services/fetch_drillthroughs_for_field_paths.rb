# typed: true

# @file-tag #visualization/drillthrough

module DataModeling::Services
  class FetchDrillthroughsForFieldPaths < T::Struct
    include Service
    include ServiceContexts::CurrentUser
    include ServiceConcerns::AbilityHelpers

    set_ability_class DashboardsAbility

    sig do
      params(field_paths: T::Array[DataModeling::Values::FieldPath])
        .returns(T::Hash[String, T::Array[Hash]])
    end
    def call(field_paths)
      # Fetch possible drillthrough destinations
      field_path_condition = field_paths.map do |fp|
        next if fp.data_set_id.blank? || fp.model_id.blank?

        <<~SQL
          (
            FS.data_set_id = #{fp.data_set_id}
            AND (FS.field_path->>'model_id')::text = '#{PG::Connection.escape(fp.model_id.to_s)}'
            AND FS.field_path->>'field_name' = '#{PG::Connection.escape(fp.field_name)}'
          )
        SQL
      end.compact.join("\nOR\n")

      return {} if field_path_condition.blank?

      sql = <<~SQL
        SELECT
          DF.id dynamic_filter_id,
          DF.dynamic_filter_holdable_id dashboard_id,
          FS.data_set_id data_set_id,
          (FS.field_path->>'model_id')::text model_id,
          FS.field_path->>'field_name' field_name
        FROM #{DynamicFilter.table_name} DF
          LEFT JOIN #{DynamicFilterDefinition.table_name} D
            ON D.id = DF.dynamic_filter_definition_id
          LEFT JOIN #{DmFieldFilterSource.table_name} FS
            ON FS.id = D.filter_source_id
              AND D.filter_source_type = 'DmFieldFilterSource'
        WHERE DF.drillthrough_enabled IS TRUE
          AND (#{field_path_condition})
          AND DF.dynamic_filter_holdable_type = 'Dashboard'
        ORDER BY DF.order, DF.id
      SQL

      res = ActiveRecord::Base.connection.select_all(sql).to_a

      dashboard_ids = res.map { |r| r['dashboard_id'] }
      dashboards = Dashboard.where(id: dashboard_ids).map { |d| [d.id, d] }.to_h

      result_mapping = {}

      Abilities::Services::AllowPublicDrillthroughService.new.call(current_ability)
      # Map into { <field_path> => [{ dashboard_id, dashboard_title, dynamic_fitler_id }] }
      res.each do |record|
        d = dashboards[record['dashboard_id']]
        next unless d && current_ability.can?(:read, d)

        field_path = Viz::Drillthroughs.generate_field_key(
          DataModeling::Values::FieldPath.new(data_set_id: record['data_set_id'], model_id: record['model_id'], field_name: record['field_name']),
        )
        result_mapping[field_path] ||= []
        result_mapping[field_path] << {
          dashboard_id: d.id,
          dashboard_title: d.title,
          dynamic_filter_id: record['dynamic_filter_id'],
        }
      end

      result_mapping
    end
  end
end
