import type { DashboardDefinition, CanvasLayout } from '@holistics/aml-std';
import { DEFAULT_THEME } from '@/modules/DynamicDashboards/utils/genAMLCode';
import type { DashboardAsCode } from '@/modules/DashboardAsCode/types';

export const DEFAULT_CANVAS_LAYOUT: CanvasLayout = {
  type: 'CanvasLayout',
  uname: 'view_1',
  label: 'View 1',
  width: 1200,
  height: 840,
  grid_size: 20,
  blocks: {},
};

export const DEFAULT_DASHBOARD_DEFINITION: DashboardDefinition = {
  uname: 'untitled',
  title: 'Untitled',
  description: '',
  blocks: [{
    type: 'TextBlock',
    uname: 'title',
    content: '# Your title goes here\n<font color="#D2D5DF">(Double click to edit)</font>',
    settings: { hide_controls: false },
  }, {
    type: 'TextBlock',
    uname: 'guide',
    content: `<div class="flex">
      <div class="flex-1 flex flex-col items-center">
        <div class="w-full">
          <h2>Tips</h2>
          <p>Add Visualization to make your dashboard more meaningful. <a href="https://docs.holistics.io/as-code/canvas-dashboard" target="_blank">Learn more</a></p>
        </div>
        <img src="https://go.holistics.io/canvas-floating-panel" width="50%" alt="Canvas Dashboard Main Panel">
      </div>
      <div class="flex-1">
        <h2>Sample Dashboards</h2>
        <p>Get inspired by what\\'s possible. Click on any of these sample dashboards to dive in and explore.</p>
        <a href="https://go.holistics.io/sample-dashboard-1" target="_blank">
          <img alt="sample dashboard 1"  src="https://go.holistics.io/sample-dashboard-1-thumb" width="65%" />
        </a>
        <a href="https://go.holistics.io/sample-dashboard-2" target="_blank">
          <img alt="sample dashboard 2" src="https://go.holistics.io/sample-dashboard-2-thumb" width="65%" />
        </a>
      </div>
    </div>`,
    settings: { hide_controls: false },
  }],
  interactions: [],
  views: [{
    ...DEFAULT_CANVAS_LAYOUT,
    blocks: {
      title: {
        layer: 0,
        position: {
          x: 20, y: 20, w: 1160, h: 120,
        },
      },
      guide: {
        layer: 0,
        position: {
          x: 20, y: 160, w: 1160, h: 660,
        },
      },
    },
  }],
  settings: { autorun: true, allow_timezone_change: false },
  theme: DEFAULT_THEME,
};

export const DEFAULT_DASHBOARD: DashboardAsCode = {
  definition: DEFAULT_DASHBOARD_DEFINITION,
  definition_aml: '',
  dynamic_filters: [],
  from_aml: false,
  id: 0,
  path: '',
  title: DEFAULT_DASHBOARD_DEFINITION.title,
  permissions: {
    can_crud: true,
    can_export: true,
    can_export_data: true,
    can_live_update: true,
    can_pin: false,
    can_read: true,
    can_share: false,
    can_update: true,
  },
  uname: '',
  version: 4,
  widgets: [],
};
