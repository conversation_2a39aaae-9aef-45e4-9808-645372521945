operationId: Ai_Answer
summary: Get Answer (so far)
tags:
  - AI
requestBody:
  content:
    application/json:
      schema:
        type: object
        properties:
          job_id:
            type: integer
        required:
          - job_id
responses:
  "200":
    description: Answer Response
    content:
      application/json:
        schema:
          $ref: ../../components/schemas/ai/Answer.yml
  "403":
    $ref: ../../components/responses/errors/PermissionDeniedError.yml
  "404":
    $ref: ../../components/responses/errors/NotFoundError.yml
