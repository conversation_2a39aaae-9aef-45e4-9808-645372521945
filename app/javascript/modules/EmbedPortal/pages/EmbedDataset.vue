<template>
  <div
    v-h-loading.body="isFetchingDataset"
    class="embed-portal-dataset flex size-full min-h-0 flex-col"
    data-ci="embed-portal-dataset"
  >
    <template v-if="dataset">
      <div class="relative">
        <HButton
          type="secondary-default"
          size="sm"
          class="absolute left-2 top-2"
          icon="sidebar-light"
          unified
          @click="emit('toggleExpand')"
        />
        <DataSetForm
          class="!pl-9"
          :data-set="dataset"
        />
      </div>
      <div class="min-h-0 flex-grow">
        <DataSetExplorer
          :data-set="dataset"
          :data-models="dataset.dataModels"
          :joins="dataset.relatedJoins"
          :source="exploreSource"
          @viz-changed="() => vizChanged = true"
        >
          <template
            v-if="canSaveVisualization"
            #save-as-viz-dataset="slotProps"
          >
            <HTooltip
              :content="slotProps.tooltipText"
              placement="top"
            >
              <HButton
                :disabled="slotProps.disabled"
                data-ci="save-embed-dataset-visualization"
                type="tertiary-highlight"
                size="sm"
                @click.prevent="slotProps.disabled ? undefined : saveVisualization(slotProps as any)"
              >
                Save As
              </HButton>
            </HTooltip>
          </template>
        </DataSetExplorer>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import {
  computed, defineAsyncComponent, onMounted, ref, watch,
} from 'vue';
import DataSet from '@/modules/DataSets/models/DataSet';
import DataSetExplorer from '@/modules/DataSets/components/DataSetExplorer.vue';
import { FetchProductionDatasetService } from '@/modules/DataSets/services/fetchDatasetService';
import { Source } from '@aml-studio/h/composables';
import DataSetForm from '@/modules/DataSets/components/DataSetForm.vue';
import { HButton, HTooltip, useModal } from '@holistics/design-system';
import { useEmbedPortalStore } from '@/modules/EmbedPortal/store/embedPortalStore';
import type { SaveVisualizationParams } from '@/modules/DashboardAsCode/composables/useDashboardConfigs';

const emit = defineEmits<{ toggleExpand: [] }>();

const route = useRoute();
const datasetId = computed(() => route.params.dataset_id as unknown as number);

const dataset = ref<DataSet>();
const isFetchingDataset = ref<boolean>(false);

const fetchDataset = async () => {
  isFetchingDataset.value = true;
  const fetchDatasetService = new FetchProductionDatasetService();
  dataset.value = await fetchDatasetService.fetch(datasetId.value) as DataSet;
  isFetchingDataset.value = false;
};

const exploreSource = computed<Source | undefined>(() => {
  if (!dataset.value) return undefined;

  return {
    id: dataset.value.id,
    type: 'DataSet',
    action: 'preview',
  };
});

const { open } = useModal();
const vizChanged = ref(false);

const embedPortalStore = useEmbedPortalStore();
const canSaveVisualization = computed(() => vizChanged.value && embedPortalStore.hasEditPermission);

function saveVisualization ({ vizSetting, generatedTitle, dataSet }: SaveVisualizationParams) {
  const modal = defineAsyncComponent(() => import('../components/EmbedSaveVisualizationModal.vue'));

  open(modal, { vizSetting, generatedTitle, dataset: dataSet });
}

watch(datasetId, () => {
  fetchDataset();
});

onMounted(() => {
  fetchDataset();
});

</script>
<style lang="postcss" scoped>
/* Hide the toggle button and data set form in embed portal to custom it in this component */
.embed-portal-dataset:deep(.data-set-explorer-fields-panel) {
  .data-set-explorer-collapsible-panel .btn-toggle,
  .data-set-form {
    @apply hidden;
  }
}
</style>
