<template>
  <div
    class="search-result-item group mx-2 cursor-pointer"
    :class="{
      'hover:bg-gray-200': !isSelected,
      'bg-gray-300': isSelected
    }"
  >
    <div
      class="relative flex w-full min-w-0 items-center px-10 py-1 text-xs font-normal"
      @click="emit('open')"
    >
      <div class="flex-shrink-0 text-xs text-gray-500">
        {{ lineNumber }}:
      </div>

      <div class="min-w-0 flex-grow truncate pl-1 pr-6">
        <!-- Show diff preview when replace text exists -->
        <template v-if="showReplace && replaceText">
          <DiffPreview
            :match="match"
            :replace-text="replaceText"
          />
        </template>

        <!-- Original view when no replace text -->
        <template v-else>
          <highlighted-text
            :text="content"
            :highlight-text="highlightTexts"
          />
        </template>
      </div>

      <div class="absolute right-2 flex items-center gap-1">
        <div
          class="p-1 opacity-0 hover:text-gray-700 group-hover:opacity-100"
          @click.stop="emit('dismiss')"
        >
          <HTooltip
            content="Dismiss"
            placement="right"
            :distance="3"
          >
            <HIcon
              name="cancel"
              size="sm"
            />
          </HTooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { HIcon, HTooltip } from '@holistics/design-system';
import HighlightedText from '@holistics/ds/components/HighlightedText/HighlightedText.vue';
import type { SearchMatch } from '@aml-studio/client/pages/search/types';
import DiffPreview from './ReplaceDiffPreview.vue';

interface Props {
  lineNumber: number;
  content: string;
  highlightTexts: string | string[];
  isSelected: boolean;
  showReplace: boolean;
  replaceText: string;
  match: SearchMatch;
}

defineProps<Props>();

const emit = defineEmits<{(e: 'open'): void;
  (e: 'dismiss'): void;
}>();
</script>

<style lang="scss" scoped>
div[class*="opacity-0"] {
  transition: opacity 0.2s ease;
  cursor: pointer;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 2px;
  }
}
</style>
