import { createInjectionState } from '@vueuse/core';
import type { BlockTemplate } from '@holistics/aml-std';
import { AmlDataset } from '@aml-studio/client';
import { FetchProductionDatasetService, IFetchDatasetService } from '@/modules/DataSets/services/fetchDatasetService';
import { FetchProductionDashboardService } from '@/modules/DashboardAsCode/services/FetchProductionDashboardService';
import type DataSet from '@/modules/DataSets/models/DataSet';
import type VizSetting from '@/modules/Viz/models/VizSetting';
import type { CreateTemplateFn, IFetchDashboardService } from '../types';

export interface SaveVisualizationParams {
  dataSet: DataSet | AmlDataset
  vizSetting: VizSetting
  generatedTitle?: string
  title?: string
  description?: string
}

export type SaveVisualizationFunc = (options: SaveVisualizationParams) =>
Promise<{ status: 'resolved' | 'dismissed' | 'rejected' | 'dismiss' | 'resolve', data: any }>

interface UseProvideDashboardConfigsOptions {
  fetchDatasetService?: IFetchDatasetService,
  fetchDashboardService?: IFetchDashboardService,
  fetchAmlBindingFunc?: (fqname: string) => Promise<unknown>,
  fetchTemplatesFunc?: () => Promise<BlockTemplate[]>,
  createTemplateFunc?: CreateTemplateFn,
  getBlockCodeFunc?: (uname: string) => Promise<string | null>,
  jumpToCodeFunc?: (path: string) => Promise<void>,
  saveVisualizationFunc?: SaveVisualizationFunc,
  extraDetails?: {
    projectId?: number,
    inDevMode?: boolean,
    inPreviewMode?: boolean
  }
}

const [useProvideDashboardConfigs, _useDashboardConfigs] = createInjectionState((options: UseProvideDashboardConfigsOptions) => {
  return {
    fetchDatasetService: options.fetchDatasetService || new FetchProductionDatasetService(),
    fetchDashboardService: options.fetchDashboardService || new FetchProductionDashboardService(),
    fetchAmlBindingFunc: options.fetchAmlBindingFunc,
    fetchTemplatesFunc: options.fetchTemplatesFunc,
    createTemplateFunc: options.createTemplateFunc,
    getBlockCodeFunc: options.getBlockCodeFunc,
    jumpToCodeFunc: options.jumpToCodeFunc,
    saveVisualizationFunc: options.saveVisualizationFunc,
    extraDetails: {
      projectId: options.extraDetails?.projectId,
      inDevMode: Boolean(options.extraDetails?.inDevMode),
      inPreviewMode: Boolean(options.extraDetails?.inPreviewMode),
    },
  };
});

function useDashboardConfigs () {
  const configStore = _useDashboardConfigs();
  return configStore || {
    fetchDatasetService: new FetchProductionDatasetService(),
    fetchDashboardService: new FetchProductionDashboardService(),
    fetchAmlBindingFunc: undefined,
    fetchTemplatesFunc: undefined,
    createTemplateFunc: undefined,
    getBlockCodeFunc: undefined,
    jumpToCodeFunc: undefined,
    saveVisualizationFunc: undefined,
    extraDetails: {
      projectId: undefined,
      inDevMode: false,
      inPreviewMode: false,
    },
  };
}

export {
  useProvideDashboardConfigs,
  useDashboardConfigs,
};
