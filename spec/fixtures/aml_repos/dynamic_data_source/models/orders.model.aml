Model orders {
  type: 'query'
  label: 'Orders'
  description: ''
  data_source_name: 'pg'
  dimension id {
    label: 'Order Id'
    type: 'number'
    hidden: false
    definition: @sql {{ #SOURCE.id }};;
  }
  dimension user_id {
    label: 'User Id'
    type: 'number'
    hidden: false
    definition: @sql {{ #SOURCE.user_id }};;
  }
  dimension product_id {
    label: 'Product Id'
    type: 'number'
    hidden: false
    definition: @sql {{ #SOURCE.product_id }};;
  }
  dimension quantity {
    label: 'Quantity'
    type: 'number'
    hidden: false
    definition: @sql {{ #SOURCE.quantity }};;
  }

  owner: '<EMAIL>'
  query: @sql
    with order(
      id,
      user_id,
      product_id,
      quantity
    ) as (
      values
        (1, 1, 1, 1),
        (2, 1, 2, 2),
        (3, 3, 3, 1),
        (4, 3, 1, 2)
    )
    select
      id,
      user_id,
      product_id,
      quantity
    from
      orders
  ;;

  models: []
}
