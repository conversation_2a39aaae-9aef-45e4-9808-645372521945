import { computed, defineAsyncComponent, type Ref } from 'vue';
import {
  type DashboardBlock, type DashboardInteraction, type FilterBlock, type FilterInteraction, type PopInteraction, type TextBlock, type VizBlock,
  SourceMapEventKind, type DashboardUpdateEvent,
} from '@holistics/aml-std';
import { cloneDeep, isEqual } from 'lodash';
import { useDashboardConfigs } from '@/modules/DashboardAsCode/composables/useDashboardConfigs';
import { useExporter } from '@/modules/Exporter/plugins/exporter';
import { submitExport } from '@/modules/DynamicDashboards/services/widgets.ajax.js';
import { useToasts } from '@/core/composables/useToasts';
import eventBus from '@/core/services/eventBus';
import { createStoreAdhocSettingsEventName } from '@/modules/Viz/utils';
import { useModal } from '@holistics/design-system';
import { trackExpandBlockWhenDrillDown, trackExploreBlockWhenDrillDown } from '@/modules/Viz/utils/drillDown/drillDownUsageTracking';
import { User } from '@/core/plugins/user';
import type { BlockPreferencesModalTabs } from '../components/modals/BlockPreferences.vue';
import { BlockActionName } from '../constants/blockActions';
import { useDashboard } from './useDashboard';
import { useDashboardDatasets } from './useDashboardDatasets';
import { useDashboardVizStates } from './useDashboardVizStates';
import { useDashboardFilters } from './useDashboardFilters';
import { textBlockModal } from '../services/textBlock.modal';
import { vizBlockModal } from '../services/vizBlock.modal';
import { filterBlockModal } from '../services/filterBlock.modal';
import { blockPreferencesModal } from '../services/preferences.modal';
import { buildBlockPermissionMap, buildVizConditionables } from '../utils/buildFilterConditionables';
import {
  findOverlappingBlockPositions, generateDefaultBlockPosition, generateDuplicatedBlockPosition,
} from '../utils/layouts';
import {
  generateBlockId,
  getBlockPreferences,
  isErrorBlock,
  isFilterBlock,
  isIcBlock,
  isIcInteraction,
  isPopBlock,
  isTextBlock,
  isVizBlock,
} from '../utils/blocks';
import { generateDuplicatedBlockUname } from '../utils/generateBlockUname';
import {
  type ActionsContext, createDeleteBlocksEvents, errorBlockActions, icBlockActions, textBlockActions, vizBlockActions,
} from '../utils/actions';
import { useLayoutActions } from './useLayoutActions';
import { useDashboardTimezone } from './useDashboardTimezone';
import { buildExportVizConditions } from '../utils/buildExportWidgetVizConditions';
import CopyBlock from '../components/modals/CopyBlock.vue';
import TemplateModal from '../components/editor/BlockTemplates/TemplateModal.vue';
import { calculateParamsValues, generateUpdateTemplateBlockEvent } from '../utils/generateTemplateBlockCode';

const CreateTemplateModal = defineAsyncComponent(() => import('../components/editor/BlockTemplates/CreateTemplateModal.vue'));

interface UseBlockActionsParams {
  editMode: Ref<boolean>
  codeModeDisabled: Ref<boolean>
  uiEditDisabled: Ref<boolean>
  dragAndDropDisabled: Ref<boolean>
  expanded?: boolean
  blockTemplate: Ref<{ name: string, params?: any[]} | undefined>
}
export function useBlockActions (block: Ref<DashboardBlock>, {
  editMode, codeModeDisabled, uiEditDisabled, dragAndDropDisabled, expanded, blockTemplate,
}: UseBlockActionsParams) {
  const layoutActions = useLayoutActions();
  const { dashboard } = useDashboard();

  const blockActions = computed(() => {
    const context: ActionsContext = {
      editMode: editMode.value,
      layoutActions: (layoutActions?.actions ?? []).map(action => {
        if (dragAndDropDisabled.value) {
          return {
            ...action,
            disabled: true,
            tooltip: 'Layout actions are disabled on blocks having code-controlled position',
          };
        }
        return action;
      }),
      codeModeDisabled: codeModeDisabled.value,
      uiEditDisabled: uiEditDisabled.value,
      isTemplatedBlock: Boolean(blockTemplate.value?.name),
      hasTemplateParams: Boolean(blockTemplate.value?.params?.length),
      expanded,
    };
    const rawBlock = block.value;
    if (isVizBlock(rawBlock)) {
      const blockId = generateBlockId(dashboard.value.id, block.value.uname);
      const widget = (dashboard.value.widgets || []).find((w) => w.id === blockId);
      return vizBlockActions(rawBlock, { ...context, permissions: widget?.permissions });
    }
    if (isIcBlock(rawBlock)) {
      return icBlockActions(rawBlock, context);
    }
    if (isTextBlock(rawBlock)) {
      return textBlockActions(rawBlock, context);
    }
    if (isErrorBlock(rawBlock)) {
      return errorBlockActions(rawBlock, context);
    }
    return [];
  });

  return {
    blockActions,
  };
}

interface UseBlockActionsHandlerParams {
  editMode: Ref<boolean>
  emitEvents: (e: DashboardUpdateEvent[]) => void
  blockTemplate: Ref<{ name: string, params?: any[] } | undefined>
}
export function useBlockActionsHandler (block: Ref<DashboardBlock>, { editMode, emitEvents, blockTemplate }: UseBlockActionsHandlerParams) {
  const {
    dashboard, uiEditOptions, currentLayout,
  } = useDashboard();
  const { open: openModal } = useModal();
  const { datasets, rawDatasets, fetchDatasets } = useDashboardDatasets();
  const {
    expandedBlockUname, explorationBlockUname, editingBlockUname, refreshViz, shouldLoadWidget,
    drillDownManager,
  } = useDashboardVizStates();
  const {
    fetchDatasetService, extraDetails, fetchAmlBindingFunc, fetchDashboardService, fetchTemplatesFunc, getBlockCodeFunc, jumpToCodeFunc,
  } = useDashboardConfigs();
  const {
    filters, filterValues, appliedFilterValues, crossFilterInteractions, appliedCrossFilters,
  } = useDashboardFilters();
  const { projectId, inDevMode } = extraDetails;
  const {
    currentTimezone,
  } = useDashboardTimezone();
  const exporter = useExporter();
  const { toast } = useToasts();
  const currentUser = new User();

  function validateVizBlockPermission (vizBlock: VizBlock) {
    const blockId = generateBlockId(dashboard.value.id, vizBlock.uname);
    const widget = (dashboard.value.widgets || []).find((w) => w.id === blockId);

    if (widget?.permissions?.can_explore === false) {
      toast.danger(`Please ask the report's owner or admin to share the underlying Dataset (id: ${vizBlock.viz.dataset_id}) with you`, { duration: 'long' });
      return false;
    }

    return true;
  }

  function validateFilterBlockPermission (filterBlock: FilterBlock) {
    const filter = (dashboard.value.dynamic_filters || []).find((f) => f.uname === filterBlock.uname);
    const filterSource = filterBlock.filter.filter_source;

    if (filterSource.source_type === 'DmFieldFilterSource' && filter?.permissions?.crud === false) {
      toast.danger(`Please ask the report's owner or admin to share the underlying Dataset (id: ${filterSource.data_set_id}) with you`, { duration: 'long' });
      return false;
    }

    return true;
  }

  function validateBlockPermission () {
    // do not validate permission in AmlStudio
    if (extraDetails.projectId) return true;

    if (isVizBlock(block.value)) return validateVizBlockPermission(block.value);
    if (isFilterBlock(block.value)) return validateFilterBlockPermission(block.value);

    return true;
  }

  function startInlineEdit () {
    editingBlockUname.value = block.value.uname;
  }

  async function updateVizBlock (payload: VizBlock) {
    await fetchDatasets([payload.viz.dataset_id as number]);
    emitEvents([{
      event: 'UpdateBlock',
      uname: block.value.uname,
      block: payload,
      datasets: datasets.value,
    }]);
  }

  function updateTextBlock (payload: TextBlock) {
    emitEvents([{
      event: 'UpdateBlock',
      uname: block.value.uname,
      block: payload,
    }]);
  }

  function endInlineEdit (payload?: DashboardBlock) {
    switch (payload?.type) {
      case 'TextBlock':
        updateTextBlock(payload);
        break;
      case 'VizBlock':
        updateVizBlock(payload);
        break;
      default:
        break;
    }
    // Remove state editing
    editingBlockUname.value = undefined;
  }

  async function editTextBlock () {
    if (block.value.type !== 'TextBlock') {
      return;
    }
    const result = await textBlockModal({
      block: block.value,
    });
    if (result.status === 'resolved' && !isEqual(block, result.data)) {
      updateTextBlock(result.data);
    }
  }

  async function editVizBlock () {
    if (block.value.type !== 'VizBlock') {
      return;
    }
    if (!validateBlockPermission()) return;

    const result = await vizBlockModal({
      inDevMode,
      projectId,
      fetchDatasetService,
      block: block.value,
      fetchAmlBindingFunc,
      timezone: currentTimezone.value,
    });
    if (result.status === 'resolved') {
      updateVizBlock(result.data);
    }
  }

  async function editIcBlock () {
    if (!isIcBlock(block.value) || !validateBlockPermission()) {
      return;
    }
    const icInteractions = (dashboard.value.definition.interactions || []).filter(i => isIcInteraction(i) && i.from === block.value.uname);
    const filterType = isPopBlock(block.value) ? 'pop' : undefined;
    const result = await filterBlockModal({
      block: block.value,
      interactions: icInteractions as (FilterInteraction | PopInteraction)[],
      vizConditionables: buildVizConditionables(dashboard.value.definition, {
        datasets: rawDatasets.value,
        currentIcBlock: block.value.uname,
        filterType,
        blockPermissionMap: buildBlockPermissionMap(dashboard.value),
      }),
      filterType,
      projectId,
      fetchDatasetService,
      fetchDashboardService,
      filters: filters.value,
      fetchAmlBindingFunc,
    });
    if (result.status === 'resolved') {
      const { block: icBlock, interactions } = result.data;
      if (isFilterBlock(icBlock)) {
        const { data_set_id: datasetId } = (icBlock.filter?.filter_source as any || {});
        if (datasetId) {
          await fetchDatasets([datasetId]);
        }
      }

      const otherInteractions = (dashboard.value.definition.interactions || [])
        .filter(i => !(isIcInteraction(i) && i.from === block.value.uname));

      // filter block
      emitEvents([{
        event: 'UpdateBlock',
        uname: icBlock.uname,
        block: icBlock,
        datasets: datasets.value,
      }, {
        event: 'UpdateInteractions',
        interactions: [...otherInteractions, ...interactions],
        datasets: datasets.value,
      }]);
    }
  }

  function editBlock () {
    switch (block.value.type) {
      case 'TextBlock':
        editTextBlock();
        break;
      case 'VizBlock':
        editVizBlock();
        break;
      case 'FilterBlock':
      case 'PopBlock':
      case 'DateDrillBlock':
        editIcBlock();
        break;
      default:
        throw new Error(`Invalid block type: ${(block.value as any).type}`);
    }
  }

  function deleteBlock () {
    if (!validateBlockPermission()) return;

    const events = createDeleteBlocksEvents([block.value.uname], dashboard.value.definition, datasets.value, currentLayout.value?.uname);
    if (events.length > 0) {
      emitEvents(events);
    }
  }

  function bringToFront () {
    if (currentLayout.value?.type !== 'CanvasLayout') {
      return; // this action is canvas-specific
    }
    const currentPosition = currentLayout.value.blocks[block.value.uname] || generateDefaultBlockPosition(currentLayout.value, block.value);

    const overlappingPositions = findOverlappingBlockPositions(currentLayout.value, block.value.uname);
    if (!overlappingPositions.length) {
      return; // no overlap => do nothing
    }
    const maxLayer = Math.max(...overlappingPositions.map(p => p.layer));
    const newLayer = maxLayer + 1;

    if (currentPosition.layer < newLayer) {
      emitEvents([{
        event: 'UpdateCanvasBlockPosition',
        uname: currentLayout.value.uname,
        blockUname: block.value.uname,
        position: {
          ...currentPosition,
          layer: newLayer,
        },
      }]);
    }
  }

  function sendToBack () {
    if (currentLayout.value?.type !== 'CanvasLayout') {
      return; // this action is canvas-specific
    }
    const currentPosition = currentLayout.value.blocks[block.value.uname] || generateDefaultBlockPosition(currentLayout.value, block.value);

    const overlappingPositions = findOverlappingBlockPositions(currentLayout.value, block.value.uname);
    if (!overlappingPositions.length) {
      return; // no overlap => do nothing
    }
    const minLayer = Math.min(...overlappingPositions.map(p => p.layer));
    const newLayer = minLayer - 1;
    if (currentPosition.layer > newLayer) {
      emitEvents([{
        event: 'UpdateCanvasBlockPosition',
        uname: currentLayout.value.uname,
        blockUname: block.value.uname,
        position: {
          ...currentPosition,
          layer: newLayer,
        },
      }]);
    }
  }

  function bringForward () {
    if (currentLayout.value?.type !== 'CanvasLayout') {
      return; // this action is canvas-specific
    }
    const currentPosition = currentLayout.value.blocks[block.value.uname] || generateDefaultBlockPosition(currentLayout.value, block.value);

    emitEvents([{
      event: 'UpdateCanvasBlockPosition',
      uname: currentLayout.value.uname,
      blockUname: block.value.uname,
      position: {
        ...currentPosition,
        layer: currentPosition.layer + 1,
      },
    }]);
  }

  function sendBackward () {
    if (currentLayout.value?.type !== 'CanvasLayout') {
      return; // this action is canvas-specific
    }
    const currentPosition = currentLayout.value.blocks[block.value.uname] || generateDefaultBlockPosition(currentLayout.value, block.value);

    emitEvents([{
      event: 'UpdateCanvasBlockPosition',
      uname: currentLayout.value.uname,
      blockUname: block.value.uname,
      position: {
        ...currentPosition,
        layer: currentPosition.layer - 1,
      },
    }]);
  }

  function expandBlock () {
    if (block.value.type === 'VizBlock') {
      expandedBlockUname.value = block.value.uname;
      eventBus.$emit(createStoreAdhocSettingsEventName(block.value.uname));
      if (drillDownManager.value.currentPosition(block.value.uname) > 0) {
        trackExpandBlockWhenDrillDown(currentUser.id, block.value.viz.viz_setting.viz_type);
      }
    }
  }

  function collapseBlock () {
    // reset shouldLoadWidget, if needed
    if (!shouldLoadWidget.value && Boolean(dashboard.value.definition.settings?.autorun)) {
      shouldLoadWidget.value = true;
    }
    eventBus.$emit(createStoreAdhocSettingsEventName(block.value.uname));
    // reset query param
    expandedBlockUname.value = undefined;
  }

  async function exploreBlock () {
    if (block.value.type === 'VizBlock' && validateBlockPermission()) {
      explorationBlockUname.value = block.value.uname;
      if (drillDownManager.value.currentPosition(block.value.uname) > 0) {
        trackExploreBlockWhenDrillDown(currentUser.id, block.value.viz.viz_setting.viz_type);
      }
    }
  }

  async function getDuplicatedBlockCode (newUname: string) {
    if (!getBlockCodeFunc) {
      return undefined;
    }
    const code = await getBlockCodeFunc(block.value.uname);
    if (!code) {
      return undefined;
    }
    return code.trim().replace(new RegExp(`block\\s+${block.value.uname}`), `block ${newUname}`);
  }

  async function duplicateBlock () {
    if (block.value.type === 'ErrorBlock') {
      // dont duplicate error block
      return;
    }
    if (!validateBlockPermission()) return;

    const uname = generateDuplicatedBlockUname(block.value.type, dashboard.value.definition.blocks.map(b => b.uname));
    const code = await getDuplicatedBlockCode(uname);

    const newBlock: DashboardBlock = cloneDeep(block.value);
    newBlock.uname = uname;

    // auto-gen interactions for the new block
    const newInteractions: DashboardInteraction[] = [];
    if (newBlock.type === 'FilterBlock' && newBlock.filter.filter_source.source_type === 'DmFieldFilterSource') {
      // clone interactions from the original block
      dashboard.value.definition.interactions.forEach(i => {
        if (i.type !== 'ErrorInteraction') {
          if (i.from === block.value.uname) {
            newInteractions.push({
              ...i,
              from: uname,
            });
          } else if (i.to === block.value.uname) {
            newInteractions.push({
              ...i,
              to: uname,
            });
          }
        }
      });
    } else if (newBlock.type === 'VizBlock') {
      // clone interactions from the original block
      dashboard.value.definition.interactions.forEach(i => {
        if (i.type !== 'ErrorInteraction') {
          if (i.from === block.value.uname) {
            newInteractions.push({
              ...i,
              from: uname,
            });
          } else if (i.to === block.value.uname) {
            newInteractions.push({
              ...i,
              to: uname,
            });
          }
        }
      });
      // add cross-filter interaction between the original and the new block
      newInteractions.push({
        type: 'CrossFilterInteraction',
        from: block.value.uname,
        to: uname,
      }, {
        type: 'CrossFilterInteraction',
        from: uname,
        to: block.value.uname,
      });
    }

    const events: DashboardUpdateEvent[] = [
      // able to get block code: use that code for the duplicated block (via `UpdateValues` event)
      // otherwise: let codegen generate it (via UpdateBlock` event)
      code ? {
        event: 'UpdateValues',
        sourceMapEvents: [{
          kind: SourceMapEventKind.Update,
          path: ['block', newBlock.uname],
          node: {
            kind: 'ElementProperty',
            astEdit: {
              text: code,
            },
          },
        }],
        jsonValues: [{
          path: `blocks.${newBlock.uname}`,
          value: newBlock,
        }],
      } : {
        event: 'UpdateBlock',
        uname: newBlock.uname,
        block: newBlock,
        datasets: datasets.value,
      }];
    if (newInteractions.length) {
      events.push({
        event: 'UpdateInteractions',
        interactions: dashboard.value.definition.interactions.concat(...newInteractions),
        datasets: datasets.value,
      });
    }

    if (currentLayout.value?.type === 'CanvasLayout') {
      // set position of the duplicated block
      const newPosition = generateDuplicatedBlockPosition(currentLayout.value, block.value);
      if (newPosition) {
        events.push({
          event: 'UpdateCanvasBlockPosition',
          uname: currentLayout.value.uname,
          blockUname: uname,
          position: newPosition,
        });
      }
    }
    emitEvents(events);
  }

  function showInCode () {
    if (typeof jumpToCodeFunc === 'function') {
      jumpToCodeFunc(block.value.uname);
    }
  }

  async function blockPreferences (initialTab: BlockPreferencesModalTabs = 'general') {
    if (block.value.type === 'ErrorBlock' || !validateBlockPermission()) {
      return;
    }
    const result = await blockPreferencesModal({
      dashboard: dashboard.value,
      block: block.value,
      initialTab,
      dashboardFilterConditions: filterValues.value,
      editMode: editMode.value,
      preferencesEditDisabled: uiEditOptions.value.preferencesEditDisabledBlocks.includes(block.value.uname),
      isTemplatedBlock: Boolean(uiEditOptions.value.templatedBlocks[block.value.uname]),
    });
    if (result.status === 'resolved') {
      const { block: updatedBlock, interactions } = result.data;
      emitEvents([{
        event: 'UpdateBlockPreferences',
        uname: updatedBlock.uname,
        preferences: getBlockPreferences(updatedBlock),
      }, {
        event: 'UpdateInteractions',
        interactions: interactions.filter((i) => i.type !== 'ErrorInteraction' && !i.disabled),
        datasets: datasets.value,
      }]);
    }
  }

  async function exportBlock (formatType: 'pdf' | 'csv' | 'xlsx', options: { applyDataFormat?: boolean } = {}) {
    const vizBlock = block.value as VizBlock;
    const blockId = generateBlockId(dashboard.value.id, vizBlock.uname);

    const widgetVizConditions = buildExportVizConditions(
      dashboard.value.id,
      [vizBlock],
      filters.value,
      appliedFilterValues.value,
      crossFilterInteractions.value,
      appliedCrossFilters.value,
    );

    exporter.startExportJob({
      sourceId: blockId,
      sourceTitle: vizBlock.label || vizBlock.uname,
      format: formatType,
      submitFunc: () => submitExport(blockId, formatType, widgetVizConditions, currentTimezone.value, options),
    });
  }

  async function editTemplateParams () {
    if (!blockTemplate.value || !fetchTemplatesFunc) {
      return;
    }
    const templates = await fetchTemplatesFunc();
    const template = templates.find(t => t.name === blockTemplate.value?.name);
    if (!template) {
      return;
    }
    const paramsValues = calculateParamsValues(template, blockTemplate.value.params);
    const { data } = await openModal(TemplateModal, {
      template,
      paramsValues,
    });
    if (data?.[0] && !isEqual(data[0], paramsValues)) {
      emitEvents(generateUpdateTemplateBlockEvent(block.value.uname, template, data[0]));
    }
  }

  async function copyBlockTo () {
    if (block.value.type !== 'VizBlock') {
      return;
    }
    if (!validateBlockPermission()) return;

    const newBlock = block.value as VizBlock;

    await fetchDatasets([newBlock.viz.dataset_id as number]);
    const vizSetting = newBlock.viz.viz_setting;

    const dataSet = datasets.value.find(d => d.id === newBlock.viz.dataset_id);
    if (!dataSet || !vizSetting) {
      return;
    }

    await openModal(CopyBlock, {
      vizSetting,
      dataSet,
      dashboardId: dashboard.value.id,
      label: newBlock.label,
      description: newBlock.description,
      inDevMode: !!extraDetails.inDevMode,
    });
  }

  async function addToLib () {
    if (block.value.type === 'ErrorBlock') {
      return;
    }
    const position = currentLayout.value?.type === 'CanvasLayout' ? currentLayout.value.blocks[block.value.uname]?.position : undefined;
    await openModal(CreateTemplateModal, {
      block: block.value,
      position,
    });
  }

  function onAction (action: BlockActionName, payload?: any) {
    switch (action) {
      case BlockActionName.Edit:
        editBlock();
        break;
      case BlockActionName.StartInlineEdit:
        startInlineEdit();
        break;
      case BlockActionName.EndInlineEdit:
        endInlineEdit(payload);
        break;
      case BlockActionName.Delete:
        deleteBlock();
        break;
      case BlockActionName.BringToFront:
        bringToFront();
        break;
      case BlockActionName.SendToBack:
        sendToBack();
        break;
      case BlockActionName.BringForward:
        bringForward();
        break;
      case BlockActionName.SendBackward:
        sendBackward();
        break;
      case BlockActionName.Expand:
        expandBlock();
        break;
      case BlockActionName.Collapse:
        collapseBlock();
        break;
      case BlockActionName.RefreshCache:
        refreshViz(block.value.uname, { bustCache: true });
        break;
      case BlockActionName.Explore:
        exploreBlock();
        break;
      case BlockActionName.Duplicate:
        duplicateBlock();
        break;
      case BlockActionName.CopyTo:
        copyBlockTo();
        break;
      case BlockActionName.ShowInCode:
        showInCode();
        break;
      case BlockActionName.Preferences:
        blockPreferences();
        break;
      case BlockActionName.DataAlerts:
        blockPreferences('data-alerts');
        break;
      case BlockActionName.ExportPdf:
        exportBlock('pdf');
        break;
      case BlockActionName.ExportCsv:
        exportBlock('csv');
        break;
      case BlockActionName.ExportCsvFormatted:
        exportBlock('csv', { applyDataFormat: true });
        break;
      case BlockActionName.ExportExcel:
        exportBlock('xlsx');
        break;
      case BlockActionName.EditTemplateParams:
        editTemplateParams();
        break;
      case BlockActionName.AddToLibrary:
        addToLib();
        break;
      default:
        console.log(action);
    }
  }

  return {
    onAction,
  };
}
