---
http_interactions:
- request:
    method: post
    uri: http://localhost:8686/chat-messages
    body:
      encoding: UTF-8
      string: '{"agent":"docsearch","query":"hi","inputs":{},"config":{},"user":"5","sync_mode":false}'
    headers:
      Authorization:
      - "<AUTH>"
      Connection:
      - close
      Content-Type:
      - application/json; charset=utf-8
      Host:
      - localhost:8686
      User-Agent:
      - http.rb/5.2.0
      Traceparent:
      - 00-c4344b02aa4087cc750d680b921a3ce0-b6ac818ec441bcd5-00
      Baggage:
      - h.tenant_id=1;,h.user_id=5;
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Thu, 29 May 2025 08:06:46 GMT
      Server:
      - uvicorn
      Content-Type:
      - application/json
      Connection:
      - close
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: |
        {"conversation_id":"4229b700f7154a12b42ad62f35b51cd6"}
        {"answer":"Hello! It looks"}
        {"answer":" like you might be looking"}
        {"answer":" for some help with Hol"}
        {"answer":"istics. Here are a"}
        {"answer":" couple of documents that could"}
        {"answer":" be relevant:\n\n1."}
        {"answer":" **Toggle sidebar using hot"}
        {"answer":"key | Holistics Docs"}
        {"answer":" (4.0)"}
        {"answer":"**\n   - You can"}
        {"answer":" easily toggle the Holistics"}
        {"answer":" sidebar by using a hot"}
        {"answer":"key:\n     - Press"}
        {"answer":" `Ctrl + \\`"}
        {"answer":" (Windows) or `"}
        {"answer":"Cmd + \\` ("}
        {"answer":"Mac) to open or"}
        {"answer":" close the sidebar.\n  "}
        {"answer":" - This feature enhances user"}
        {"answer":" experience and allows for quicker"}
        {"answer":" navigation.\n\n   More details"}
        {"answer":" can be found here:"}
        {"answer":" [Toggle sidebar using hot"}
        {"answer":"key](https://docs"}
        {"answer":".holistics.io/re"}
        {"answer":"lease-notes/202"}
        {"answer":"4-02-hotkey"}
        {"answer":"-open-panel).\n   \n  "}
        {"answer":" ![](https://community"}
        {"answer":".holistics.io/uploads"}
        {"answer":"/default/original/2"}
        {"answer":"X/5/5"}
        {"answer":"bff27e5"}
        {"answer":"ed7362b59"}
        {"answer":"caf53b8f"}
        {"answer":"732e8e206"}
        {"answer":"3f98da.gif"}
        {"answer":")\n\n2. **Access"}
        {"answer":" Demo Account | Holistics"}
        {"answer":" Docs (4.0"}
        {"answer":")**\n   - You"}
        {"answer":" can access a demo account"}
        {"answer":" to test Holistics "}
        {"answer":"4.0 features."}
        {"answer":" However, keep in mind"}
        {"answer":" that this account is reset"}
        {"answer":" every Monday at 5"}
        {"answer":" PM (UTC).\n  "}
        {"answer":" - You can access the"}
        {"answer":" demo account via this link"}
        {"answer":": [Access Demo Account"}
        {"answer":"](https://demo4"}
        {"answer":".holistics.io/demo"}
        {"answer":").\n\nIf you have a"}
        {"answer":" more specific question or need"}
        {"answer":" further assistance, feel free"}
        {"answer":" to ask!"}
        {"metadata":{"embedder_metadata":{"model":"text-embedding-3-large","usage":{"prompt_tokens":1,"total_tokens":1}},"llm_metadata":[{"model":"gpt-4o-mini-2024-07-18","index":0,"finish_reason":"stop","completion_start_time":"2025-05-29T15:06:49.924651","usage":{}}]}}
  recorded_at: Thu, 29 May 2025 08:06:53 GMT
recorded_with: VCR 6.2.0
