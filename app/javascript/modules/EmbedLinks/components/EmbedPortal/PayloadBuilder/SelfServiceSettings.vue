<template>
  <div class="flex flex-col">
    <div class="border-b border-b-gray-300 bg-gray-100 px-3 py-2 font-medium">
      Identity and Workspace settings
    </div>
    <div class="border-b border-b-gray-300 p-3">
      <div class="flex flex-col gap-3">
        <div class="text-2xs font-normal text-gray-600">
          Configure the user identity and workspace settings to determine access permissions and content visibility. <a
            :href="docUrl"
            target="_blank"
          >Learn more</a>
        </div>
        <div class="grid grid-cols-5 items-start">
          <div class="col-span-3 flex flex-col gap-1">
            <div class="required-label text-xs font-medium">
              Embed user ID
            </div>
            <div class="text-2xs font-normal text-gray-600">
              Unique identifier for the current user
            </div>
          </div>
          <div class="relative col-span-2">
            <HTooltip
              :open="!!embedUserIdError"
              :disabled="!embedUserIdError"
              :content="embedUserIdError"
              placement="right"
              disable-hoverable-content
            >
              <input
                v-model="embedUserId"
                class="h-input"
                :class="{ 'error-input': embedUserIdError }"
                placeholder="<EMAIL>"
                @input="onUpdateEmbedUserId"
              >
            </HTooltip>
          </div>
        </div>
        <div class="mt-1.5 grid grid-cols-10 items-start">
          <div class="col-span-7 flex flex-col gap-1">
            <div class="text-xs font-medium">
              Enable Personal Workspace
            </div>
            <div class="text-2xs font-normal text-gray-600">
              When enabled, users can create dashboards in their private workspace
            </div>
          </div>
          <div class="col-span-3 flex items-center justify-end">
            <HTooltip
              placement="right"
              :disabled="!tooltipContent.personalWorkspace"
              :content="tooltipContent.personalWorkspace"
              disable-hoverable-content
            >
              <HSwitch
                v-model="personalWorkspaceEnabled"
                :disabled="!embedUserId"
                size="lg"
              />
            </HTooltip>
          </div>
        </div>
        <div class="mt-1.5 grid grid-cols-5 items-start">
          <div class="col-span-3 flex flex-col gap-1">
            <div class="text-xs font-medium">
              Embed organization ID
            </div>
            <div class="text-2xs font-normal text-gray-600">
              Unique value to identify organization
            </div>
          </div>
          <div class="col-span-2">
            <HTooltip
              :open="!!embedOrgIdError"
              :disabled="!embedOrgIdError"
              :content="embedOrgIdError"
              placement="right"
              disable-hoverable-content
            >
              <input
                v-model="embedOrgId"
                class="h-input"
                :class="{ 'error-input': embedOrgIdError }"
                placeholder="acme-corp"
                :disabled="!embedUserId"
              >
            </HTooltip>
          </div>
        </div>
        <div class="mt-1.5 grid grid-cols-5 items-start">
          <div class="col-span-3 flex flex-col gap-1">
            <div class="max-w-[150px] text-xs font-medium">
              Shared workspace role
            </div>
            <div class="mr-1 text-2xs font-normal text-gray-600">
              Users' permission level within the organization's shared workspace
            </div>
          </div>

          <div class="col-span-2">
            <HTooltip
              placement="right"
              :disabled="!tooltipContent.orgWorkspaceRole"
              :content="tooltipContent.orgWorkspaceRole"
              disable-hoverable-content
            >
              <HSelect
                v-model="orgWorkspaceRole"
                :options="orgWorkspaceRoleOptions"
                :disabled="!embedUserId || !embedOrgId"
              />
            </HTooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { EmbedPortalPayload } from '@/modules/EmbedPortal/types';
import { computed } from 'vue';
import {
  HSelect, HSwitch, HTooltip,
} from '@holistics/design-system';
import { generateDocsLink } from '@aml-studio/h/utils';

const docUrl = generateDocsLink('/docs/embedded-analytics');

const embedOrgId = defineModel<EmbedPortalPayload['embed_org_id']>('embedOrgId');
const embedUserId = defineModel<EmbedPortalPayload['embed_user_id']>('embedUserId');
const permissions = defineModel<EmbedPortalPayload['permissions']>('permissions', {
  default: {
    org_workspace_role: 'no_access',
    enable_personal_workspace: false,
  },
});

const orgWorkspaceRoleOptions = [
  { label: 'No Access', value: 'no_access' },
  { label: 'Viewer', value: 'viewer' },
  { label: 'Editor', value: 'editor' },
];

type Permissions = NonNullable<EmbedPortalPayload['permissions']>;

const FORBIDDEN_CHARS = ['\\', '/', ':', '*', '?', '"', '<', '>', '|'];
function validateEmbedId (value: string, fieldName: string): string {
  if (!value) return '';

  if (value.length > 250) return `${fieldName} must be less than 250 characters`;

  if (FORBIDDEN_CHARS.some(char => value.includes(char))) {
    return `${fieldName} cannot contain the following characters: ${FORBIDDEN_CHARS.join(', ')}`;
  }

  return '';
}

const embedUserIdError = computed(() => {
  if (!embedUserId.value) return '';

  return validateEmbedId(embedUserId.value, 'Embed user ID');
});

const embedOrgIdError = computed(() => {
  if (!embedOrgId.value) return '';

  return validateEmbedId(embedOrgId.value, 'Embed organization ID');
});

function updateActionBasedPermissions<K extends keyof Permissions> (
  key: K,
  value: Permissions[K],
) {
  if (!permissions.value) {
    permissions.value = {
      org_workspace_role: 'no_access',
      enable_personal_workspace: false,
    };
  }

  permissions.value[key] = value;
}

const orgWorkspaceRole = computed<Permissions['org_workspace_role']>({
  get: () => {
    if (!embedUserId.value || !embedOrgId.value) {
      updateActionBasedPermissions('org_workspace_role', 'no_access');
      return 'no_access';
    }

    return permissions.value?.org_workspace_role || 'no_access';
  },
  set: (v: Permissions['org_workspace_role']) => updateActionBasedPermissions('org_workspace_role', v),
});

const personalWorkspaceEnabled = computed<Permissions['enable_personal_workspace']>({
  get: () => {
    if (!embedUserId.value) {
      updateActionBasedPermissions('enable_personal_workspace', false);
      return false;
    }

    return !!permissions.value?.enable_personal_workspace;
  },
  set: (v: Permissions['enable_personal_workspace']) => updateActionBasedPermissions('enable_personal_workspace', v),
});

const onUpdateEmbedUserId = () => {
  if (!embedUserId.value) {
    embedOrgId.value = undefined;
  }
};

const tooltipContent = computed(() => {
  if (!embedUserId.value) {
    return {
      personalWorkspace: 'Please provide an embed user ID to enable personal workspace',
      embedOrgId: 'Please provide an embed user ID to enable organization workspace',
      orgWorkspaceRole: 'Please provide and embed user ID and organization ID to set the organization workspace role',
    };
  }

  if (!embedOrgId.value) {
    return {
      orgWorkspaceRole: 'Please provide an organization ID to set the organization workspace role',
    };
  }

  return {
    orgWorkspaceRole: undefined,
    personalWorkspace: undefined,
  };
});

</script>
<style lang="postcss" scoped>
  .required-label::after {
    content: '*';
    @apply text-red-500;
  }

  .error-input {
    @apply border-red-500 focus:border-red-500 hover:border-red-500;
  }
</style>
