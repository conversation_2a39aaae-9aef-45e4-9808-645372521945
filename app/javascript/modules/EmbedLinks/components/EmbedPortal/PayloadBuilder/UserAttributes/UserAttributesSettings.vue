<template>
  <div>
    <div class="border-b border-b-gray-300 bg-gray-100 px-3 py-2 font-medium">
      Data permissions
    </div>
    <div class="border-b border-b-gray-300 px-3 py-2">
      <div class="text-2xs font-normal text-gray-600">
        Control user access to data with dataset's row-level permission. Edit the user attributes to restrict visible data. <a
          :href="docUrl"
          target="_blank"
        >Learn more</a>
      </div>
      <UserAttributesTable
        class="mt-2"
        :embed-portal="embedPortal"
        :user-attributes-payload="userAttributesPayload"
        @update:user-attributes-payload="newUserAttributesPayload => userAttributesPayload = newUserAttributesPayload"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { EmbedPortal, EmbedPortalPayload } from '@/modules/EmbedPortal/types';
import { isArray } from 'lodash';
import { onMounted } from 'vue';
import { generateDocsLink } from '@aml-studio/h/utils';
import UserAttributesTable from './UserAttributesTable.vue';

const docUrl = generateDocsLink('/docs/embedded-analytics');

const props = defineProps<{
  embedPortal: EmbedPortal,
}>();

const userAttributesPayload = defineModel<EmbedPortalPayload['user_attributes']>('userAttributes');

const computeInitialEmbedUserAttributePayload = () => {
  const hash: Record<string, any> = {};
  props.embedPortal.embedUserAttributes.forEach(userAttribute => {
    if (userAttribute.defaultValues) {
      if (isArray(userAttribute.defaultValues)) {
        hash[userAttribute.attributeName] = userAttribute.defaultValues;
      } else if (userAttribute.defaultValues === '__ALL__') {
        hash[userAttribute.attributeName] = '__ALL__';
      } else {
        hash[userAttribute.attributeName] = [userAttribute.defaultValues];
      }
    } else if (userAttribute.required) {
      hash[userAttribute.attributeName] = '__ALL__';
    }
  });
  return hash;
};

onMounted(async () => {
  const payload = computeInitialEmbedUserAttributePayload();
  userAttributesPayload.value = payload;
});
</script>
