<template>
  <div class="create-dataset-form w-full">
    <div class="flex gap-7">
      <div class="h-form-group basis-1/3">
        <p class="h-form-label required">
          Dataset Name
        </p>
        <input
          v-h-variablize
          :value="props.modelValue.title"
          class="h-input h-form-input ci-dataset-name"
          :class="{ 'border-red-500': shouldShowError && !validTitle }"
          data-ci="ci-dataset-name"
          placeholder="E.g: Customers Analysis, Revenue..."
          @input="(e: Event) => updateField('title', (e.target as HTMLInputElement).value)"
        >
        <template v-if="shouldShowError">
          <p
            v-if="!validTitle"
            class="h-form-invalid-feedback"
          >
            <template v-if="emptyTitle">
              Dataset Name is required
            </template>
            <template v-else-if="titleHasConsecutiveUnderscores">
              Name must not contain consecutive underscores (__)
            </template>
            <template v-else>
              Invalid name
            </template>
          </p>
        </template>
      </div>
      <div class="h-form-group min-h-0 min-w-0 max-w-[30rem] flex-1">
        <p class="h-form-label required w-36">
          Select Data Models
          <a
            v-if="!fetchingDM"
            title="Reload"
            class="btn-reload float-right mr-1 cursor-pointer text-gray-700"
            @click.prevent.stop="fetchDataModels(props.modelValue.dataSourceId)"
          >
            <h-icon name="refresh" />
          </a>
        </p>
        <template v-if="!fetchingDM && existDataModel">
          <p class="h-form-description">
            You can change this later anytime.
          </p>
          <template
            v-if="existDataModel"
          >
            <div
              class="model-select-wrapper"
              :class="shouldShowError && emptySelectedModels ? 'has-error':''"
            >
              <model-tree-select
                :models="dataModelTree"
                :selected-models="selectedModels"
                multiple-selectable
                @input="updateSelectedModelIds"
              />
            </div>
            <p
              v-if="shouldShowError && emptySelectedModels"
              class="h-form-invalid-feedback"
            >
              Please select at least one data model.
            </p>
          </template>
        </template>
        <template v-else>
          <div class="h-[266px]" />
        </template>
      </div>
      <div
        v-show="fetchingDM || fetchingRelatedModel"
      >
        <h-icon
          name="circle-notch"
          spin
        /> Loading Data Models
      </div>
      <p v-if="noModelAvailable">
        No data model available.
        <a
          class="ml-1 font-medium"
          @click.prevent="handleShowCreateDataModelScreen"
        >
          Create Data Model.
        </a>
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { map } from 'lodash';
import { standardizeDataModels, TreeNode } from '@aml-studio/client/utils/buildDataModelTree';
import { FileService } from '@aml-studio/connector/services';
import { fail } from '@aml-studio/h/services/notifier';
import { useStore } from 'vuex';
import ModelTreeSelect from '@/modules/DataSets/components/ModelTreeSelect.vue';
import { AMLDataModelNode, convertAmlDataModelsToNodes } from '@/modules/DataSets/utils/buildNodeTreeFromAmlTableModels';
import { getDefaultDataSource } from '@/modules/DataSources/utils';
import {
  computed, onMounted, ref, watch,
} from 'vue';

const props = defineProps<{
  modelValue: any;
  shouldShowError: boolean;
}>();
const emits = defineEmits(['update:modelValue', 'showCreateDataModelScreen']);
const store = useStore();

const fetchingDM = ref(false);
const fetchingRelatedModel = ref(false);
const dataModelTree = ref<AMLDataModelNode[] | null>(null);

const currentProjectId = computed(() => store.getters['aml/project/currentProjectId']);

const emptyTitle = computed(() => !props.modelValue.title || props.modelValue.title.trim().length === 0);
const titleHasConsecutiveUnderscores = computed(() => !!props.modelValue.title && props.modelValue.title.includes('__'));
const validTitle = computed(() => !emptyTitle.value && !titleHasConsecutiveUnderscores.value);

const emptyDataSource = computed(() => props.modelValue.dataSourceId == null);
const emptySelectedModels = computed(() => props.modelValue.selectedModelIds && props.modelValue.selectedModelIds.length === 0);
const existDataModel = computed(() => dataModelTree.value && dataModelTree.value.length > 0);
const noModelAvailable = computed(() => !emptyDataSource.value && !fetchingDM.value && !existDataModel.value);

const dataModelMap : Record<number|string, any> = computed(() => {
  if (!dataModelTree.value || dataModelTree.value.length <= 0) return {};

  const queue = [...dataModelTree.value];
  const result: Record<number, any> = {};

  while (queue.length > 0) {
    const node = queue.shift();
    if (node?.type === 'DataModel' && (typeof node.id === 'number')) {
      result[node.id] = node;
    }
  }

  return result;
});
const selectedModels = computed(() => {
  if (props.modelValue.dataModels == null || props.modelValue.dataModels.length === 0) {
    return [];
  }

  // Return related model that were stored in the Dataset
  return map(props.modelValue.selectedModelIds, id => {
    return dataModelMap.value[id];
  });
});

const valid = computed(() => {
  if (fetchingDM.value) {
    return false;
  }

  return validTitle.value && !emptyDataSource.value && !emptySelectedModels.value;
});

function updateField (field: string, value: string) {
  emits('update:modelValue', {
    ...props.modelValue,
    [field]: value,
  });
}

async function fetchDataModels (dataSourceId: number) {
  if (!currentProjectId.value) {
    return;
  }
  dataModelTree.value = null;
  fetchingDM.value = true;
  const { data, success } = await FileService.fetchAllModels({ projectId: currentProjectId.value, targetAmlEnv: 'aml_studio' });
  if (success && data) {
    const allDataModels = standardizeDataModels(data).filter(model => model.data_source.id === dataSourceId);
    dataModelTree.value = convertAmlDataModelsToNodes(allDataModels as unknown as TreeNode[]);
  } else {
    fail('Fail to fetch data models');
  }

  fetchingDM.value = false;
}

watch(() => currentProjectId.value, async () => {
  await fetchDataModels(props.modelValue.dataSourceId ?? getDefaultDataSource());
});

function getRelatedModels (modelIds: number[]) {
  return map(modelIds, id => {
    return dataModelMap.value[id];
  });
}

function updateSelectedModelIds (modelIds: any[]) {
  emits('update:modelValue', {
    ...props.modelValue,
    selectedModelIds: modelIds,
    dataModels: getRelatedModels(modelIds),
  });
}

function handleShowCreateDataModelScreen () {
  emits('showCreateDataModelScreen');
}

async function init () {
  dataModelTree.value = null;
  await fetchDataModels(props.modelValue.dataSourceId ?? getDefaultDataSource());
}

onMounted(async () => {
  await init();
});

defineExpose({
  valid,
});
</script>

<style lang="postcss">
.create-dataset-form {
  .root-model-select-wrapper {
    position: relative;
    z-index: 1000;

    .vue-treeselect__menu {
      overflow: auto;
    }
  }

  .model-select-wrapper {
    height: 246px;
  }

  .required:after {
    content:" *";
    @apply text-red-600;
  }
}
</style>
